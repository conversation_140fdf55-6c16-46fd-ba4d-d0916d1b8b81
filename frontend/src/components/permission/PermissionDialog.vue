<template>
  <el-dialog
    :title="title"
    v-model="visible"
    width="600px"
    append-to-body
    @close="handleClose"
  >
    <div class="permission-dialog">
      <!-- 当前数据信息 -->
      <div class="data-info" v-if="false">
        <h4>选中的数据 ({{ selectedData.length }} 条)</h4>
        <div class="data-list">
          <el-tag
            v-for="item in selectedData.slice(0, 5)"
            :key="item.id"
            type="info"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ item.name || item.title || `ID: ${item.id}` }}
          </el-tag>
          <span v-if="selectedData.length > 5" class="more-count">
            等 {{ selectedData.length }} 条数据
          </span>
        </div>
      </div>

      <!-- 当前权限显示 -->
      <div class="current-permissions" v-if="false">
        <h4>当前权限</h4>
        <div class="permission-tags">
          <el-tag
            v-for="role in currentPermissions"
            :key="role.id"
            type="success"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ role.roleName }}
          </el-tag>
        </div>
      </div>

      <!-- 角色选择 -->
      <div class="role-selection">
        <h4>设置权限角色</h4>
        <el-checkbox-group v-model="selectedRoleIds">
          <el-checkbox
            v-for="role in availableRoles"
            :key="role.id"
            :label="role.id"
            style="display: block; margin-bottom: 8px;"
          >
            {{ role.roleName }}
            <span class="role-alias" v-if="role.roleAlias">
              ({{ role.roleAlias }})
            </span>
          </el-checkbox>
        </el-checkbox-group>
        
        <div class="role-tips" style="margin-top: 16px;">
          <el-alert
            title="权限说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <ul style="margin: 0; padding-left: 20px;">
                <li>不选择任何角色表示所有用户都可以查看</li>
                <li>选择角色后，只有拥有对应角色的用户才能查看</li>
                <li>管理员角色不受权限控制限制</li>
              </ul>
            </template>
          </el-alert>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="saving" @click="handleSave">
          确 定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {getQuestionBankRoles as getRoleList} from '@/api/system/role'

export default {
  name: 'PermissionDialog',
  props: {
    // 是否显示弹窗
    modelValue: {
      type: Boolean,
      default: false
    },
    // 弹窗标题
    title: {
      type: String,
      default: '设置权限'
    },
    // 选中的数据
    selectedData: {
      type: Array,
      default: () => []
    },
    // 当前权限（角色列表）
    currentPermissions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue', 'save'],
  data() {
    return {
      visible: false,
      saving: false,
      availableRoles: [],
      selectedRoleIds: []
    }
  },
  watch: {
    modelValue: {
      handler(val) {
        this.visible = val
        if (val) {
          this.loadRoles()
          this.initSelectedRoles()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 加载可用角色列表
    async loadRoles() {
      try {
        const response = await getRoleList(1, 1000, {})
        this.availableRoles = response.data.data || []
      } catch (error) {
        console.error('加载角色列表失败:', error)
        this.$message.error('加载角色列表失败')
      }
    },

    // 初始化已选择的角色
    initSelectedRoles() {
      this.selectedRoleIds = this.currentPermissions.map(role => role.id)
    },

    // 关闭弹窗
    handleClose() {
      this.visible = false
      this.$emit('update:modelValue', false)
      this.selectedRoleIds = []
    },

    // 保存权限设置
    async handleSave() {
      this.saving = true
      try {
        const ids = this.selectedData.map(item => item.id)
        await this.$emit('save', {
          ids,
          roleIds: this.selectedRoleIds
        })
        this.handleClose()
        this.$message.success('权限设置成功')
      } catch (error) {
        console.error('权限设置失败:', error)
        this.$message.error('权限设置失败')
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style scoped>
.permission-dialog {
  padding: 0;
}

.data-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.data-info h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.data-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.more-count {
  color: #909399;
  font-size: 12px;
}

.current-permissions {
  margin-bottom: 24px;
}

.current-permissions h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.permission-tags {
  display: flex;
  flex-wrap: wrap;
}

.role-selection h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.role-alias {
  color: #909399;
  font-size: 12px;
}

.role-tips {
  margin-top: 16px;
}

.role-tips ul {
  margin: 0;
  padding-left: 20px;
  font-size: 12px;
  color: #606266;
}

.role-tips li {
  margin-bottom: 4px;
}
</style>
