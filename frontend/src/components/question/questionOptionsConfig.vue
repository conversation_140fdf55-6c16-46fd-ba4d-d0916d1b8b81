<template>
  <div class="question-options-config">
    <div class="config-header">
      <span class="config-title">选项配置</span>
      <el-switch
        v-model="visualMode"
        active-text="可视化"
        inactive-text="JSON"
        @change="onModeChange"
      />
    </div>

    <!-- 可视化配置模式 -->
    <div v-if="visualMode" class="visual-config">
      <!-- 单选题配置 -->
      <div v-if="isSingleChoice" class="single-choice-config">
        <div class="option-item" v-for="(option, index) in singleChoiceOptions" :key="index">
          <span class="option-label">{{ getOptionLabel(index) }}.</span>
          <el-input
            v-model="singleChoiceOptions[index].content"
            :placeholder="`请输入选项${getOptionLabel(index)}内容`"
            @input="updateOptions"
          />
          <el-button
            v-if="singleChoiceOptions.length > 2"
            type="danger"
            icon="el-icon-delete"
            size="small"
            @click="removeSingleOption(index)"
          />
        </div>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="small"
          @click="addSingleOption"
          :disabled="singleChoiceOptions.length >= 8"
        >
          添加选项
        </el-button>
      </div>

      <!-- 多选题配置 -->
      <div v-if="isMultipleChoice" class="multiple-choice-config">
        <div class="option-item" v-for="(option, index) in multipleChoiceOptions" :key="index">
          <span class="option-label">{{ getOptionLabel(index) }}.</span>
          <el-input
            v-model="multipleChoiceOptions[index].content"
            :placeholder="`请输入选项${getOptionLabel(index)}内容`"
            @input="updateOptions"
          />
          <el-button
            v-if="multipleChoiceOptions.length > 2"
            type="danger"
            icon="el-icon-delete"
            size="small"
            @click="removeOption(index)"
          />
        </div>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="small"
          @click="addOption"
          :disabled="multipleChoiceOptions.length >= 8"
        >
          添加选项
        </el-button>
      </div>

      <!-- 判断题配置 -->
      <div v-if="isJudgeChoice" class="judge-config">
        <div class="option-item">
          <span class="option-label">A.</span>
          <el-input v-model="judgeOptions.A" placeholder="正确选项描述" @input="updateOptions" />
        </div>
        <div class="option-item">
          <span class="option-label">B.</span>
          <el-input v-model="judgeOptions.B" placeholder="错误选项描述" @input="updateOptions" />
        </div>
      </div>
    </div>

    <!-- JSON编辑模式 -->
    <div v-else class="json-config">
      <el-input
        type="textarea"
        :rows="5"
        v-model="jsonValue"
        placeholder="请输入选项(JSON格式)"
        @input="onJsonChange"
      />
    </div>

    <!-- JSON预览 -->
    <div class="json-preview">
      <div class="preview-header">
        <span>JSON预览</span>
      </div>
      <pre class="json-content">{{ formattedJson }}</pre>
    </div>
  </div>
</template>

<script>
export default {
  name: 'questionOptionsConfig',
  props: {
    questionType: {
      type: String,
      default: '1'
    },
    modelValue: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue', 'options-change'],
  data() {
    return {
      visualMode: true,
      jsonValue: '',
      singleChoiceOptions: [
        { content: '' },
        { content: '' },
        { content: '' },
        { content: '' }
      ],
      multipleChoiceOptions: [
        { content: '' },
        { content: '' }
      ],
      judgeOptions: {
        A: '正确',
        B: '错误'
      }
    }
  },
  computed: {
    formattedJson() {
      try {
        const options = this.getCurrentOptions();
        return JSON.stringify(options, null, 2);
      } catch (e) {
        return '无效的JSON格式';
      }
    },

    // 判断是否为单选题
    isSingleChoice() {
      return this.questionType === '1' || this.questionType === 'single' ||
             this.questionType === '单选' || this.questionType === '单选题';
    },

    // 判断是否为多选题
    isMultipleChoice() {
      return this.questionType === '2' || this.questionType === 'multiple' ||
             this.questionType === '多选' || this.questionType === '多选题';
    },

    // 判断是否为判断题
    isJudgeChoice() {
      return this.questionType === '3' || this.questionType === 'judge' ||
             this.questionType === '判断' || this.questionType === '判断题';
    }
  },
  watch: {
    questionType: {
      handler(newType) {
        this.resetOptions();
        this.updateOptions();
      },
      immediate: true
    },
    modelValue: {
      handler(newValue) {
        this.parseJsonValue(newValue);
      },
      immediate: true
    }
  },
  methods: {
    onModeChange() {
      if (this.visualMode) {
        // 切换到可视化模式，解析JSON
        this.parseJsonValue(this.jsonValue);
      } else {
        // 切换到JSON模式，更新JSON值
        this.jsonValue = this.modelValue;
      }
    },

    parseJsonValue(jsonStr) {
      if (!jsonStr) return;

      try {
        const options = JSON.parse(jsonStr);

        if (this.isSingleChoice) {
          // 单选题
          const optionKeys = Object.keys(options);
          this.singleChoiceOptions = optionKeys.map(key => ({
            content: options[key]
          }));
        } else if (this.isMultipleChoice) {
          // 多选题
          const optionKeys = Object.keys(options);
          this.multipleChoiceOptions = optionKeys.map(key => ({
            content: options[key]
          }));
        } else if (this.isJudgeChoice) {
          // 判断题
          this.judgeOptions = {
            A: options.A || '正确',
            B: options.B || '错误'
          };
        }
      } catch (e) {
        console.warn('解析选项JSON失败:', e);
      }
    },

    getCurrentOptions() {
      if (this.isSingleChoice) {
        const options = {};
        this.singleChoiceOptions.forEach((option, index) => {
            options[this.getOptionLabel(index)] = option.content;
          // if (option.content.trim()) {
          // }
        });
        return options;
      } else if (this.isMultipleChoice) {
        const options = {};
        this.multipleChoiceOptions.forEach((option, index) => {
            options[this.getOptionLabel(index)] = option.content;
          //   if (option.content.trim()) {
          // }
        });
        return options;
      } else if (this.isJudgeChoice) {
        return this.judgeOptions;
      }
      return {};
    },

    updateOptions() {
      const options = this.getCurrentOptions();
      const jsonStr = JSON.stringify(options);
      this.jsonValue = jsonStr;
      this.$emit('update:modelValue', jsonStr);
      this.$emit('options-change', options);
    },

    onJsonChange() {
      this.$emit('update:modelValue', this.jsonValue);
      if (this.visualMode) {
        this.parseJsonValue(this.jsonValue);
      }
    },

    resetOptions() {
      if (this.isSingleChoice) {
        this.singleChoiceOptions = [
          { content: '' },
          { content: '' },
          { content: '' },
          { content: '' }
        ];
      } else if (this.isMultipleChoice) {
        this.multipleChoiceOptions = [{ content: '' }, { content: '' }];
      } else if (this.isJudgeChoice) {
        this.judgeOptions = { A: '正确', B: '错误' };
      }
    },

    getOptionLabel(index) {
      return String.fromCharCode(65 + index); // A, B, C, D...
    },

    addOption() {
      if (this.multipleChoiceOptions.length < 8) {
        this.multipleChoiceOptions.push({ content: '' });
      }
    },

    removeOption(index) {
      if (this.multipleChoiceOptions.length > 2) {
        this.multipleChoiceOptions.splice(index, 1);
        this.updateOptions();
      }
    },

    addSingleOption() {
      if (this.singleChoiceOptions.length < 8) {
        this.singleChoiceOptions.push({ content: '' });
        this.updateOptions();
      }
    },

    removeSingleOption(index) {
      if (this.singleChoiceOptions.length > 2) {
        this.singleChoiceOptions.splice(index, 1);
        this.updateOptions();
      }
    }
  }
}
</script>

<style scoped>
.question-options-config {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.config-title {
  font-weight: 600;
  color: #303133;
}

.visual-config {
  margin-bottom: 16px;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.option-label {
  min-width: 24px;
  font-weight: 500;
  color: #606266;
}

.json-preview {
  margin-top: 16px;
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.preview-header {
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}

.json-content {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  color: #606266;
  max-height: 200px;
  overflow-y: auto;
  margin: 0;
}

.json-config {
  margin-bottom: 16px;
}

.single-choice-config,
.multiple-choice-config,
.judge-config {
  margin-bottom: 16px;
}
</style>
