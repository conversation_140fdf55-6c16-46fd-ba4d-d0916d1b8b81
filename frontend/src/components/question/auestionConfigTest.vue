<template>
  <div class="question-config-test">
    <h2>题目配置测试页面</h2>
    
    <el-form :model="testForm" label-width="100px">
      <el-form-item label="题型">
        <el-select v-model="testForm.type" placeholder="请选择题型">
          <el-option label="单选题" value="1"></el-option>
          <el-option label="多选题" value="2"></el-option>
          <el-option label="判断题" value="3"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="选项配置" v-if="testForm.type">
        <QuestionOptionsConfig
          :question-type="testForm.type"
          v-model="testForm.options"
          @options-change="onOptionsChange"
        />
      </el-form-item>
      
      <el-form-item label="答案配置" v-if="testForm.type">
        <QuestionAnswerConfig
          :question-type="testForm.type"
          :options="currentOptions"
          v-model="testForm.answer"
        />
      </el-form-item>
      
      <el-form-item label="表单数据">
        <pre>{{ JSON.stringify(testForm, null, 2) }}</pre>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import QuestionOptionsConfig from './QuestionOptionsConfig.vue';
import QuestionAnswerConfig from './QuestionAnswerConfig.vue';

export default {
  name: 'QuestionConfigTest',
  components: {
    QuestionOptionsConfig,
    QuestionAnswerConfig
  },
  data() {
    return {
      testForm: {
        type: '',
        options: '',
        answer: ''
      },
      currentOptions: {}
    }
  },
  methods: {
    onOptionsChange(options) {
      this.currentOptions = options;
    }
  }
}
</script>

<style scoped>
.question-config-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

pre {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  color: #606266;
  max-height: 300px;
  overflow-y: auto;
}
</style>
