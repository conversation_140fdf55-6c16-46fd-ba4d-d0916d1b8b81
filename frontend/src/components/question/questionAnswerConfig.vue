<template>
  <div class="question-answer-config">
    <div class="config-header">
      <span class="config-title">答案配置</span>
      <el-switch
        v-model="visualMode"
        active-text="可视化"
        inactive-text="文本"
        @change="onModeChange"
      />
    </div>

    <!-- 可视化配置模式 -->
    <div v-if="visualMode" class="visual-config">
      <!-- 单选题答案 -->
      <div v-if="isSingleChoice" class="single-choice-answer">
        <el-radio-group v-model="singleAnswer" @change="updateAnswer">
          <el-radio
            v-for="(content, key) in options"
            :key="key"
            :label="key"
            :disabled="!content || !content.trim()"
          >
            {{ key }}. {{ content || '(未填写选项内容)' }}
          </el-radio>
        </el-radio-group>
      </div>

      <!-- 多选题答案 -->
      <div v-if="isMultipleChoice" class="multiple-choice-answer">
        <el-checkbox-group v-model="multipleAnswer" @change="updateAnswer">
          <el-checkbox
            v-for="(content, key) in options"
            :key="key"
            :label="key"
            :disabled="!content || !content.trim()"
          >
            {{ key }}. {{ content || '(未填写选项内容)' }}
          </el-checkbox>
        </el-checkbox-group>
      </div>

      <!-- 判断题答案 -->
      <div v-if="isJudgeChoice" class="judge-answer">
        <el-radio-group v-model="judgeAnswer" @change="updateAnswer">
          <el-radio label="A">A. {{ options.A || '正确' }}</el-radio>
          <el-radio label="B">B. {{ options.B || '错误' }}</el-radio>
        </el-radio-group>
      </div>

      <!-- 无选项提示 -->
      <div v-if="!hasValidOptions" class="no-options-tip">
        <el-alert
          title="请先配置选项内容"
          type="warning"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <!-- 文本编辑模式 -->
    <div v-else class="text-config">
      <el-input
        type="textarea"
        :rows="3"
        v-model="textAnswer"
        placeholder="请输入答案"
        @input="onTextChange"
      />
    </div>

    <!-- 答案预览 -->
    <div class="answer-preview">
      <div class="preview-header">
        <span>答案预览</span>
      </div>
      <div class="answer-content">
        <el-tag v-if="modelValue" type="success">{{ modelValue }}</el-tag>
        <span v-else class="no-answer">未设置答案</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'questionAnswerConfig',
  props: {
    questionType: {
      type: String,
      default: '1'
    },
    options: {
      type: Object,
      default: () => ({})
    },
    modelValue: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue'],
  data() {
    return {
      visualMode: true,
      textAnswer: '',
      singleAnswer: '',
      multipleAnswer: [],
      judgeAnswer: ''
    }
  },
  computed: {
    hasValidOptions() {
      const optionKeys = Object.keys(this.options);
      return optionKeys.some(key => this.options[key] && this.options[key].trim());
    },

    // 判断是否为单选题
    isSingleChoice() {
      return this.questionType === '1' || this.questionType === 'single' ||
             this.questionType === '单选' || this.questionType === '单选题';
    },

    // 判断是否为多选题
    isMultipleChoice() {
      return this.questionType === '2' || this.questionType === 'multiple' ||
             this.questionType === '多选' || this.questionType === '多选题';
    },

    // 判断是否为判断题
    isJudgeChoice() {
      return this.questionType === '3' || this.questionType === 'judge' ||
             this.questionType === '判断' || this.questionType === '判断题';
    }
  },
  watch: {
    questionType: {
      handler() {
        this.resetAnswer();
      },
      immediate: true
    },
    modelValue: {
      handler(newValue) {
        this.parseAnswerValue(newValue);
      },
      immediate: true
    },
    options: {
      handler() {
        // 当选项变化时，检查当前答案是否仍然有效
        this.validateCurrentAnswer();
      },
      deep: true
    }
  },
  methods: {
    onModeChange() {
      if (this.visualMode) {
        // 切换到可视化模式，解析文本答案
        this.parseAnswerValue(this.textAnswer);
      } else {
        // 切换到文本模式，更新文本值
        this.textAnswer = this.modelValue;
      }
    },

    parseAnswerValue(answerStr) {
      if (!answerStr) return;

      if (this.isSingleChoice || this.isJudgeChoice) {
        // 单选题或判断题
        this.singleAnswer = answerStr;
        this.judgeAnswer = answerStr;
      } else if (this.isMultipleChoice) {
        // 多选题
        this.multipleAnswer = answerStr.split(',').filter(item => item.trim());
      }

      this.textAnswer = answerStr;
    },

    updateAnswer() {
      let answer = '';

      if (this.isSingleChoice) {
        answer = this.singleAnswer;
      } else if (this.isMultipleChoice) {
        answer = this.multipleAnswer.join(',');
      } else if (this.isJudgeChoice) {
        answer = this.judgeAnswer;
      }

      this.textAnswer = answer;
      this.$emit('update:modelValue', answer);
    },

    onTextChange() {
      this.$emit('update:modelValue', this.textAnswer);
      if (this.visualMode) {
        this.parseAnswerValue(this.textAnswer);
      }
    },

    resetAnswer() {
      this.singleAnswer = '';
      this.multipleAnswer = [];
      this.judgeAnswer = '';
      this.textAnswer = '';
    },

    validateCurrentAnswer() {
      // 检查当前答案选项是否仍然存在
      const optionKeys = Object.keys(this.options);

      if (this.isSingleChoice || this.isJudgeChoice) {
        if (this.singleAnswer && !optionKeys.includes(this.singleAnswer)) {
          this.singleAnswer = '';
          this.judgeAnswer = '';
          this.updateAnswer();
        }
      } else if (this.isMultipleChoice) {
        const validAnswers = this.multipleAnswer.filter(answer =>
          optionKeys.includes(answer)
        );
        if (validAnswers.length !== this.multipleAnswer.length) {
          this.multipleAnswer = validAnswers;
          this.updateAnswer();
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.question-answer-config {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.config-title {
  font-weight: 600;
  color: #303133;
}

.visual-config {
  margin-bottom: 16px;
}

.single-choice-answer,
.multiple-choice-answer,
.judge-answer {
  margin-bottom: 16px;
}

.no-options-tip {
  margin-bottom: 16px;
}

.text-config {
  margin-bottom: 16px;
}

.answer-preview {
  margin-top: 16px;
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.preview-header {
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}

.answer-content {
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 32px;
  display: flex;
  align-items: center;
}

.no-answer {
  color: #c0c4cc;
  font-style: italic;
}


</style>
