<template>
    <basic-container>
        <div class="paper-wizard">
            <el-steps :active="currentStep" finish-status="success" align-center>
                <el-step title="基本信息" description="填写试卷基本信息"></el-step>
                <el-step title="配置题目" description="选择题目并设置分值"></el-step>
                <el-step title="完成创建" description="保存草稿或发布试卷"></el-step>
            </el-steps>

            <!-- 步骤1：基本信息 -->
            <div v-show="currentStep === 0" class="step-content">
                <el-card header="试卷基本信息">
                    <el-form :model="paperForm" :rules="paperRules" ref="paperForm" label-width="120px">
                        <el-form-item label="试卷名称" prop="name">
                            <el-input v-model="paperForm.name" placeholder="请输入试卷名称"/>
                        </el-form-item>
                        <el-form-item label="试卷描述" prop="description">
                            <el-input type="textarea" :rows="4" v-model="paperForm.description" placeholder="请输入试卷描述"/>
                        </el-form-item>
                        <el-form-item label="考试时长(分钟)" prop="durationMinutes">
                            <el-input-number v-model="paperForm.durationMinutes" :min="1" :max="300" placeholder="请输入考试时长"/>
                        </el-form-item>
                        <el-form-item label="及格分数" prop="passScore">
                            <el-input-number v-model="paperForm.passScore" :min="0" :max="100" placeholder="请输入及格分数"/>
                        </el-form-item>
                        <el-form-item label="是否打乱题目顺序">
                            <el-switch v-model="paperForm.shuffle"/>
                        </el-form-item>
                        <el-form-item label="是否允许暂停">
                            <el-switch v-model="paperForm.allowPause"/>
                        </el-form-item>
                        <el-form-item label="是否显示结果详情">
                            <el-switch v-model="paperForm.showResultDetail"/>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>

            <!-- 步骤2：配置题目 -->
            <div v-show="currentStep === 1" class="step-content">
                <el-card header="配置题目">
                    <div class="question-config">
                        <el-button type="primary" @click="showQuestionDialog = true">添加题目</el-button>
                        <el-table :data="selectedQuestions" style="margin-top: 20px;">
                            <el-table-column prop="questionContent" label="题目内容" width="300"/>
                            <el-table-column prop="questionType" label="题型" width="100"/>
                            <el-table-column prop="subjectName" label="科目" width="120"/>
                            <el-table-column prop="score" label="分值" width="100">
                                <template #default="{row, $index}">
                                    <el-input-number v-model="row.score" :min="1" :max="100" size="small"/>
                                </template>
                            </el-table-column>
                            <el-table-column prop="orderNo" label="排序" width="100">
                                <template #default="{row, $index}">
                                    <el-input-number v-model="row.orderNo" :min="1" size="small"/>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="100">
                                <template #default="{row, $index}">
                                    <el-button type="danger" size="small" @click="removeQuestion($index)">删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div style="margin-top: 20px;">
                            <span>总分：{{ totalScore }}</span>
                        </div>
                    </div>
                </el-card>
            </div>

            <!-- 步骤3：完成创建 -->
            <div v-show="currentStep === 2" class="step-content">
                <el-card header="完成创建">
                    <div class="summary">
                        <h3>试卷信息确认</h3>
                        <p><strong>试卷名称：</strong>{{ paperForm.name }}</p>
                        <p><strong>考试时长：</strong>{{ paperForm.durationMinutes }}分钟</p>
                        <p><strong>及格分数：</strong>{{ paperForm.passScore }}分</p>
                        <p><strong>题目数量：</strong>{{ selectedQuestions.length }}题</p>
                        <p><strong>总分：</strong>{{ totalScore }}分</p>
                    </div>
                </el-card>
            </div>

            <!-- 操作按钮 -->
            <div class="wizard-actions">
                <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
                <el-button v-if="currentStep < 2" type="primary" @click="nextStep">下一步</el-button>
                <el-button v-if="currentStep === 2" type="success" @click="saveDraft">保存草稿</el-button>
                <el-button v-if="currentStep === 2" type="primary" @click="publishPaper">发布试卷</el-button>
            </div>

            <!-- 题目选择对话框 -->
            <el-dialog title="选择题目" v-model="showQuestionDialog" width="80%">
                <div class="question-selector">
                    <!-- 这里可以添加题目选择的功能，暂时简化 -->
                    <p>题目选择功能待完善...</p>
                </div>
                <template #footer>
                    <el-button @click="showQuestionDialog = false">取消</el-button>
                    <el-button type="primary" @click="confirmSelectQuestions">确定</el-button>
                </template>
            </el-dialog>
        </div>
    </basic-container>
</template>

<script>
import {createDraft, publishPaper} from "@/api/papers/papers";

export default {
    data() {
        return {
            currentStep: 0,
            showQuestionDialog: false,
            paperForm: {
                name: '',
                description: '',
                durationMinutes: 60,
                passScore: 60,
                shuffle: false,
                allowPause: true,
                showResultDetail: true
            },
            paperRules: {
                name: [
                    {required: true, message: '请输入试卷名称', trigger: 'blur'}
                ],
                durationMinutes: [
                    {required: true, message: '请输入考试时长', trigger: 'blur'}
                ],
                passScore: [
                    {required: true, message: '请输入及格分数', trigger: 'blur'}
                ]
            },
            selectedQuestions: [],
            createdPaperId: null
        }
    },
    computed: {
        totalScore() {
            return this.selectedQuestions.reduce((sum, item) => sum + (item.score || 0), 0);
        }
    },
    methods: {
        nextStep() {
            if (this.currentStep === 0) {
                this.$refs.paperForm.validate((valid) => {
                    if (valid) {
                        this.currentStep++;
                    }
                });
            } else if (this.currentStep === 1) {
                if (this.selectedQuestions.length === 0) {
                    this.$message.warning('请至少添加一道题目');
                    return;
                }
                this.paperForm.totalScore = this.totalScore;
                this.currentStep++;
            }
        },
        prevStep() {
            this.currentStep--;
        },
        removeQuestion(index) {
            this.selectedQuestions.splice(index, 1);
        },
        confirmSelectQuestions() {
            // 这里应该从题目选择对话框中获取选中的题目
            // 暂时添加一个示例题目
            this.selectedQuestions.push({
                questionId: Date.now(),
                questionContent: '示例题目',
                questionType: 'single',
                subjectName: '示例科目',
                score: 10,
                orderNo: this.selectedQuestions.length + 1
            });
            this.showQuestionDialog = false;
        },
        saveDraft() {
            createDraft(this.paperForm).then(res => {
                this.createdPaperId = res.data.data.id;
                this.$message.success('草稿保存成功');
                this.$router.push('/papers');
            });
        },
        publishPaper() {
            createDraft(this.paperForm).then(res => {
                this.createdPaperId = res.data.data.id;
                return publishPaper(this.createdPaperId);
            }).then(() => {
                this.$message.success('试卷发布成功');
                this.$router.push('/papers');
            });
        }
    }
};
</script>

<style scoped>
.paper-wizard {
    padding: 20px;
}

.step-content {
    margin: 40px 0;
    min-height: 400px;
}

.wizard-actions {
    text-align: center;
    margin-top: 40px;
}

.wizard-actions .el-button {
    margin: 0 10px;
}

.summary {
    padding: 20px;
}

.summary h3 {
    margin-bottom: 20px;
    color: #409EFF;
}

.summary p {
    margin: 10px 0;
    font-size: 16px;
}

.question-config {
    padding: 20px;
}

.question-selector {
    min-height: 300px;
    padding: 20px;
    text-align: center;
}
</style>
