<template>
    <basic-container>
        <div class="paper-questions">
            <el-card :header="`配置试卷题目 - ${paperInfo.name}`">
                <div class="toolbar">
                    <el-button type="primary" @click="showAddDialog = true">添加题目</el-button>
                    <el-button type="success" @click="savePaperItems">保存配置</el-button>
                    <el-button @click="goBack">返回</el-button>
                </div>

                <el-table :data="paperItems" style="margin-top: 20px;" v-loading="loading">
                    <el-table-column type="index" label="#" width="50"/>
                    <el-table-column prop="questionContent" label="题目内容" min-width="300"/>
                    <el-table-column prop="questionType" label="题型" width="100">
                        <template #default="{row}">
                            <el-tag v-if="row.questionType === 'single'">单选题</el-tag>
                            <el-tag v-else-if="row.questionType === 'multiple'" type="success">多选题</el-tag>
                            <el-tag v-else-if="row.questionType === 'judge'" type="warning">判断题</el-tag>
                            <el-tag v-else type="info">{{ row.questionType }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="subjectName" label="科目" width="120"/>
                    <el-table-column prop="score" label="分值" width="120">
                        <template #default="{row}">
                            <el-input-number v-model="row.score" :min="1" :max="100" size="small" @change="calculateTotal"/>
                        </template>
                    </el-table-column>
                    <el-table-column prop="orderNo" label="排序" width="100">
                        <template #default="{row}">
                            <el-input-number v-model="row.orderNo" :min="1" size="small"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100">
                        <template #default="{row, $index}">
                            <el-button type="danger" size="small" @click="removeItem($index)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <div class="summary" style="margin-top: 20px;">
                    <el-row>
                        <el-col :span="12">
                            <span>题目总数：{{ paperItems.length }}题</span>
                        </el-col>
                        <el-col :span="12" style="text-align: right;">
                            <span>总分：{{ totalScore }}分</span>
                        </el-col>
                    </el-row>
                </div>
            </el-card>

            <!-- 添加题目对话框 -->
            <el-dialog title="添加题目" v-model="showAddDialog" width="80%">
                <div class="question-list">
                    <div class="search-bar" style="margin-bottom: 20px;">
                        <el-form :inline="true">
                            <el-form-item label="科目:">
                                <el-select v-model="searchForm.subjectId" placeholder="请选择科目" clearable>
                                    <el-option label="全部" value=""></el-option>
                                    <!-- 这里应该动态加载科目列表 -->
                                </el-select>
                            </el-form-item>
                            <el-form-item label="题型:">
                                <el-select v-model="searchForm.type" placeholder="请选择题型" clearable>
                                    <el-option label="全部" value=""></el-option>
                                    <el-option label="单选题" value="single"></el-option>
                                    <el-option label="多选题" value="multiple"></el-option>
                                    <el-option label="判断题" value="judge"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="关键词:">
                                <el-input v-model="searchForm.keyword" placeholder="请输入关键词" style="width: 200px;"/>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="searchQuestions">搜索</el-button>
                                <el-button @click="resetSearch">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </div>

                    <el-table :data="availableQuestions" @selection-change="handleSelectionChange" v-loading="questionLoading">
                        <el-table-column type="selection" width="55"/>
                        <el-table-column prop="content" label="题目内容" min-width="300"/>
                        <el-table-column prop="type" label="题型" width="100">
                            <template #default="{row}">
                                <el-tag v-if="row.type === 'single'">单选题</el-tag>
                                <el-tag v-else-if="row.type === 'multiple'" type="success">多选题</el-tag>
                                <el-tag v-else-if="row.type === 'judge'" type="warning">判断题</el-tag>
                                <el-tag v-else type="info">{{ row.type }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="subjectName" label="科目" width="120"/>
                        <el-table-column prop="difficulty" label="难度" width="100">
                            <template #default="{row}">
                                <el-tag v-if="row.difficulty === 'easy'" type="success">简单</el-tag>
                                <el-tag v-else-if="row.difficulty === 'medium'" type="warning">中等</el-tag>
                                <el-tag v-else-if="row.difficulty === 'hard'" type="danger">困难</el-tag>
                                <el-tag v-else type="info">{{ row.difficulty }}</el-tag>
                            </template>
                        </el-table-column>
                    </el-table>

                    <el-pagination
                        v-if="questionTotal > 0"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="questionPage.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="questionPage.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="questionTotal"
                        style="margin-top: 20px; text-align: center;">
                    </el-pagination>
                </div>

                <template #footer>
                    <el-button @click="showAddDialog = false">取消</el-button>
                    <el-button type="primary" @click="addSelectedQuestions">确定添加</el-button>
                </template>
            </el-dialog>
        </div>
    </basic-container>
</template>

<script>
import {getDetail} from "@/api/papers/papers";
import {getPaperItems, savePaperItems} from "@/api/papers/papers";
import {getList as getQuestionList} from "@/api/questions/questions";

export default {
    data() {
        return {
            paperId: null,
            paperInfo: {},
            paperItems: [],
            loading: false,
            showAddDialog: false,
            searchForm: {
                subjectId: '',
                type: '',
                keyword: ''
            },
            availableQuestions: [],
            selectedQuestions: [],
            questionLoading: false,
            questionPage: {
                currentPage: 1,
                pageSize: 10
            },
            questionTotal: 0
        }
    },
    computed: {
        totalScore() {
            return this.paperItems.reduce((sum, item) => sum + (item.score || 0), 0);
        }
    },
    mounted() {
        this.paperId = this.$route.params.id;
        this.loadPaperInfo();
        this.loadPaperItems();
    },
    methods: {
        loadPaperInfo() {
            getDetail(this.paperId).then(res => {
                this.paperInfo = res.data.data;
            });
        },
        loadPaperItems() {
            this.loading = true;
            getPaperItems(this.paperId).then(res => {
                this.paperItems = res.data.data || [];
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            });
        },
        searchQuestions() {
            this.questionPage.currentPage = 1;
            this.loadQuestions();
        },
        resetSearch() {
            this.searchForm = {
                subjectId: '',
                type: '',
                keyword: ''
            };
            this.searchQuestions();
        },
        loadQuestions() {
            this.questionLoading = true;
            const params = {
                ...this.searchForm,
                current: this.questionPage.currentPage,
                size: this.questionPage.pageSize
            };
            
            getQuestionList(this.questionPage.currentPage, this.questionPage.pageSize, params).then(res => {
                this.availableQuestions = res.data.data.records || [];
                this.questionTotal = res.data.data.total || 0;
                this.questionLoading = false;
            }).catch(() => {
                this.questionLoading = false;
            });
        },
        handleSelectionChange(selection) {
            this.selectedQuestions = selection;
        },
        handleSizeChange(size) {
            this.questionPage.pageSize = size;
            this.loadQuestions();
        },
        handleCurrentChange(page) {
            this.questionPage.currentPage = page;
            this.loadQuestions();
        },
        addSelectedQuestions() {
            if (this.selectedQuestions.length === 0) {
                this.$message.warning('请选择要添加的题目');
                return;
            }

            this.selectedQuestions.forEach(question => {
                // 检查是否已经添加过
                const exists = this.paperItems.find(item => item.questionId === question.id);
                if (!exists) {
                    this.paperItems.push({
                        questionId: question.id,
                        questionContent: question.content,
                        questionType: question.type,
                        subjectName: question.subjectName,
                        score: 10, // 默认分值
                        orderNo: this.paperItems.length + 1
                    });
                }
            });

            this.showAddDialog = false;
            this.calculateTotal();
        },
        removeItem(index) {
            this.paperItems.splice(index, 1);
            this.calculateTotal();
        },
        calculateTotal() {
            // 触发计算属性更新
            this.$forceUpdate();
        },
        savePaperItems() {
            if (this.paperItems.length === 0) {
                this.$message.warning('请至少添加一道题目');
                return;
            }

            const items = this.paperItems.map(item => ({
                questionId: item.questionId,
                score: item.score,
                orderNo: item.orderNo
            }));

            savePaperItems(this.paperId, items).then(() => {
                this.$message.success('题目配置保存成功');
            }).catch(() => {
                this.$message.error('题目配置保存失败');
            });
        },
        goBack() {
            this.$router.go(-1);
        }
    }
};
</script>

<style scoped>
.paper-questions {
    padding: 20px;
}

.toolbar {
    margin-bottom: 20px;
}

.toolbar .el-button {
    margin-right: 10px;
}

.summary {
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
}

.question-list {
    min-height: 400px;
}

.search-bar {
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
}
</style>
