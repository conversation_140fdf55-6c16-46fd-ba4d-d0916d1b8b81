<template>
    <basic-container>
        <div class="avue-crud">
            <el-row :hidden="!search" style="padding:6px 18px">
                <!-- 查询模块 -->
                <el-form :inline="true" :model="query">
                    <el-form-item label="试卷名称:">
                        <el-input v-model="query.name" placeholder="请输入试卷名称"></el-input>
                    </el-form-item>
                    <el-form-item label="是否打乱题序:">
                        <el-select style="width: 100px;" v-model="query.shuffle" clearable placeholder="请选择">
                            <el-option label="否" value="0"></el-option>
                            <el-option label="是" value="1"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="是否允许暂停:">
                        <el-select style="width: 100px;" v-model="query.allowPause" clearable placeholder="请选择">
                            <el-option label="否" value="0"></el-option>
                            <el-option label="是" value="1"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态:">
                        <el-select style="width: 100px;" v-model="query.status" clearable placeholder="请选择">
                            <el-option
                                v-for="item in statusOptions"
                                :key="item.dictKey"
                                :label="item.dictValue"
                                :value="item.dictKey">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="发布时间:">
                        <!-- 日期区间 -->
                        <el-date-picker v-model="query.publishAt" type="daterange" range-separator="至"
                                        start-placeholder="开始日期" end-placeholder="结束日期"
                                        style="width: 240px;"
                        ></el-date-picker>

                    </el-form-item><!---->
                    <!-- 查询按钮 -->
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" @click="searchChange">搜 索</el-button>
                        <el-button icon="el-icon-delete" @click="searchReset()">清 空</el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <div class="avue-crud__header">
                    <!-- 头部左侧按钮模块 -->
                    <div class="avue-crud__left">
                        <el-button v-if="this.permissionList.addBtn" type="primary" icon="el-icon-plus"
                                   @click="handleCreateWizard">向导式创建
                        </el-button>
                        <el-button v-if="this.permissionList.addBtn" type="success" icon="el-icon-plus"
                                   @click="handleAdd">快速新增
                        </el-button>
                        <el-button v-if="this.permissionList.delBtn" type="danger" icon="el-icon-delete"
                                   @click="handleDelete" plain>删 除
                        </el-button>
                    </div>
                    <!-- 头部右侧按钮模块 -->
                    <div class="avue-crud__right">
                        <el-button icon="el-icon-refresh" @click="searchChange" circle></el-button>
                        <el-button icon="el-icon-search" @click="searchHide" circle></el-button>
                    </div>
                </div>
            </el-row>
            <el-row>
                <!-- 列表模块 -->
                <el-table ref="table" v-loading="loading"
                          @selection-change="selectionChange"
                          :data="data"
                          :height="height"
                          style="width: 100%"
                          :border="option.border">
                    <el-table-column type="selection" v-if="option.selection" width="55"
                                     align="center"></el-table-column>
                    <el-table-column type="expand" v-if="option.expand" align="center"></el-table-column>
                    <el-table-column v-if="option.index" label="#" type="index" width="50" align="center" fixed="left">
                    </el-table-column>
                    <template v-for="(item,index) in option.column">
                        <!-- table字段 -->
                        <el-table-column v-if="item.hide!==true"
                                         :prop="item.prop"
                                         :label="item.label"
                                         :width="item.width"
                                         :fixed="item.fixed"
                                         :key="index">
                            <template v-if="item.prop === 'shuffle'" #default="{row}">
                                <el-tag>{{ row.shuffle === 1 ? '是' : '否' }}</el-tag>
                            </template>
                            <template v-if="item.prop === 'allowPause'" #default="{row}">
                                <el-tag>{{ row.allowPause === 1 ? '是' : '否' }}</el-tag>
                            </template>
                            <template v-if="item.prop === 'showResultDetail'" #default="{row}">
                                <el-tag>{{ row.showResultDetail === 1 ? '是' : '否' }}</el-tag>
                            </template>
                            <template v-if="item.prop === 'status'" #default="{row}">
                                <el-tag>{{ row.status === 1 ? '草稿' : row.status === 2 ? '已发布' : '已归档' }}</el-tag>
                            </template>
                            <template v-if="item.prop === 'publishAt'" #default="{row}">
                                {{ row.publishAt ? row.publishAt.split(' ')[0] : '' }}
                            </template>

                        </el-table-column>
                    </template>
                    <!-- 操作栏模块 -->
                    <el-table-column prop="menu" label="操作" :width="320" align="center" fixed="right">
                        <template #="{row}">
                            <el-button v-if="this.permissionList.viewBtn" type="primary" text icon="el-icon-view"
                                       @click="handleView(row)">查看
                            </el-button>
                            <el-button v-if="this.permissionList.editBtn && row.status === 1" type="primary" text icon="el-icon-edit"
                                       @click="handleEdit(row)">编辑
                            </el-button>
                            <el-button v-if="this.permissionList.editBtn && row.status === 1" type="success" text icon="el-icon-setting"
                                       @click="handleConfigQuestions(row)">配置题目
                            </el-button>
                            <el-button v-if="this.permissionList.editBtn && row.status === 1" type="warning" text icon="el-icon-upload"
                                       @click="handlePublish(row)">发布
                            </el-button>
                            <el-button v-if="this.permissionList.viewBtn && row.status === 2" type="success" text icon="el-icon-document"
                                       @click="handleViewQuestions(row)">查看题目
                            </el-button>
                            <el-button v-if="this.permissionList.editBtn && row.status === 2" type="info" text icon="el-icon-box"
                                       @click="handleArchive(row)">归档
                            </el-button>
                            <el-button v-if="this.permissionList.delBtn" type="danger" text icon="el-icon-delete"
                                       @click="rowDel(row)">删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-row>
            <el-row>
                <div class="avue-crud__pagination" style="width:100%">
                    <!-- 分页模块 -->
                    <el-pagination align="right"
                                   background
                                   @size-change="sizeChange"
                                   @current-change="currentChange"
                                   :current-page="page.currentPage"
                                   :page-sizes="[10, 20, 30, 40, 50, 100]"
                                   :page-size="page.pageSize"
                                   layout="total, sizes, prev, pager, next, jumper"
                                   :total="page.total">
                    </el-pagination>
                </div>
            </el-row>
            <!-- 表单模块 -->
            <el-dialog :title="title"
                       v-model="box"
                       width="70%"
                       :before-close="beforeClose"
                       append-to-body>
                <el-form :disabled="view" ref="form" :model="form" label-width="150px">
                    <!-- 表单字段 -->
                    <el-form-item label="试卷名称" prop="name">
                        <el-input v-model="form.name" placeholder="请输入试卷名称"/>
                    </el-form-item>
                    <el-form-item label="试卷描述" prop="description">
                        <el-input type="textarea" :rows="5" v-model="form.description" placeholder="请输入试卷描述"/>
                    </el-form-item>
                    <el-form-item label="发布时间" prop="publishAt">
                        <el-date-picker v-model="form.publishAt" type="date" value-format="YYYY-MM-DD"
                                        placeholder="请选择发布时间"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="考试时长(分钟)" prop="durationMinutes">
                        <el-input-number v-model="form.durationMinutes" :min="1" placeholder="请输入考试时长(分钟)"/>
                    </el-form-item>
                    <el-form-item label="及格分数" prop="passScore">
                        <el-input-number v-model="form.passScore" :min="0" :max="100" step="0.5" placeholder="请输入及格分数"/>
                    </el-form-item>
<!--                    <el-form-item label="总分" prop="totalScore">-->
<!--                        <el-input v-model="form.totalScore" placeholder="请输入总分"/>-->
<!--                    </el-form-item>-->
                    <el-form-item label="是否打乱题目顺序" prop="shuffle">
                        <el-switch v-model="form.shuffle" :active-value="1" :inactive-value="0" active-text="是" inactive-text="否"/>
                    </el-form-item>
                    <el-form-item label="是否允许暂停" prop="allowPause">
                        <el-switch v-model="form.allowPause" :active-value="1" :inactive-value="0" active-text="是" inactive-text="否"/>
                    </el-form-item>
                    <el-form-item label="是否显示结果详情" prop="showResultDetail">
                        <el-switch v-model="form.showResultDetail" :active-value="1" :inactive-value="0" active-text="是" inactive-text="否"/>
                    </el-form-item>
                </el-form>
                <!-- 表单按钮 -->
                <template #footer>
          <span v-if="!view" class="dialog-footer">
            <el-button type="primary" icon="el-icon-circle-check" @click="handleSubmit">提 交</el-button>
            <el-button icon="el-icon-circle-close" @click="box = false">取 消</el-button>
          </span>
                </template>
            </el-dialog>

            <!-- 向导式创建对话框 -->
            <el-dialog title="向导式创建试卷" v-model="wizardDialog" width="90%" fullscreen>
                <div class="paper-wizard">
                    <el-steps :active="currentStep" finish-status="success" align-center>
                        <el-step title="基本信息" description="填写试卷基本信息"></el-step>
                        <el-step title="配置题目" description="选择题目并设置分值"></el-step>
                        <el-step title="完成创建" description="保存草稿或发布试卷"></el-step>
                    </el-steps>

                    <!-- 步骤1：基本信息 -->
                    <div v-show="currentStep === 0" class="step-content">
                        <el-card header="试卷基本信息">
                            <el-form :model="wizardForm" :rules="wizardRules" ref="wizardForm" label-width="120px">
                                <el-form-item label="试卷名称：" prop="name">
                                    <el-input v-model="wizardForm.name" placeholder="请输入试卷名称"/>
                                </el-form-item>
                                <el-form-item label="试卷描述：" prop="description">
                                    <el-input type="textarea" :rows="4" v-model="wizardForm.description" placeholder="请输入试卷描述"/>
                                </el-form-item>
                                <el-row :gutter="20">
                                    <el-col :span="8">
                                        <el-form-item label="考试时长(分钟)：" label-width="130" prop="durationMinutes">
                                            <el-input-number v-model="wizardForm.durationMinutes" :min="1" :max="300" placeholder="请输入考试时长"/>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="及格分数：" prop="passScore">
                                            <el-input-number v-model="wizardForm.passScore" :min="0" :max="100" step="0.5" placeholder="请输入及格分数"/>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="总分：" prop="totalScore">
                                            总分将自动计算
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="8">
                                        <el-form-item label="打乱题目顺序：">
                                            <el-switch v-model="wizardForm.shuffle" :active-value="1" :inactive-value="0" active-text="是" inactive-text="否"/>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="允许暂停：">
                                            <el-switch v-model="wizardForm.allowPause" :active-value="1" :inactive-value="0" active-text="是" inactive-text="否"/>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="显示结果详情：">
                                            <el-switch v-model="wizardForm.showResultDetail" :active-value="1" :inactive-value="0" active-text="是" inactive-text="否"/>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </el-card>
                    </div>

                    <!-- 步骤2：配置题目 -->
                    <div v-show="currentStep === 1" class="step-content">
                        <el-card header="配置题目">
                            <div class="question-config">
                                <el-button type="primary" @click="openQuestionSelector">添加题目</el-button>
                                <el-table :data="selectedQuestions" style="margin-top: 20px;">
                                    <el-table-column type="index" label="#" width="50"/>
                                    <el-table-column prop="questionContent" label="题目内容" min-width="200"/>
                                    <el-table-column prop="subjectName" label="科目" width="120"/>
                                    <el-table-column prop="questionType" label="题型" width="100" align="center">
                                        <template #default="{row}">
                                            <el-tag v-if="row.questionType === 'single'">单选题</el-tag>
                                            <el-tag v-else-if="row.questionType === 'multiple'" type="success">多选题</el-tag>
                                            <el-tag v-else-if="row.questionType === 'judge'" type="warning">判断题</el-tag>
                                            <el-tag v-else type="info">{{ row.questionType }}</el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="score" label="分值" width="120">
                                        <template #default="{row, $index}">
                                            <el-input-number v-model="row.score" :min="1" :max="100" step="0.5" size="small" @change="calculateWizardTotal"
                                                             style="width: 95px;"
                                            />
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="orderNo" label="排序" width="120">
                                        <template #default="{row, $index}">
                                            <el-input-number v-model="row.orderNo" :min="1" size="small"
                                                             style="width: 95px;"
                                            />
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="操作" width="100">
                                        <template #default="{row, $index}">
                                            <el-button type="danger" size="small" @click="removeWizardQuestion($index)">删除</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <div style="margin-top: 20px;">
                                    <span>总分：{{ wizardTotalScore }}</span>
                                </div>
                            </div>
                        </el-card>
                    </div>

                    <!-- 步骤3：完成创建 -->
                    <div v-show="currentStep === 2" class="step-content">
                        <el-card header="完成创建">
                            <div class="summary">
                                <h3>试卷信息确认</h3>
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <p><strong>试卷名称：</strong>{{ wizardForm.name }}</p>
                                        <p><strong>考试时长：</strong>{{ wizardForm.durationMinutes }}分钟</p>
                                        <p><strong>及格分数：</strong>{{ wizardForm.passScore }}分</p>
                                    </el-col>
                                    <el-col :span="12">
                                        <p><strong>题目数量：</strong>{{ selectedQuestions.length }}题</p>
                                        <p><strong>总分：</strong>{{ wizardTotalScore }}分</p>
                                        <p><strong>打乱题序：</strong>{{ wizardForm.shuffle ? '是' : '否' }}</p>
                                    </el-col>
                                </el-row>
                            </div>
                        </el-card>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="wizard-actions">
                        <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
                        <el-button v-if="currentStep < 2" type="primary" @click="nextStep">下一步</el-button>
                        <el-button v-if="currentStep === 2" type="success" @click="saveWizardDraft">保存草稿</el-button>
                        <el-button v-if="currentStep === 2" type="primary" @click="publishWizardPaper">发布试卷</el-button>
                        <el-button @click="closeWizard">取消</el-button>
                    </div>
                </div>
            </el-dialog>

            <!-- 题目选择对话框 -->
            <el-dialog title="选择题目" v-model="showQuestionDialog" width="90%" append-to-body>
                <div class="question-selector">
                    <!-- 搜索条件 -->
                    <div class="search-bar" style="margin-bottom: 20px;">
                        <el-form :inline="true" :model="questionSearchForm">
                            <el-form-item label="科目:">
                                <el-select v-model="questionSearchForm.subjectId" placeholder="请选择科目" clearable style="width: 150px;">
                                    <el-option label="全部" value=""></el-option>
                                    <el-option
                                        v-for="subject in subjectOptions"
                                        :key="subject.value"
                                        :label="subject.label"
                                        :value="subject.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="题型:">
                                <el-select v-model="questionSearchForm.type" placeholder="请选择题型" clearable style="width: 120px;">
                                    <el-option label="全部" value=""></el-option>
                                    <el-option label="单选题" value="single"></el-option>
                                    <el-option label="多选题" value="multiple"></el-option>
                                    <el-option label="判断题" value="judge"></el-option>
                                    <el-option label="问答题" value="essay"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="难度:">
                                <el-select v-model="questionSearchForm.difficulty" placeholder="请选择难度" clearable style="width: 120px;">
                                    <el-option label="全部" value=""></el-option>
                                    <el-option label="简单" value="easy"></el-option>
                                    <el-option label="中等" value="medium"></el-option>
                                    <el-option label="困难" value="hard"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="关键词:">
                                <el-input v-model="questionSearchForm.content" placeholder="请输入题目关键词" style="width: 200px;"/>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="searchQuestions">搜索</el-button>
                                <el-button @click="resetQuestionSearch">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </div>

                    <!-- 题目列表 -->
                    <el-table
                        :data="availableQuestions"
                        @selection-change="handleQuestionSelectionChange"
                        v-loading="questionLoading"
                        max-height="400">
                        <el-table-column type="selection" width="55"/>
                        <el-table-column prop="content" label="题目内容" min-width="300" show-overflow-tooltip/>
                        <el-table-column prop="type" label="题型" width="100">
                            <template #default="{row}">
                                <el-tag v-if="row.type === 'single'">单选题</el-tag>
                                <el-tag v-else-if="row.type === 'multiple'" type="success">多选题</el-tag>
                                <el-tag v-else-if="row.type === 'judge'" type="warning">判断题</el-tag>
                                <el-tag v-else-if="row.type === 'essay'" type="info">问答题</el-tag>
                                <el-tag v-else type="info">{{ row.type }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="subjectName" label="科目" width="120"/>
                        <el-table-column prop="difficulty" label="难度" width="100">
                            <template #default="{row}">
                                <el-tag v-if="row.difficulty === 'easy'" type="success">简单</el-tag>
                                <el-tag v-else-if="row.difficulty === 'medium'" type="warning">中等</el-tag>
                                <el-tag v-else-if="row.difficulty === 'hard'" type="danger">困难</el-tag>
                                <el-tag v-else type="info">{{ row.difficulty }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态" width="100">
                            <template #default="{row}">
                                <el-tag v-if="isQuestionSelected(row.id)" type="info">已选择</el-tag>
                                <span v-else>-</span>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <el-pagination
                        v-if="questionTotal > 0"
                        @size-change="handleQuestionSizeChange"
                        @current-change="handleQuestionCurrentChange"
                        :current-page="questionPage.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="questionPage.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="questionTotal"
                        style="margin-top: 20px; text-align: center; justify-content: flex-end;">
                    </el-pagination>
                </div>
                <template #footer>
                    <div style="text-align: left; margin-bottom: 10px;">
                        <span>已选择 {{ selectedQuestionItems.length }} 道题目</span>
                    </div>
                    <el-button @click="showQuestionDialog = false">取消</el-button>
                    <el-button type="primary" @click="confirmAddQuestions" :disabled="selectedQuestionItems.length === 0">
                        确定添加 ({{ selectedQuestionItems.length }})
                    </el-button>
                </template>
            </el-dialog>

            <!-- 题目配置对话框 -->
            <el-dialog title="配置试卷题目" v-model="configDialog" width="90%" fullscreen>
                <div class="paper-config">
                    <div class="config-header">
                        <h3>{{ currentPaper.name }} - 题目配置</h3>
                        <div class="config-actions">
                            <el-button type="primary" @click="openQuestionSelector">添加题目</el-button>
                            <el-button type="success" @click="saveConfigItems">保存配置</el-button>
                            <el-button @click="closeConfig">关闭</el-button>
                        </div>
                    </div>

                    <el-table :data="configQuestions" style="margin-top: 20px;" v-loading="configLoading">
                        <el-table-column type="index" label="#" width="50"/>
                        <el-table-column prop="questionContent" label="题目内容" min-width="200" show-overflow-tooltip/>
                        <el-table-column prop="questionType" label="题型" width="100">
                            <template #default="{row}">
                                <el-tag v-if="row.questionType === 'single'">单选题</el-tag>
                                <el-tag v-else-if="row.questionType === 'multiple'" type="success">多选题</el-tag>
                                <el-tag v-else-if="row.questionType === 'judge'" type="warning">判断题</el-tag>
                                <el-tag v-else-if="row.questionType === 'essay'" type="info">问答题</el-tag>
                                <el-tag v-else type="info">{{ row.questionType }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="subjectName" label="科目" width="120"/>
                        <el-table-column prop="score" label="分值" width="120">
                            <template #default="{row}">
                                <el-input-number style="width: 95px;" v-model="row.score" :min="1" :max="100" :step="0.5" size="small" @change="calculateConfigTotal"/>
                            </template>
                        </el-table-column>
                        <el-table-column prop="orderNo" label="排序" width="120">
                            <template #default="{row}">
                                <el-input-number style="width: 95px;" v-model="row.orderNo" :min="1" size="small"/>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="100">
                            <template #default="{row, $index}">
                                <el-button type="danger" size="small" @click="removeConfigQuestion($index)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <div class="config-summary" style="margin-top: 20px;">
                        <el-row>
                            <el-col :span="12">
                                <span>题目总数：{{ configQuestions.length }}题</span>
                            </el-col>
                            <el-col :span="12" style="text-align: right;">
                                <span>总分：{{ configTotalScore }}分</span>
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </el-dialog>

            <!-- 查看题目对话框 -->
            <el-dialog title="查看试卷题目" v-model="viewQuestionsDialog" width="90%" append-to-body>
                <div class="view-questions">
                    <div class="questions-header">
                        <h3>{{ currentViewPaper.name }} - 题目列表</h3>
                        <div class="questions-summary">
                            <span>题目总数：{{ viewQuestions.length }}题</span>
                            <span style="margin-left: 20px;">总分：{{ viewTotalScore }}分</span>
                        </div>
                    </div>

                    <el-table :data="viewQuestions" style="margin-top: 20px;" v-loading="viewQuestionsLoading">
                        <el-table-column type="index" label="#" width="50"/>
                        <el-table-column prop="questionContent" label="题目内容" min-width="300" show-overflow-tooltip/>
                        <el-table-column prop="questionType" label="题型" width="100">
                            <template #default="{row}">
                                <el-tag v-if="row.questionType === 'single'">单选题</el-tag>
                                <el-tag v-else-if="row.questionType === 'multiple'" type="success">多选题</el-tag>
                                <el-tag v-else-if="row.questionType === 'judge'" type="warning">判断题</el-tag>
                                <el-tag v-else-if="row.questionType === 'essay'" type="info">问答题</el-tag>
                                <el-tag v-else type="info">{{ row.questionType }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="subjectName" label="科目" width="120"/>
                        <el-table-column prop="score" label="分值" width="80" align="center"/>
                        <el-table-column prop="orderNo" label="排序" width="80" align="center"/>
                    </el-table>
                </div>
                <template #footer>
                    <el-button @click="closeViewQuestions">关闭</el-button>
                </template>
            </el-dialog>
        </div>
    </basic-container>
</template>

<script>
import {getList, getDetail, add, update, remove, publishPaper, archivePaper, savePaperItems, getPaperItems, createDraft} from "@/api/papers/papers";
import {getList as getQuestionList} from "@/api/questions/questions";
import {selectSubjectsOptions} from "@/api/subjects/subjects";
import option from "@/option/papers/papers";
import {mapGetters} from "vuex";
import dictBiz from "@/utils/dictBiz";

export default {
    data() {
        return {
            height: 0,
            // 弹框标题
            title: '',
            // 是否展示弹框
            box: false,
            // 是否显示查询
            search: true,
            // 加载中
            loading: true,
            // 是否为查看模式
            view: false,
            // 查询信息
            query: {},
            // 分页信息
            page: {
                currentPage: 1,
                pageSize: 10,
                total: 40
            },
            // 表单数据
            form: {},
            // 选择行
            selectionList: [],
            // 表单配置
            option: option,
            // 表单列表
            data: [],

            statusOptions: [],

            // 向导相关数据
            wizardDialog: false,
            currentStep: 0,
            showQuestionDialog: false,
            wizardForm: {
                name: '',
                description: '',
                durationMinutes: 60,
                passScore: 60,
                totalScore: 0,
                shuffle: false,
                allowPause: true,
                showResultDetail: true
            },
            wizardRules: {
                name: [
                    {required: true, message: '请输入试卷名称', trigger: 'blur'}
                ],
                durationMinutes: [
                    {required: true, message: '请输入考试时长', trigger: 'blur'}
                ],
                passScore: [
                    {required: true, message: '请输入及格分数', trigger: 'blur'}
                ]
            },
            selectedQuestions: [],
            createdPaperId: null,
            // 题目选择相关数据
            availableQuestions: [],
            selectedQuestionItems: [],
            questionLoading: false,
            questionSearchForm: {
                subjectId: '',
                type: '',
                difficulty: '',
                content: ''
            },
            questionPage: {
                currentPage: 1,
                pageSize: 10
            },
            questionTotal: 0,
            subjectOptions: [],
            // 题目配置相关数据
            configDialog: false,
            configLoading: false,
            configQuestions: [],
            currentPaper: {},
            // 查看题目相关数据
            viewQuestionsDialog: false,
            viewQuestionsLoading: false,
            viewQuestions: [],
            currentViewPaper: {}
        }
    },
    async mounted() {
        await this.loadStatusOptions();
        this.init();
        this.onLoad(this.page);
        this.loadSubjectOptions();
    },
    computed: {
        ...mapGetters(["permission"]),
        permissionList() {
            return {
                addBtn: this.validData(this.permission.papers_add, false),
                viewBtn: this.validData(this.permission.papers_view, false),
                delBtn: this.validData(this.permission.papers_delete, false),
                editBtn: this.validData(this.permission.papers_edit, false),
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        },
        wizardTotalScore() {
            return this.selectedQuestions.reduce((sum, item) => sum + (item.score || 0), 0);
        },
        configTotalScore() {
            return this.configQuestions.reduce((sum, item) => sum + (item.score || 0), 0);
        },
        viewTotalScore() {
            return this.viewQuestions.reduce((sum, item) => sum + (item.score || 0), 0);
        }
    },
    methods: {
        init() {
            console.log(document.body.clientHeight)
            this.height = this.setPx(document.body.clientHeight - 395);
        },
        searchHide() {
            this.search = !this.search;
        },
        searchChange() {
            this.onLoad(this.page);
        },
        searchReset() {
            this.query = {};
            this.page.currentPage = 1;
            this.onLoad(this.page);
        },
        handleSubmit() {
            // 处理日期格式
            const formData = { ...this.form };
            if (formData.publishAt && typeof formData.publishAt === 'string') {
                // 如果是日期字符串格式 "2025-09-03"，转换为完整的日期时间格式
                if (formData.publishAt.length === 10) {
                    formData.publishAt = formData.publishAt + ' 00:00:00';
                }
            }

            if (!this.form.id) {
                add(formData).then(() => {
                    this.box = false;
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                }).catch((error) => {
                    console.error('添加失败:', error);
                    this.$message({
                        type: "error",
                        message: "操作失败: " + (error.message || '未知错误')
                    });
                });
            } else {
                update(formData).then(() => {
                    this.box = false;
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                }).catch((error) => {
                    console.error('更新失败:', error);
                    this.$message({
                        type: "error",
                        message: "操作失败: " + (error.message || '未知错误')
                    });
                });
            }
        },
        handleAdd() {
            this.title = '新增'
            this.form = {}
            this.box = true
        },
        handleEdit(row) {
            this.title = '编辑'
            this.box = true
            getDetail(row.id).then(res => {
                this.form = res.data.data;
            });
        },
        handleView(row) {
            this.title = '查看'
            this.view = true;
            this.box = true;
            getDetail(row.id).then(res => {
                this.form = res.data.data;
            });
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.selectionClear();
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        beforeClose(done) {
            done()
            this.form = {};
            this.view = false;
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.table.clearSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
            this.onLoad(this.page);
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
            this.onLoad(this.page);
        },
        onLoad(page, params = {}) {
            this.loading = true;

            const {
                name,
                shuffle,
                allowPause,
                showResultDetail,
                publishAt,
                status,
            } = this.query;

            let values = {
                name_like: name,
                shuffle_equal: shuffle,
                allowPause_equal: allowPause,
                showResultDetail_equal: showResultDetail,
                publishAt_between: publishAt,
                status_equal: status,
            };

            getList(page.currentPage, page.pageSize, values).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        async loadStatusOptions() {
            this.statusOptions = await dictBiz.getMap('papers_status');
        },
        handleCreateWizard() {
            // 打开向导式创建对话框
            this.wizardDialog = true;
            this.currentStep = 0;
            this.resetWizardForm();
        },
        handleConfigQuestions(row) {
            // 打开题目配置对话框
            this.currentPaper = row;
            this.configDialog = true;
            this.loadPaperItems(row.id);
        },
        handlePublish(row) {
            this.$confirm("确定要发布此试卷吗？发布后将不能再编辑。", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return publishPaper(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "试卷发布成功!"
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "error",
                        message: "试卷发布失败!"
                    });
                });
        },
        handleArchive(row) {
            this.$confirm("确定要归档此试卷吗？", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return archivePaper(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "试卷归档成功!"
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "error",
                        message: "试卷归档失败!"
                    });
                });
        },
        // 向导相关方法
        resetWizardForm() {
            this.wizardForm = {
                name: '',
                description: '',
                durationMinutes: 60,
                passScore: 60,
                totalScore: 0,
                shuffle: false,
                allowPause: true,
                showResultDetail: true
            };
            this.selectedQuestions = [];
            this.createdPaperId = null;
        },
        nextStep() {
            if (this.currentStep === 0) {
                this.$refs.wizardForm.validate((valid) => {
                    if (valid) {
                        this.currentStep++;
                    }
                });
            } else if (this.currentStep === 1) {
                if (this.selectedQuestions.length === 0) {
                    this.$message.warning('请至少添加一道题目');
                    return;
                }
                // 验证题目数据完整性
                const invalidQuestions = this.selectedQuestions.filter(q => !q.questionId || !q.score);
                if (invalidQuestions.length > 0) {
                    this.$message.error('存在无效的题目数据，请检查题目ID和分值');
                    console.error('无效题目:', invalidQuestions);
                    return;
                }
                this.wizardForm.totalScore = this.wizardTotalScore;
                this.currentStep++;
            }
        },
        prevStep() {
            this.currentStep--;
        },
        closeWizard() {
            this.wizardDialog = false;
            this.currentStep = 0;
            this.resetWizardForm();
        },
        removeWizardQuestion(index) {
            this.selectedQuestions.splice(index, 1);
            this.calculateWizardTotal();
        },
        calculateWizardTotal() {
            this.wizardForm.totalScore = this.wizardTotalScore;
            this.$forceUpdate();
        },
        saveWizardDraft() {
            const paperData = {
                ...this.wizardForm
                // createDraft接口会自动设置为草稿状态
            };

            createDraft(paperData).then(res => {
                console.log('试卷创建响应:', res);
                console.log('响应数据结构:', res.data);

                // 尝试不同的数据结构路径
                this.createdPaperId = res.data.data?.id || res.data?.id || res.id;
                console.log('获取到的试卷ID:', this.createdPaperId);

                if (!this.createdPaperId) {
                    console.error('无法获取试卷ID，响应数据:', res);
                    throw new Error('试卷创建成功但未获取到试卷ID');
                }

                // 保存题目关联
                if (this.selectedQuestions.length > 0) {
                    const items = this.selectedQuestions.map((item, index) => ({
                        questionId: item.questionId,
                        score: Number(item.score) || 10, // 确保是数字类型
                        orderNo: item.orderNo || (index + 1) // 确保有正确的排序
                    }));
                    console.log('草稿模式 - 准备保存题目关联, paperId:', this.createdPaperId, 'items:', items);
                    console.log('草稿模式 - 总分:', this.wizardTotalScore);
                    return savePaperItems(this.createdPaperId, items);
                }
                return Promise.resolve();
            }).then((itemsResult) => {
                if (itemsResult) {
                    console.log('草稿模式 - 题目关联保存成功:', itemsResult);
                }
                this.$message.success('草稿保存成功');
                this.closeWizard();
                this.onLoad(this.page);
            }).catch((error) => {
                console.error('草稿保存失败:', error);
                this.$message.error('草稿保存失败: ' + (error.message || '未知错误'));
            });
        },
        publishWizardPaper() {
            const paperData = {
                ...this.wizardForm
                // createDraft接口会自动设置为草稿状态
            };

            createDraft(paperData).then(res => {
                console.log('试卷创建响应:', res);
                console.log('响应数据结构:', res.data);

                // 尝试不同的数据结构路径
                this.createdPaperId = res.data.data?.id || res.data?.id || res.id;
                console.log('获取到的试卷ID:', this.createdPaperId);

                if (!this.createdPaperId) {
                    console.error('无法获取试卷ID，响应数据:', res);
                    throw new Error('试卷创建成功但未获取到试卷ID');
                }

                // 保存题目关联
                if (this.selectedQuestions.length > 0) {
                    const items = this.selectedQuestions.map((item, index) => ({
                        questionId: item.questionId,
                        score: Number(item.score) || 10, // 确保是数字类型
                        orderNo: item.orderNo || (index + 1) // 确保有正确的排序
                    }));
                    console.log('发布模式 - 准备保存题目关联, paperId:', this.createdPaperId, 'items:', items);
                    console.log('发布模式 - 总分:', this.wizardTotalScore);
                    return savePaperItems(this.createdPaperId, items);
                }
                return Promise.resolve();
            }).then((itemsResult) => {
                if (itemsResult) {
                    console.log('题目关联保存成功:', itemsResult);
                }
                // 发布试卷
                console.log('准备发布试卷，ID:', this.createdPaperId);
                return publishPaper(this.createdPaperId);
            }).then(() => {
                console.log('试卷发布成功');
                this.$message.success('试卷发布成功');
                this.closeWizard();
                this.onLoad(this.page);
            }).catch((error) => {
                console.error('试卷发布失败:', error);
                this.$message.error('试卷发布失败: ' + (error.message || '未知错误'));
            });
        },
        // 题目选择相关方法
        async loadSubjectOptions() {
            try {
                const res = await selectSubjectsOptions();
                this.subjectOptions = res.data.data.map(item => ({
                    label: item.name,
                    value: item.id
                }));
            } catch (error) {
                console.error('加载科目选项失败:', error);
            }
        },
        searchQuestions() {
            this.questionPage.currentPage = 1;
            this.loadQuestions();
        },
        resetQuestionSearch() {
            this.questionSearchForm = {
                subjectId: '',
                type: '',
                difficulty: '',
                content: ''
            };
            this.searchQuestions();
        },
        loadQuestions() {
            this.questionLoading = true;
            const params = {
                subjectId_equal: this.questionSearchForm.subjectId,
                type_equal: this.questionSearchForm.type,
                difficulty_equal: this.questionSearchForm.difficulty,
                content_like: this.questionSearchForm.content
            };

            getQuestionList(this.questionPage.currentPage, this.questionPage.pageSize, params).then(res => {
                this.availableQuestions = res.data.data.records || [];
                this.questionTotal = res.data.data.total || 0;
                this.questionLoading = false;
            }).catch(() => {
                this.questionLoading = false;
                this.$message.error('加载题目失败');
            });
        },
        handleQuestionSelectionChange(selection) {
            this.selectedQuestionItems = selection;
        },
        handleQuestionSizeChange(size) {
            this.questionPage.pageSize = size;
            this.loadQuestions();
        },
        handleQuestionCurrentChange(page) {
            this.questionPage.currentPage = page;
            this.loadQuestions();
        },
        // 打开题目选择对话框
        openQuestionSelector() {
            this.showQuestionDialog = true;
            this.selectedQuestionItems = [];
            this.loadQuestions();
        },
        // 重写原来的示例方法，现在打开真实的题目选择对话框
        addSampleQuestion() {
            this.openQuestionSelector();
        },
        // 题目配置相关方法
        loadPaperItems(paperId) {
            this.configLoading = true;
            getPaperItems(paperId).then(res => {
                this.configQuestions = res.data.data || [];
                this.configLoading = false;
            }).catch(() => {
                this.configLoading = false;
                this.$message.error('加载试卷题目失败');
            });
        },
        closeConfig() {
            this.configDialog = false;
            this.configQuestions = [];
            this.currentPaper = {};
        },
        removeConfigQuestion(index) {
            this.configQuestions.splice(index, 1);
            this.calculateConfigTotal();
        },
        calculateConfigTotal() {
            this.$forceUpdate();
        },
        saveConfigItems() {
            if (this.configQuestions.length === 0) {
                this.$message.warning('请至少添加一道题目');
                return;
            }

            const items = this.configQuestions.map((item, index) => ({
                questionId: item.questionId,
                score: Number(item.score) || 10, // 确保是数字类型
                orderNo: item.orderNo || (index + 1) // 确保有正确的排序
            }));

            console.log('保存题目配置, paperId:', this.currentPaper.id, 'items:', items);
            console.log('当前总分:', this.configTotalScore);

            savePaperItems(this.currentPaper.id, items).then(() => {
                this.$message.success(`题目配置保存成功，总分：${this.configTotalScore}分`);
                this.closeConfig();
                this.onLoad(this.page); // 刷新列表以显示更新后的总分
            }).catch((error) => {
                console.error('题目配置保存失败:', error);
                this.$message.error('题目配置保存失败');
            });
        },
        // 重写题目选择方法，支持配置模式
        confirmAddQuestions() {
            if (this.selectedQuestionItems.length === 0) {
                this.$message.warning('请选择要添加的题目');
                return;
            }

            let addedCount = 0;
            const targetQuestions = this.configDialog ? this.configQuestions : this.selectedQuestions;

            this.selectedQuestionItems.forEach(question => {
                // 检查是否已经添加过
                const exists = targetQuestions.find(item => item.questionId === question.id);
                if (!exists) {
                    targetQuestions.push({
                        questionId: question.id,
                        questionContent: question.content,
                        questionType: question.type,
                        subjectName: question.subjectName,
                        score: 10, // 默认分值
                        orderNo: targetQuestions.length + 1
                    });
                    addedCount++;
                }
            });

            if (addedCount > 0) {
                this.$message.success(`成功添加 ${addedCount} 道题目`);
                if (this.configDialog) {
                    this.calculateConfigTotal();
                } else {
                    this.calculateWizardTotal();
                }
            } else {
                this.$message.warning('所选题目已存在，未添加任何题目');
            }

            this.showQuestionDialog = false;
            this.selectedQuestionItems = [];
        },
        // 重写题目重复检测方法，支持配置模式
        isQuestionSelected(questionId) {
            const targetQuestions = this.configDialog ? this.configQuestions : this.selectedQuestions;
            return targetQuestions.some(item => item.questionId === questionId);
        },
        // 查看题目相关方法
        handleViewQuestions(row) {
            this.currentViewPaper = row;
            this.viewQuestionsDialog = true;
            this.loadViewQuestions(row.id);
        },
        loadViewQuestions(paperId) {
            this.viewQuestionsLoading = true;
            getPaperItems(paperId).then(res => {
                this.viewQuestions = res.data.data || [];
                this.viewQuestionsLoading = false;
            }).catch(() => {
                this.viewQuestionsLoading = false;
                this.$message.error('加载试卷题目失败');
            });
        },
        closeViewQuestions() {
            this.viewQuestionsDialog = false;
            this.viewQuestions = [];
            this.currentViewPaper = {};
        }
    }
};
</script>

<style scoped>
.paper-wizard {
    padding: 20px;
}

.step-content {
    margin: 40px 0;
    min-height: 400px;
}

.wizard-actions {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    border-top: 1px solid #ebeef5;
}

.wizard-actions .el-button {
    margin: 0 10px;
}

.summary {
    padding: 20px;
}

.summary h3 {
    margin-bottom: 20px;
    color: #409EFF;
}

.summary p {
    margin: 10px 0;
    font-size: 16px;
}

.question-config {
    padding: 0;
}

.question-selector {
    min-height: 500px;
}

.search-bar {
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    margin-bottom: 20px;
}

.paper-config {
    padding: 20px;
}

.config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
}

.config-header h3 {
    margin: 0;
    color: #409EFF;
}

.config-actions .el-button {
    margin-left: 10px;
}

.config-summary {
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
}
</style>
