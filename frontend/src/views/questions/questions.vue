<template>
    <basic-container>
        <div class="avue-crud">
            <el-row :hidden="!search" style="padding:6px 18px">
                <!-- 查询模块 -->
                <el-form :inline="true" :model="query">
                    <el-form-item label="科目名称:">
                        <el-input v-model="query.subjectName" placeholder="请输入科目名称"></el-input>
                    </el-form-item>
                    <el-form-item label="题目内容:">
                        <el-input v-model="query.content" placeholder="请输入题目内容"></el-input>
                    </el-form-item>
<!--                    <el-form-item label="答案:">-->
<!--                        <el-input v-model="query.answer" placeholder="请输入答案"></el-input>-->
<!--                    </el-form-item>-->
<!--                    <el-form-item label="解析:">-->
<!--                        <el-input v-model="query.explanation" placeholder="请输入解析"></el-input>-->
<!--                    </el-form-item>-->
                    <el-form-item label="题型:">
                        <el-select style="width: 100px;" v-model="query.type" clearable placeholder="请选择">
                            <el-option
                                v-for="item in typeOptions"
                                :key="item.dictKey"
                                :label="item.dictValue"
                                :value="item.dictKey"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="难度:">
                        <el-select style="width: 100px;" v-model="query.difficulty" clearable placeholder="请选择">
                            <el-option
                                v-for="item in difficultyOptions"
                                :key="item.dictKey"
                                :label="item.dictValue"
                                :value="item.dictKey"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态:">
                        <el-select style="width: 100px;" v-model="query.status" clearable placeholder="请选择">
                            <el-option label="启用" value="1"></el-option>
                            <el-option label="禁用" value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- 查询按钮 -->
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" @click="searchChange">搜 索</el-button>
                        <el-button icon="el-icon-delete" @click="searchReset()">清 空</el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <div class="avue-crud__header">
                    <!-- 头部左侧按钮模块 -->
                    <div class="avue-crud__left">
                        <el-button v-if="this.permissionList.addBtn" type="primary" icon="el-icon-plus"
                                   @click="handleAdd">新 增
                        </el-button>
                        <el-button v-if="this.permissionList.delBtn" type="danger" icon="el-icon-delete"
                                   @click="handleDelete" plain>删 除
                        </el-button>
                    </div>
                    <!-- 头部右侧按钮模块 -->
                    <div class="avue-crud__right">
                        <el-button icon="el-icon-refresh" @click="searchChange" circle></el-button>
                        <el-button icon="el-icon-search" @click="searchHide" circle></el-button>
                    </div>
                </div>
            </el-row>
            <el-row>
                <!-- 列表模块 -->
                <el-table ref="table" v-loading="loading"
                          @selection-change="selectionChange"
                          :data="data"
                          :height="height"
                          style="width: 100%;"
                          :border="option.border">
                    <el-table-column type="selection" v-if="option.selection" width="55"
                                     align="center"></el-table-column>
                    <el-table-column type="expand" v-if="option.expand" align="center"></el-table-column>
                    <el-table-column v-if="option.index" label="#" type="index" width="50" align="center">
                    </el-table-column>
                    <template v-for="(item,index) in option.column">
                        <!-- table字段 -->
                        <el-table-column v-if="item.hide!==true"
                                         :prop="item.prop"
                                         :label="item.label"
                                         :width="item.width"
                                         :key="index">
                            <template v-if="item.prop === 'status'" #default="{row}">
                                <el-tag>{{ row.status === 1 ? '启用' : '禁用' }}</el-tag>
                            </template>
                            <template v-if="item.prop === 'options'" #default="{row}">
                                <div class="options-display">
                                    <div v-for="(option, key) in parseOptions(row.options)" :key="key" class="option-item">
                                        <span class="option-key">{{ key }}.</span>
                                        <span class="option-content">{{ option }}</span>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                    </template>
                    <!-- 操作栏模块 -->
                    <el-table-column prop="menu" label="操作" :width="220" align="center">
                        <template #="{row}">
                            <el-button v-if="this.permissionList.viewBtn" type="primary" text icon="el-icon-view"
                                       @click="handleView(row)">查看
                            </el-button>
                            <el-button v-if="this.permissionList.editBtn" type="primary" text icon="el-icon-edit"
                                       @click="handleEdit(row)">编辑
                            </el-button>
                            <el-button v-if="this.permissionList.delBtn" type="primary" text icon="el-icon-delete"
                                       @click="rowDel(row)">删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-row>
            <el-row>
                <div class="avue-crud__pagination" style="width:100%">
                    <!-- 分页模块 -->
                    <el-pagination align="right"
                                   background
                                   @size-change="sizeChange"
                                   @current-change="currentChange"
                                   :current-page="page.currentPage"
                                   :page-sizes="[10, 20, 30, 40, 50, 100]"
                                   :page-size="page.pageSize"
                                   layout="total, sizes, prev, pager, next, jumper"
                                   :total="page.total">
                    </el-pagination>
                </div>
            </el-row>
            <!-- 表单模块 -->
            <el-dialog :title="title"
                       v-model="box"
                       width="50%"
                       :before-close="beforeClose"
                       append-to-body>
                <el-form :disabled="view" ref="form" :model="form" label-width="80px">
                    <el-form-item label="科目" prop="subjectId">
                        <el-select v-model="form.subjectId" clearable placeholder="请选择科目">
                            <el-option
                                v-for="item in subjectOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <!-- 表单字段 -->
                    <el-form-item label="题型" prop="type">
                        <el-select v-model="form.type" clearable placeholder="请选择题型">
                            <el-option
                                v-for="item in typeOptions"
                                :key="item.dictKey"
                                :label="item.dictValue"
                                :value="item.dictKey"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="难度" prop="difficulty">
                        <el-select v-model="form.difficulty" clearable placeholder="请选择难度">
                            <el-option
                                v-for="item in difficultyOptions"
                                :key="item.dictKey"
                                :label="item.dictValue"
                                :value="item.dictKey"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="题目内容" prop="content">
                        <el-input type="textarea" :rows="5" v-model="form.content" placeholder="请输入题目内容"/>
                    </el-form-item>

                    <!-- 可视化选项配置 -->
                    <el-form-item label="选项配置" prop="options" v-if="form.type">
                        <QuestionOptionsConfig
                            :question-type="form.type"
                            v-model="form.options"
                            @options-change="onOptionsChange"
                        />
                    </el-form-item>

                    <!-- 传统选项输入（作为备用） -->
                    <el-form-item label="选项(JSON)" prop="options" v-if="!form.type">
                        <el-input type="textarea" :rows="5" v-model="form.options" placeholder="请输入选项(JSON格式)"/>
                    </el-form-item>

                    <!-- 可视化答案配置 -->
                    <el-form-item label="答案配置" prop="answer" v-if="form.type">
                        <QuestionAnswerConfig
                            :question-type="form.type"
                            :options="currentOptions"
                            v-model="form.answer"
                        />
                    </el-form-item>

                    <!-- 传统答案输入（作为备用） -->
                    <el-form-item label="答案" prop="answer" v-if="!form.type">
                        <el-input type="textarea" :rows="3" v-model="form.answer" placeholder="请输入答案"/>
                    </el-form-item>
                    <el-form-item label="解析" prop="explanation">
                        <el-input type="textarea" :rows="5" v-model="form.explanation" placeholder="请输入解析"/>
                    </el-form-item>
                    <el-form-item label="标签" prop="tags">
                        <el-input type="textarea" :rows="5" v-model="form.tags" placeholder="请输入标签(JSON格式)"/>
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-switch v-model="form.status" :active-value="1" :inactive-value="2" active-text="启用"
                                   inactive-text="禁用"></el-switch>
                    </el-form-item>
                </el-form>
                <!-- 表单按钮 -->
                <template #footer>
          <span v-if="!view" class="dialog-footer">
            <el-button type="primary" icon="el-icon-circle-check" @click="handleSubmit">提 交</el-button>
            <el-button icon="el-icon-circle-close" @click="box = false">取 消</el-button>
          </span>
                </template>
            </el-dialog>
        </div>
    </basic-container>
</template>

<script>
import {getList, getDetail, add, update, remove} from "@/api/questions/questions";
import option from "@/option/questions/questions";
import {mapGetters} from "vuex";
import dictBiz from "@/utils/dictBiz";
import {selectSubjectsOptions} from "@/api/subjects/subjects";
import QuestionOptionsConfig from "@/components/question/questionOptionsConfig.vue";
import QuestionAnswerConfig from "@/components/question/questionAnswerConfig.vue";

export default {
    components: {
        QuestionOptionsConfig,
        QuestionAnswerConfig
    },
    data() {
        return {
            height: 0,
            // 弹框标题
            title: '',
            // 是否展示弹框
            box: false,
            // 是否显示查询
            search: true,
            // 加载中
            loading: true,
            // 是否为查看模式
            view: false,
            // 查询信息
            query: {},
            // 分页信息
            page: {
                currentPage: 1,
                pageSize: 10,
                total: 40
            },
            // 表单数据
            form: {},
            // 选择行
            selectionList: [],
            // 表单配置
            option: option,
            // 表单列表
            data: [],

            typeOptions: [],
            difficultyOptions: [],
            subjectOptions: [],

            // 选项配置相关
            currentOptions: {},
        }
    },
    async mounted() {
        await this.loadTypeOptions();
        await this.loadDifficultyOptions();
        await this.loadSubjectOptions();
        this.init();
        this.onLoad(this.page);
    },
    computed: {
        ...mapGetters(["permission"]),
        permissionList() {
            return {
                addBtn: this.validData(this.permission.questions_add, false),
                viewBtn: this.validData(this.permission.questions_view, false),
                delBtn: this.validData(this.permission.questions_delete, false),
                editBtn: this.validData(this.permission.questions_edit, false),
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    methods: {
        init() {
            this.height = this.setPx(document.body.clientHeight - 395);
        },
        searchHide() {
            this.search = !this.search;
        },
        searchChange() {
            this.onLoad(this.page);
        },
        searchReset() {
            this.query = {};
            this.page.currentPage = 1;
            this.onLoad(this.page);
        },
        handleSubmit() {
            // 表单验证
            this.$refs.form.validate((valid) => {
                if (!valid) {
                    this.$message.error('请完善表单信息');
                    return;
                }

                // 验证选项和答案
                if (!this.validateOptionsAndAnswer()) {
                    return;
                }

                if (!this.form.id) {
                    add(this.form).then(() => {
                        this.box = false;
                        this.onLoad(this.page);
                        this.$message({
                            type: "success",
                            message: "操作成功!"
                        });
                    }).catch(error => {
                        this.$message.error('保存失败: ' + (error.message || '未知错误'));
                    });
                } else {
                    update(this.form).then(() => {
                        this.box = false;
                        this.onLoad(this.page);
                        this.$message({
                            type: "success",
                            message: "操作成功!"
                        });
                    }).catch(error => {
                        this.$message.error('更新失败: ' + (error.message || '未知错误'));
                    });
                }
            });
        },
        handleAdd() {
            this.title = '新增'
            this.form = {}
            this.form.status = 1;
            this.currentOptions = {}
            this.box = true
        },
        handleEdit(row) {
            this.title = '编辑'
            this.box = true
            getDetail(row.id).then(res => {
                this.form = res.data.data;
                this.initCurrentOptions();
            });
        },
        handleView(row) {
            this.title = '查看'
            this.view = true;
            this.box = true;
            getDetail(row.id).then(res => {
                this.form = res.data.data;
                this.initCurrentOptions();
            });
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.selectionClear();
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        beforeClose(done) {
            done()
            this.form = {};
            this.currentOptions = {};
            this.view = false;
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.table.clearSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
            this.onLoad(this.page);
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
            this.onLoad(this.page);
        },
        onLoad(page, params = {}) {
            this.loading = true;

            const {
                type,
                difficulty,
                content,
                answer,
                explanation,
                status,
            } = this.query;

            let values = {
                type_equal: type,
                difficulty_equal: difficulty,
                content_like: content,
                answer_like: answer,
                explanation_like: explanation,
                status_equal: status,
            };

            getList(page.currentPage, page.pageSize, values).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        async loadTypeOptions() {
            this.typeOptions = await dictBiz.getMap('questions_type');
        },
        async loadDifficultyOptions() {
            this.difficultyOptions = await dictBiz.getMap('questions_difficulty');
        },
        async loadSubjectOptions() {
            let res = await selectSubjectsOptions();
            this.subjectOptions = res.data.data;
        },

        // 处理选项变化
        onOptionsChange(options) {
            this.currentOptions = options;
        },

        // 初始化当前选项（用于编辑和查看模式）
        initCurrentOptions() {
            if (this.form.options) {
                try {
                    this.currentOptions = JSON.parse(this.form.options);
                } catch (e) {
                    console.warn('解析选项JSON失败:', e);
                    this.currentOptions = {};
                }
            } else {
                this.currentOptions = {};
            }
        },

        // 解析选项JSON字符串为对象
        parseOptions(optionsStr) {
            if (!optionsStr) return {};
            try {
                return JSON.parse(optionsStr);
            } catch (e) {
                console.warn('解析选项JSON失败:', e);
                return {};
            }
        },

        // 验证选项和答案
        validateOptionsAndAnswer() {
            // 验证选项
            if (!this.form.options || this.form.options.trim() === '') {
                this.$message.error('请配置题目选项');
                return false;
            }

            try {
                const options = JSON.parse(this.form.options);
                const optionKeys = Object.keys(options);

                if (optionKeys.length === 0) {
                    this.$message.error('请至少配置一个选项');
                    return false;
                }

                // 检查选项内容是否为空
                const hasEmptyOption = optionKeys.some(key => !options[key] || !options[key].trim());
                if (hasEmptyOption) {
                    this.$message.error('选项内容不能为空');
                    return false;
                }

                // 验证答案
                if (!this.form.answer || this.form.answer.trim() === '') {
                    this.$message.error('请设置题目答案');
                    return false;
                }

                // 验证答案是否在选项中
                const answerKeys = this.form.answer.split(',').map(key => key.trim());
                const invalidAnswers = answerKeys.filter(key => !optionKeys.includes(key));

                if (invalidAnswers.length > 0) {
                    this.$message.error(`答案 "${invalidAnswers.join(', ')}" 不在选项中`);
                    return false;
                }

                // 根据题型验证答案格式
                const isSingleChoice = this.form.type === '1' || this.form.type === 'single' ||
                                     this.form.type === '单选' || this.form.type === '单选题';
                const isMultipleChoice = this.form.type === '2' || this.form.type === 'multiple' ||
                                       this.form.type === '多选' || this.form.type === '多选题';
                const isJudgeChoice = this.form.type === '3' || this.form.type === 'judge' ||
                                    this.form.type === '判断' || this.form.type === '判断题';

                if (isSingleChoice || isJudgeChoice) {
                    // 单选题或判断题只能有一个答案
                    if (answerKeys.length > 1) {
                        this.$message.error('单选题和判断题只能有一个答案');
                        return false;
                    }
                } else if (isMultipleChoice) {
                    // 多选题至少要有一个答案
                    if (answerKeys.length === 0) {
                        this.$message.error('多选题至少要有一个答案');
                        return false;
                    }
                }

            } catch (e) {
                this.$message.error('选项格式不正确，请检查JSON格式');
                return false;
            }

            return true;
        }
    }
};
</script>

<style scoped>
/* 表单对话框样式优化 */
.el-dialog {
  border-radius: 8px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-form-item__label {
  font-weight: 500;
  color: #303133;
}

/* 可视化配置区域样式 */
.visual-config-section {
  background-color: #fafbfc;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.config-section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.config-section-title::before {
  content: '';
  width: 3px;
  height: 16px;
  background-color: #409eff;
  margin-right: 8px;
  border-radius: 2px;
}

/* 选项显示样式 */
.options-display {
  max-width: 300px;
}

.option-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 4px;
  font-size: 13px;
  line-height: 1.4;
}

.option-key {
  font-weight: 600;
  color: #409eff;
  margin-right: 6px;
  flex-shrink: 0;
  min-width: 20px;
}

.option-content {
  color: #606266;
  word-break: break-word;
  flex: 1;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }

  .question-options-config,
  .question-answer-config {
    padding: 12px;
  }

  .options-display {
    max-width: 200px;
  }

  .option-item {
    font-size: 12px;
  }
}
</style>
