<template>
  <basic-container>
    <div class="avue-crud">
      <el-row :hidden="!search" style="padding:6px 18px">
        <!-- 查询模块 -->
        <el-form :inline="true" :model="query">
          <el-form-item label="章节名称:">
            <el-input v-model="query.name" placeholder="请输入章节名称" />
          </el-form-item>
          <el-form-item label="科目名称:">
            <el-input v-model="query.subjectName" placeholder="请输入科目名称" />
          </el-form-item>

          <!-- 查询按钮 -->
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="searchChange">搜 索</el-button>
            <el-button icon="el-icon-delete" @click="searchReset()">清 空</el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row>
        <div class="avue-crud__header">
          <!-- 头部左侧按钮模块 -->
          <div class="avue-crud__left">
            <el-button v-if="this.permissionList.addBtn" type="primary" icon="el-icon-plus" @click="handleAdd">新 增</el-button>
            <el-button v-if="this.permissionList.delBtn" type="danger" icon="el-icon-delete" @click="handleDelete" plain>删 除</el-button>
          </div>
          <!-- 头部右侧按钮模块 -->
          <div class="avue-crud__right">
            <el-button icon="el-icon-refresh" @click="searchChange" circle></el-button>
            <el-button icon="el-icon-search" @click="searchHide" circle></el-button>
          </div>
        </div>
      </el-row>
      <el-row>
        <!-- 列表模块 -->
        <el-table ref="table" v-loading="loading"
                  :data="data"
                  :height="height"
                  style="width: 100%"
                  :border="option.border">
          <el-table-column type="selection" v-if="option.selection" width="55" align="center"></el-table-column>
          <el-table-column type="expand" v-if="option.expand" align="center"></el-table-column>
          <el-table-column v-if="option.index" label="#" type="index" width="50" align="center">
          </el-table-column>
          <template v-for="(item,index) in option.column">
            <!-- table字段 -->
            <el-table-column v-if="item.hide!==true"
                             :prop="item.prop"
                             :label="item.label"
                             :width="item.width"
                             :key="index">
            </el-table-column>
          </template>
          <!-- 操作栏模块 -->
          <el-table-column prop="menu" label="操作" :width="220" align="center">
            <template #="{row}">
              <el-button v-if="this.permissionList.viewBtn" type="primary" text icon="el-icon-view" @click="handleView(row)">查看</el-button>
              <el-button v-if="this.permissionList.editBtn" type="primary" text icon="el-icon-edit" @click="handleEdit(row)">编辑</el-button>
              <el-button type="primary" text icon="el-icon-setting" @click="openConfigDialog(row)">配置题目</el-button>
              <el-button v-if="this.permissionList.delBtn" type="primary" text icon="el-icon-delete" @click="rowDel(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <div class="avue-crud__pagination" style="width:100%">
          <!-- 分页模块 -->
          <el-pagination align="right"
                         background
                         @size-change="sizeChange"
                         @current-change="currentChange"
                         :current-page="page.currentPage"
                         :page-sizes="[10, 20, 30, 40, 50, 100]"
                         :page-size="page.pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="page.total">
          </el-pagination>
        </div>
      </el-row>
      <!-- 表单模块 -->
      <el-dialog :title="title"
                 v-model="box"
                 width="50%"
                 append-to-body>
        <el-form :disabled="view" ref="form" :model="form" label-width="80px">
          <!-- 表单字段 -->
          <el-form-item label="章节名称" prop="name" required>
            <el-input v-model="form.name" placeholder="请输入章节名称" />
          </el-form-item>
          <el-form-item label="所属科目" prop="subjectId" required>
            <el-select v-model="form.subjectId" filterable placeholder="请选择所属科目" style="width:100%">
              <el-option v-for="s in subjectsOptions" :key="s.id" :label="s.name" :value="s.id" />
            </el-select>
          </el-form-item>

          <el-form-item label="排序号" prop="orderNo" required>
            <el-input-number v-model="form.orderNo" :min="1" :step="1" style="width:100%" />
          </el-form-item>
          <el-form-item label="题目总数">
            <el-input v-model="form.totalQuestions" disabled />
          </el-form-item>

        </el-form>
        <!-- 表单按钮 -->
        <template #footer>
          <span v-if="!view" class="dialog-footer">
            <el-button type="primary" icon="el-icon-circle-check" @click="handleSubmit">提 交</el-button>
            <el-button icon="el-icon-circle-close" @click="box = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 配置章节题目对话框 -->
      <el-dialog title="配置章节题目" v-model="configDialog.visible" width="70%" append-to-body>
        <div style="margin-bottom:10px;text-align:right">
          <el-button type="primary" icon="el-icon-plus" @click="openSelectQuestionDialog">添加题目</el-button>
          <el-button type="success" icon="el-icon-check" @click="saveChapterItems" :loading="configDialog.saving">保存配置</el-button>
        </div>
        <el-table :data="configDialog.items" border style="width:100%">
          <el-table-column label="#" type="index" width="60" />
          <el-table-column label="题目内容" prop="content" />
          <el-table-column label="题型" prop="typeName" width="100" />
          <el-table-column label="科目" prop="subjectName" width="120" />
          <el-table-column label="排序" width="140">
            <template #="{row}">
              <el-input-number style="width:90px;" v-model="row.orderNo" :min="1" :step="1" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #="{row,$index}">
              <el-button type="text" icon="el-icon-delete" @click="removeConfigItem($index)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>

      <!-- 选择题目对话框 -->
      <el-dialog title="选择题目" v-model="selectDialog.visible" width="80%" append-to-body>
        <div style="margin-bottom:10px">
          <el-form :inline="true" :model="selectDialog.query">
            <el-form-item label="科目">
              <el-select v-model="selectDialog.query.subjectId" clearable filterable placeholder="全部科目" style="width:180px">
                <el-option v-for="s in subjectsOptions" :key="s.id" :label="s.name" :value="s.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="题型">
              <el-select v-model="selectDialog.query.type" clearable placeholder="全部题型" style="width:140px">
                <el-option label="单选" :value="'single'" />
                <el-option label="多选" :value="'multiple'" />
                <el-option label="判断" :value="'truefalse'" />
              </el-select>
            </el-form-item>
            <el-form-item label="难度">
              <el-select v-model="selectDialog.query.difficulty" clearable placeholder="全部难度" style="width:140px">
                <el-option label="容易" :value="'easy'" />
                <el-option label="中等" :value="'medium'" />
                <el-option label="困难" :value="'hard'" />
              </el-select>
            </el-form-item>
            <el-form-item label="关键词">
              <el-input v-model="selectDialog.query.content" placeholder="题目包含的关键词" style="width:240px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="loadQuestionList">搜索</el-button>
              <el-button icon="el-icon-refresh" @click="() => { selectDialog.query = { subjectId: null, type: null, difficulty: null, content: '' }; loadQuestionList(); }">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="selectDialog.data" border @selection-change="selectDialogSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column label="题目内容" prop="content" />
          <el-table-column label="题型" prop="type" width="100" />
          <el-table-column label="科目" prop="subjectName" width="120" />
          <el-table-column label="难度" prop="difficulty" width="100" />
        </el-table>
        <div style="margin-top:10px;text-align:right">
          <el-pagination
            background
            @size-change="selectSizeChange"
            @current-change="selectCurrentChange"
            :current-page="selectDialog.page.currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="selectDialog.page.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="selectDialog.page.total">
          </el-pagination>
        </div>
        <template #footer>
          <el-button type="primary" icon="el-icon-plus" @click="confirmAddQuestions">添加到章节</el-button>
          <el-button @click="selectDialog.visible=false">关闭</el-button>
        </template>
      </el-dialog>

    </div>
  </basic-container>
</template>

<script>
import { getList, getDetail, add, update, remove } from "@/api/subjects/subjectChapter";
import { getList as getQuestionChapterList, batchSave as batchSaveQuestionChapter } from "@/api/questions/questionChapter";
import { getList as getQuestionsList } from "@/api/questions/questions";
import option from "@/option/subjects/subjectChapter";
import { mapGetters } from "vuex";
import { selectSubjectsOptions } from "@/api/subjects/subjects";


export default {
  data () {
    return {
      height: 0,
      // 弹框标题
      title: '',
      // 是否展示弹框
      box: false,
      // 是否显示查询
      search: true,
      // 加载中
      loading: true,
      // 是否为查看模式
      view: false,
      // 科目下拉选项
      subjectsOptions: [],

      // 查询信息
      // 配置题目对话框数据
      configDialog: {
        visible: false,
        chapterId: null,
        items: [], // {questionId, content, typeName, subjectName, orderNo}
        saving: false,
      },
      // 选择题目对话框数据
      selectDialog: {
        visible: false,
        query: { subjectId: null, type: null, difficulty: null, content: '' },
        data: [],
        selection: [],
        page: { currentPage: 1, pageSize: 10, total: 0 }
      },

      query: {},
      // 分页信息
      page: {
        currentPage: 1,
        pageSize: 10,
        total: 40
      },
      // 表单数据
      form: {},
      // 选择行
      selectionList: [],
      // 表单配置
      option: option,
      // 表单列表
      data: [],
    }
  },

  mounted() {
    this.init();
    this.loadSubjectsOptions();
    this.onLoad(this.page);
  },
  computed: {
    ...mapGetters(["permission"]),

    permissionList() {
      return {
        addBtn: this.validData(this.permission.subjectChapter_add, false),
        viewBtn: this.validData(this.permission.subjectChapter_view, false),
        delBtn: this.validData(this.permission.subjectChapter_delete, false),
        editBtn: this.validData(this.permission.subjectChapter_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    loadSubjectsOptions(name = '') {
      selectSubjectsOptions(name).then(res => {
        this.subjectsOptions = res.data.data || []
      })
    },
    init() {
      this.height = this.setPx(document.body.clientHeight - 340);
    },
    searchHide() {
      this.search = !this.search;
    },
    searchChange() {
      this.onLoad(this.page);
    },
    searchReset() {
      this.query = {};
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    handleSubmit() {
      if (!this.form.id) {
        add(this.form).then(() => {
          this.box = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
      } else {
        update(this.form).then(() => {
          this.box = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        })
      }
    },
    handleAdd(){
      this.title = '新增'
      this.form = {}
      this.box = true
    },
    handleEdit(row) {
      this.title = '编辑'
      this.box = true
      getDetail(row.id).then(res => {
        this.form = res.data.data;
      });
    },
    handleView(row) {
      this.title = '查看'
      this.view = true;
      this.box = true;
      getDetail(row.id).then(res => {
        this.form = res.data.data;
      });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.selectionClear();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
      },

      resetSelectQuery() {
        this.selectDialog.query = { subjectId: null, type: null, difficulty: null, content: '' };
        this.selectDialog.page.currentPage = 1;
        this.loadQuestionList();
      },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return remove(row.id)
      }).then(() => {
        this.onLoad(this.page)
        this.$message({ type: 'success', message: '操作成功!' })
      })
    },

    // 打开配置对话框
    openConfigDialog(row) {
      this.configDialog.chapterId = row.id
      this.configDialog.items = []
      this.configDialog.visible = true
      // 1) 查出关联的 questionId 列表
      getQuestionChapterList(1, 1000, { chapterId_equal: row.id }).then(res => {
        const records = res.data.data?.records || []
        const ids = records.map(x => x.questionId)
        if (ids.length === 0) return
        // 2) 批量查题目详情（使用 id_in ）
        return getQuestionsList(1, 1000, { id_in: ids.join(',') }).then(qres => {
          const items = (qres.data.data?.records || []).map((q, idx) => ({
            questionId: q.id,
            content: q.content,
            typeName: q.type,
            subjectName: q.subjectName,
            orderNo: idx + 1,
          }))
          const mapOrder = new Map()
          for (const r of records) {
            mapOrder.set(r.questionId, r.orderNo || 1)
          }
          for (const it of items) {
            if (mapOrder.has(it.questionId)) it.orderNo = mapOrder.get(it.questionId)
          }
          items.sort((a,b) => (a.orderNo||1) - (b.orderNo||1))
          this.configDialog.items = items
        })
      })
    },

    openSelectQuestionDialog() {
      this.selectDialog.visible = true
      this.selectDialog.page.currentPage = 1
      this.loadQuestionList()
    },

    loadQuestionList() {
      const { subjectId, type, difficulty, content } = this.selectDialog.query
      getQuestionsList(this.selectDialog.page.currentPage, this.selectDialog.page.pageSize, { subjectId, type, difficulty, content }).then(res => {
        const data = res.data.data
        this.selectDialog.page.total = data.total
        this.selectDialog.data = data.records
      })
    },

    selectSizeChange(ps) {
      this.selectDialog.page.pageSize = ps
      this.loadQuestionList()
    },

    selectCurrentChange(cp) {
      this.selectDialog.page.currentPage = cp
      this.loadQuestionList()
    },

    selectDialogSelectionChange(list) {
      this.selectDialog.selection = list
    },

    confirmAddQuestions() {
      const existing = new Set(this.configDialog.items.map(x => x.questionId))
      let maxOrder = this.configDialog.items.reduce((m, x) => Math.max(m, x.orderNo || 0), 0)
      for (const q of this.selectDialog.selection) {
        if (existing.has(q.id)) continue
        maxOrder += 1
        this.configDialog.items.push({ questionId: q.id, content: q.content, typeName: q.type, subjectName: q.subjectName, orderNo: maxOrder })
      }
      this.selectDialog.visible = false
    },

    removeConfigItem(index) {
      this.configDialog.items.splice(index, 1)
    },

    saveChapterItems() {
      const payload = this.configDialog.items.map(x => ({ questionId: x.questionId, orderNo: x.orderNo }))
      if (!this.configDialog.chapterId) return
      this.configDialog.saving = true
      batchSaveQuestionChapter(this.configDialog.chapterId, payload).then(() => {
        this.$message.success('保存成功')
        this.configDialog.visible = false
        this.onLoad(this.page)
      }).finally(() => {
        this.configDialog.saving = false
      })
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.table.clearSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onLoad(this.page);
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onLoad(this.page);
    },
    onLoad(page) {
      this.loading = true;

      const { name, subjectName } = this.query;
      let values = {
        name_like: name,
        subjectName_like: subjectName,
      };

      getList(page.currentPage, page.pageSize, values).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    }
  }
};
</script>
