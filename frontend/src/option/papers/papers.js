export default {
  expand: false,
  index: false,
  border: true,
  selection: true,
  column: [
    {
      label: "主键ID",
      prop: "id",
      display: false,
      hide: true,
    },
    {
      label: "试卷名称",
      prop: "name",
      search: true,
        width: 150,
        fixed: true,
    },
    {
      label: "试卷描述",
      prop: "description",
        hide: true,
    },
    {
      label: "考试时长(分钟)",
      prop: "durationMinutes",
        width: 140,
    },
    {
      label: "及格分数",
      prop: "passScore",
        width: 90,
    },
    {
      label: "总分",
      prop: "totalScore",
        width: 90,
    },
    {
      label: "打乱题序",
      prop: "shuffle",
      search: true,
        width: 90,
    },
    {
      label: "允许暂停",
      prop: "allowPause",
      search: true,
        width: 90,
    },
    {
      label: "显示结果详情",
      prop: "showResultDetail",
      search: true,
        width: 120,
    },
    {
      label: "发布时间",
      prop: "publishAt",
      format: "YYYY-MM-DD",
      valueFormat: "YYYY-MM-DD HH:mm:ss",
      search: true,
        width: 120,
    },
    {
      label: "租户ID",
      prop: "tenantId",
      display: false,
      hide: true,
    },
    {
      label: "状态",
      prop: "status",
      search: true,
      width: 90,
    },
    {
      label: "创建人",
      prop: "createUser",
      display: false,
      hide: true,
    },
      {
          label: "创建人",
          prop: "createUserName",
          display: false,
      },
    {
      label: "创建时间",
      prop: "createTime",
      display: false,
      hide: true,
    },
    {
      label: "更新人",
      prop: "updateUser",
      display: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      display: false,
      hide: true,
    },
    {
      label: "是否删除(0-未删除,1-已删除)",
      prop: "isDeleted",
      display: false,
      hide: true,
    },
  ]
}
