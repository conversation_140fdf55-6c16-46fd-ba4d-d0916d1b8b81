export default {
  expand: false,
  index: false,
  border: true,
  selection: true,
  column: [
    {
      label: "主键ID",
      prop: "id",
      display: false,
      hide: true,
    },
    {
      label: "科目ID",
      prop: "subjectId",
      display: false,
      hide: true,
    },
    {
      label: "科目名称",
      prop: "subjectName",
      search: true,
    },
    {
      label: "题型",
      prop: "type",
      search: true,
    },
    {
      label: "难度",
      prop: "difficulty",
      search: true,
    },
    {
      label: "题目内容",
      prop: "content",
      span: 24,
      search: true,
    },
    {
      label: "选项",
      prop: "options",
      span: 24,
    },
    {
      label: "答案",
      prop: "answer",
      span: 24,
      search: true,
    },
    {
      label: "解析",
      prop: "explanation",
      span: 24,
      search: true,
    },
    {
      label: "标签",
      prop: "tags",
      span: 24,
        hide: true,
    },
    {
      label: "状态",
      prop: "status",
      search: true,
    },
    {
      label: "租户ID",
      prop: "tenantId",
      display: false,
      hide: true,
    },
    {
      label: "创建人",
      prop: "createUser",
      display: false,
      hide: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      display: false,
      hide: true,
    },
    {
      label: "更新人",
      prop: "updateUser",
      display: false,
      hide: true,
    },
    {
      label: "更新时间",
      prop: "updateTime",
      display: false,
      hide: true,
    },
    {
      label: "是否删除(0-未删除,1-已删除)",
      prop: "isDeleted",
      display: false,
      hide: true,
    },
  ]
}
