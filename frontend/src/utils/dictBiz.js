import {getDictionary} from "@/api/system/dictbiz";
import keyBy from 'lodash/keyBy'
import {ElLoading} from 'element-plus'

export default {
    loading: null,
    async getMap(code, key = 'dictKey') {
        this.loading = ElLoading.service({fullscreen: true})
        try {
            let resData = (await getDictionary({code})).data;
            if (resData && resData.data) {
                return keyBy(resData.data, key);
            }
            return null;
        } catch (ex) {
            return null;
        } finally {
            this.loading.close()
        }
    },
    mapToOptions(mapList) {
        if (!mapList) return [];
        return Object.keys(mapList).map(key => ({
            label: mapList[key]?.dictValue || String(mapList[key]?.dictKey || key),
            value: mapList[key]?.dictKey ?? key,
        }));
    }
}
