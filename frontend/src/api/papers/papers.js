import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/exam-papers/papers/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/exam-papers/papers/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/exam-papers/papers/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/exam-papers/papers/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/exam-papers/papers/submit',
    method: 'post',
    data: row
  })
}

export const createDraft = (row) => {
  return request({
    url: '/exam-papers/papers/create-draft',
    method: 'post',
    data: row
  })
}

export const publishPaper = (paperId) => {
  return request({
    url: `/exam-papers/papers/${paperId}/publish`,
    method: 'post'
  })
}

export const archivePaper = (paperId) => {
  return request({
    url: `/exam-papers/papers/${paperId}/archive`,
    method: 'post'
  })
}

export const getPaperItems = (paperId) => {
  return request({
    url: `/exam-papers/paper-items/by-paper/${paperId}`,
    method: 'get'
  })
}

export const savePaperItems = (paperId, items) => {
  return request({
    url: `/exam-papers/paper-items/batch-save/${paperId}`,
    method: 'post',
    data: items
  })
}

