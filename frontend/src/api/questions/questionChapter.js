import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/blade-questionchapter/questionChapter/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/blade-questionchapter/questionChapter/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/blade-questionchapter/questionChapter/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/blade-questionchapter/questionChapter/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/blade-questionchapter/questionChapter/submit',
    method: 'post',
    data: row
  })
}

export const batchSave = (chapterId, items) => {
  return request({
    url: `/blade-questionchapter/questionChapter/batch-save/${chapterId}`,
    method: 'post',
    data: items
  })
}


