import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/exam-questions/questions/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/exam-questions/questions/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/exam-questions/questions/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/exam-questions/questions/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/exam-questions/questions/submit',
    method: 'post',
    data: row
  })
}

// 更新权限
export const updatePermissions = (ids, roleIds) => {
  return request({
    url: '/exam-questions/questions/update-permissions',
    method: 'post',
    data: {
      ids,
      roleIds
    }
  })
}

