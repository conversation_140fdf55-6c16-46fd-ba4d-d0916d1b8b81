import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/exam-feedback/feedback/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/exam-feedback/feedback/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/exam-feedback/feedback/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/exam-feedback/feedback/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/exam-feedback/feedback/submit',
    method: 'post',
    data: row
  })
}

