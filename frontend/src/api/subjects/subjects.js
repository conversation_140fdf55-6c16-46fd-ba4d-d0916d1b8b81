import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/exam-subjects/subjects/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/exam-subjects/subjects/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/exam-subjects/subjects/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/exam-subjects/subjects/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/exam-subjects/subjects/submit',
    method: 'post',
    data: row
  })
}

// 更新权限
export const updatePermissions = (ids, roleIds) => {
  return request({
    url: '/exam-subjects/subjects/update-permissions',
    method: 'post',
    data: {
      ids,
      roleIds
    }
  })
}

export const selectSubjectsOptions = (name) => {
    return request({
        url: '/exam-subjects/subjects/select-subjects-options',
        method: 'get',
        params: {
            name
        }
    })
}
