CREATE TABLE `exam_subjects` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` varchar(100) DEFAULT NULL COMMENT '科目名称',
    `code` varchar(50) DEFAULT NULL COMMENT '科目编码',
    `order_no` int DEFAULT 0 COMMENT '排序号',
    `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `status` int(11) DEFAULT NULL COMMENT '状态',
    `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除(0-未删除,1-已删除)',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='科目表';


CREATE TABLE `exam_questions` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `subject_id` bigint DEFAULT NULL COMMENT '科目ID',
    `type` varchar(20) DEFAULT NULL COMMENT '题型(单选,多选,判断)',
    `difficulty` varchar(20) DEFAULT NULL COMMENT '难度(简单,中等,困难)',
    `content` text DEFAULT NULL COMMENT '题目内容(支持富文本)',
    `options` json DEFAULT NULL COMMENT '选项(JSON格式)',
    `answer` varchar(500) DEFAULT NULL COMMENT '答案',
    `explanation` text DEFAULT NULL COMMENT '解析',
    `tags` json DEFAULT NULL COMMENT '标签(JSON格式)',
    `status` int(11) DEFAULT NULL COMMENT '状态(1-启用,2-禁用)',
    `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除(0-未删除,1-已删除)',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='题目表';

CREATE TABLE `exam_papers` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` varchar(200) DEFAULT NULL COMMENT '试卷名称',
    `description` text DEFAULT NULL COMMENT '试卷描述',
    `duration_minutes` int DEFAULT NULL COMMENT '考试时长(分钟)',
    `pass_score` decimal(5,2) DEFAULT NULL COMMENT '及格分数',
    `total_score` decimal(5,2) DEFAULT NULL COMMENT '总分',
    `shuffle` tinyint(1) DEFAULT NULL COMMENT '是否打乱题目顺序(0-否,1-是)',
    `allow_pause` tinyint(1) DEFAULT NULL COMMENT '是否允许暂停(0-否,1-是)',
    `show_result_detail` tinyint(1) DEFAULT NULL COMMENT '是否显示结果详情(0-否,1-是)',
    `publish_at` datetime DEFAULT NULL COMMENT '发布时间',
    `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
    `status` int(11) DEFAULT NULL COMMENT '状态(1-草稿,2-已发布,3-已归档)',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除(0-未删除,1-已删除)',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='试卷表';

CREATE TABLE `exam_paper_items` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `paper_id` bigint DEFAULT NULL COMMENT '试卷ID',
    `question_id` bigint DEFAULT NULL COMMENT '题目ID',
    `score` decimal(5,2) DEFAULT NULL COMMENT '题目分值',
    `order_no` int DEFAULT NULL COMMENT '题目排序号',
    `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `status` int(11) DEFAULT NULL COMMENT '状态',
    `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除(0-未删除,1-已删除)',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='试卷题目关联表';


CREATE TABLE `exam_feedback` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint DEFAULT NULL COMMENT '用户ID',
    `type` varchar(20) DEFAULT NULL COMMENT '反馈类型(Bug报告,功能建议,投诉,其他)',
    `title` varchar(200) DEFAULT NULL COMMENT '反馈标题',
    `content` text DEFAULT NULL COMMENT '反馈内容',
    `status` int(11) DEFAULT NULL COMMENT '状态(1-待处理,2-处理中,3-已解决,4-已关闭)',
    `attachments` json DEFAULT NULL COMMENT '附件信息(JSON格式)',
    `priority` varchar(20) DEFAULT NULL COMMENT '优先级(低,中,高,紧急)',
    `assigned_to` bigint DEFAULT NULL COMMENT '分配给(处理人ID)',
    `resolved_at` datetime DEFAULT NULL COMMENT '解决时间',
    `closed_at` datetime DEFAULT NULL COMMENT '关闭时间',
    `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除(0-未删除,1-已删除)',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='意见反馈表';

CREATE TABLE `exam_feedback_replies` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `feedback_id` bigint DEFAULT NULL COMMENT '反馈ID',
    `replier_id` bigint DEFAULT NULL COMMENT '回复者ID',
    `content` text DEFAULT NULL COMMENT '回复内容',
    `is_internal` tinyint(1) DEFAULT 0 COMMENT '是否内部回复(0-否,1-是)',
    `attachments` json DEFAULT NULL COMMENT '附件信息(JSON格式)',
    `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `status` int(11) DEFAULT NULL COMMENT '状态',
    `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除(0-未删除,1-已删除)',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='反馈回复表';


-- 科目章节表
CREATE TABLE `exam_subject_chapter` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
     `subject_id` BIGINT DEFAULT NULL COMMENT '科目ID（关联 subjects.id）',
     `name` VARCHAR(128) DEFAULT NULL COMMENT '章节名称',
     `order_no` INT DEFAULT NULL DEFAULT 0 COMMENT '排序号（升序）',
     `total_questions` INT DEFAULT NULL DEFAULT 0 COMMENT '章节题目总数快照（便于加速展示）',
     `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
     `create_user` bigint DEFAULT NULL COMMENT '创建人',
     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
     `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
     `update_user` bigint DEFAULT NULL COMMENT '更新人',
     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
     `status` int(11) DEFAULT NULL COMMENT '状态',
     `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除：0未删除，1已删除',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='科目章节表';

-- 用户科目进度表（可选预聚合）
CREATE TABLE `exam_user_subject_progress` (
                                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
       `user_id` BIGINT DEFAULT NULL COMMENT '用户ID',
       `subject_id` BIGINT DEFAULT NULL COMMENT '科目ID（关联 subjects.id）',
       `done_count` INT DEFAULT NULL DEFAULT 0 COMMENT '已作答题目数（练习+考试口径或仅练习，按业务约定）',
       `correct_count` INT DEFAULT NULL DEFAULT 0 COMMENT '作答正确题目数',
       `total_questions` INT DEFAULT NULL DEFAULT 0 COMMENT '科目题目总数快照',
       `last_practice_at` DATETIME NULL COMMENT '最近练习时间',
       `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
       `create_user` bigint DEFAULT NULL COMMENT '创建人',
       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
       `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
       `update_user` bigint DEFAULT NULL COMMENT '更新人',
       `update_time` datetime DEFAULT NULL COMMENT '更新时间',
       `status` int(11) DEFAULT NULL COMMENT '状态',
       `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除：0未删除，1已删除',
       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户科目进度预聚合表';

-- 练习会话表
CREATE TABLE `exam_practice_session` (
      `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID（练习会话ID）',
      `user_id` BIGINT DEFAULT NULL COMMENT '用户ID',
      `subject_id` BIGINT DEFAULT NULL COMMENT '科目ID（关联 subjects.id）',
      `paper_id` BIGINT DEFAULT NULL COMMENT '试卷ID（关联 papers.id,用于按试卷练习）',
      `mode` VARCHAR(16) DEFAULT NULL COMMENT '练习模式：sequence|random|wrong',
      `question_total` INT DEFAULT 0 COMMENT '本次会话题目总量',
      `current_index` INT DEFAULT 0 COMMENT '当前进度指针/已答数量（0 基）',
      `question_ids` JSON NULL COMMENT '题目ID有序列表快照（JSON 数组，固定题序）',
      `start_time` DATETIME DEFAULT NULL COMMENT '开始时间',
      `end_time` DATETIME NULL COMMENT '结束时间（完成/放弃时填充）',
      `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
      `create_user` bigint DEFAULT NULL COMMENT '创建人',
      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
      `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
      `update_user` bigint DEFAULT NULL COMMENT '更新人',
      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
      `status` int(11) DEFAULT NULL COMMENT '状态',
      `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除：0未删除，1已删除',
      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='练习会话表';

-- 练习答案表
CREATE TABLE `exam_practice_answer` (
     `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
     `session_id` BIGINT DEFAULT NULL COMMENT '练习会话ID（关联 practice_session.id）',
     `user_id` BIGINT DEFAULT NULL COMMENT '用户ID',
     `subject_id` BIGINT DEFAULT NULL COMMENT '科目ID（冗余，便于统计与查询）',
     `question_id` BIGINT DEFAULT NULL COMMENT '题目ID（关联 questions.id）',
     `values_json` JSON DEFAULT NULL COMMENT '用户作答选项（JSON 数组，单选长度为1，多选为多个）',
     `is_correct` TINYINT NULL COMMENT '是否作答正确：1正确，0错误，NULL未判分',
     `duration_sec` INT DEFAULT NULL DEFAULT 0 COMMENT '该题耗时（秒）',
     `submitted_at` DATETIME DEFAULT NULL COMMENT '提交时间',
     `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
     `create_user` bigint DEFAULT NULL COMMENT '创建人',
     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
     `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
     `update_user` bigint DEFAULT NULL COMMENT '更新人',
     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
     `status` int(11) DEFAULT NULL COMMENT '状态',
     `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除：0未删除，1已删除',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='练习答案记录表';

-- 考试会话表
CREATE TABLE `exam_session` (
      `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID（考试会话ID）',
      `user_id` BIGINT DEFAULT NULL COMMENT '用户ID',
      `paper_id` BIGINT DEFAULT NULL COMMENT '试卷ID（关联 papers.id）',
      `question_total` INT DEFAULT NULL DEFAULT 0 COMMENT '本次考试题目总量',
      `start_time` DATETIME DEFAULT NULL COMMENT '开始时间',
      `end_time` DATETIME NULL COMMENT '结束/提交时间',
      `expire_time` DATETIME NULL COMMENT '截止时间（倒计时结束点）',
      `question_ids` JSON NULL COMMENT '题目ID有序列表快照（JSON 数组）',
      `score` DECIMAL(5,2) NULL COMMENT '考试得分（提交计算后写入）',
      `accuracy` DECIMAL(5,2) NULL COMMENT '正确率百分比（0-100）',
      `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
      `create_user` bigint DEFAULT NULL COMMENT '创建人',
      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
      `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
      `update_user` bigint DEFAULT NULL COMMENT '更新人',
      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
      `status` int(11) DEFAULT NULL COMMENT '状态',
      `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除：0未删除，1已删除',
      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='考试会话表';

-- 考试答案表
CREATE TABLE `exam_answer` (
        `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `session_id` BIGINT DEFAULT NULL COMMENT '考试会话ID（关联 exam_session.id）',
        `user_id` BIGINT DEFAULT NULL COMMENT '用户ID',
        `paper_id` BIGINT DEFAULT NULL COMMENT '试卷ID（关联 papers.id）',
        `question_id` BIGINT DEFAULT NULL COMMENT '题目ID（关联 questions.id）',
        `values_json` JSON DEFAULT NULL COMMENT '用户作答选项（JSON 数组）',
        `duration_sec` INT DEFAULT 0 COMMENT '该题耗时（秒）',
        `submitted_at` DATETIME DEFAULT NULL COMMENT '提交时间（保存草稿亦记录）',
        `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
        `create_user` bigint DEFAULT NULL COMMENT '创建人',
        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
        `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
        `update_user` bigint DEFAULT NULL COMMENT '更新人',
        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
        `status` int(11) DEFAULT NULL COMMENT '状态',
        `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除：0未删除，1已删除',
        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='考试答案记录表';

CREATE TABLE `exam_question_chapter`
(
    `id`          BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `question_id` BIGINT                       NULL COMMENT '题目ID（关联 exam_questions.id）',
    `chapter_id`  BIGINT                       NULL COMMENT '章节ID（关联 exam_subject_chapter.id）',
    `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
    `create_user` bigint DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
    `update_user` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `status` int(11) DEFAULT NULL COMMENT '状态',
    `is_deleted` tinyint(1) DEFAULT 0 COMMENT '逻辑删除：0未删除，1已删除',
    PRIMARY KEY (`id`)
)
    COMMENT '题目-章节关联中间表';
