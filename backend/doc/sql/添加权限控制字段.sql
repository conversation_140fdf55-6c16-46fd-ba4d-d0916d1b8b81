-- 添加权限控制字段到相关表
-- 为每个表添加 role_ids 字段，用于存储角色ID数组（逗号分隔字符串格式）

-- 修改科目表
ALTER TABLE `exam_subjects`
ADD COLUMN `role_ids` VARCHAR(500) NULL COMMENT '权限角色ID数组(逗号分隔字符串格式)' AFTER `order_no`;

-- 修改题目表
ALTER TABLE `exam_questions`
ADD COLUMN `role_ids` VARCHAR(500) NULL COMMENT '权限角色ID数组(逗号分隔字符串格式)' AFTER `tags`;

-- 修改试卷表
ALTER TABLE `exam_papers`
ADD COLUMN `role_ids` VARCHAR(500) NULL COMMENT '权限角色ID数组(逗号分隔字符串格式)' AFTER `publish_at`;

-- 修改考试答案表
ALTER TABLE `exam_answer`
ADD COLUMN `role_ids` VARCHAR(500) NULL COMMENT '权限角色ID数组(逗号分隔字符串格式)' AFTER `submitted_at`;

-- 修改科目章节表
ALTER TABLE `exam_subject_chapter`
ADD COLUMN `role_ids` VARCHAR(500) NULL COMMENT '权限角色ID数组(逗号分隔字符串格式)' AFTER `total_questions`;

-- 更新现有数据，将role_ids设置为空字符串
UPDATE `exam_subjects` SET `role_ids` = '' WHERE `role_ids` IS NULL;
UPDATE `exam_questions` SET `role_ids` = '' WHERE `role_ids` IS NULL;
UPDATE `exam_papers` SET `role_ids` = '' WHERE `role_ids` IS NULL;
UPDATE `exam_answer` SET `role_ids` = '' WHERE `role_ids` IS NULL;
UPDATE `exam_subject_chapter` SET `role_ids` = '' WHERE `role_ids` IS NULL;
