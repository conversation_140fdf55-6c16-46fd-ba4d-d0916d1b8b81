-- 测试权限控制功能的SQL语句
-- 用于验证权限控制是否正常工作

-- 1. 插入测试数据
-- 插入测试角色数据（假设角色ID为1,2,3）

-- 插入测试科目数据
INSERT INTO `exam_subjects` (`id`, `name`, `code`, `order_no`, `role_ids`, `tenant_id`, `create_user`, `create_time`, `status`, `is_deleted`) VALUES
(1, '数学', 'MATH', 1, '1,2', '000000', 1, NOW(), 1, 0),
(2, '语文', 'CHINESE', 2, '2,3', '000000', 1, NOW(), 1, 0),
(3, '英语', 'ENGLISH', 3, '1', '000000', 1, NOW(), 1, 0),
(4, '物理', 'PHYSICS', 4, '', '000000', 1, NOW(), 1, 0);

-- 插入测试题目数据
INSERT INTO `exam_questions` (`id`, `subject_id`, `type`, `difficulty`, `content`, `options`, `answer`, `explanation`, `tags`, `role_ids`, `status`, `tenant_id`, `create_user`, `create_time`, `is_deleted`) VALUES
(1, 1, '单选', '简单', '1+1等于多少？', '["A.1","B.2","C.3","D.4"]', 'B', '基础数学运算', '["数学","基础"]', '1,2', 1, '000000', 1, NOW(), 0),
(2, 1, '单选', '中等', '求导数', '["A.x","B.1","C.0","D.2x"]', 'B', '导数基础', '["数学","导数"]', '2', 1, '000000', 1, NOW(), 0),
(3, 2, '单选', '简单', '下列哪个是汉字？', '["A.A","B.中","C.1","D.@"]', 'B', '汉字识别', '["语文","汉字"]', '2,3', 1, '000000', 1, NOW(), 0),
(4, 3, '单选', '简单', 'Hello的中文意思是？', '["A.再见","B.你好","C.谢谢","D.对不起"]', 'B', '英语基础词汇', '["英语","词汇"]', '1', 1, '000000', 1, NOW(), 0),
(5, 4, '单选', '困难', '牛顿第一定律是什么？', '["A.惯性定律","B.加速度定律","C.作用反作用定律","D.万有引力定律"]', 'A', '物理基础定律', '["物理","定律"]', '', 1, '000000', 1, NOW(), 0);

-- 插入测试试卷数据
INSERT INTO `exam_papers` (`id`, `name`, `description`, `duration_minutes`, `pass_score`, `total_score`, `shuffle`, `allow_pause`, `show_result_detail`, `publish_at`, `role_ids`, `tenant_id`, `status`, `create_user`, `create_time`, `is_deleted`) VALUES
(1, '数学基础测试', '数学基础知识测试', 60, 60.00, 100.00, 0, 1, 1, NOW(), '1,2', '000000', 2, 1, NOW(), 0),
(2, '语文阅读理解', '语文阅读理解能力测试', 90, 70.00, 100.00, 0, 1, 1, NOW(), '2,3', '000000', 2, 1, NOW(), 0),
(3, '英语词汇测试', '英语基础词汇测试', 45, 80.00, 100.00, 1, 0, 1, NOW(), '1', '000000', 2, 1, NOW(), 0),
(4, '综合能力测试', '多学科综合能力测试', 120, 75.00, 150.00, 0, 1, 1, NOW(), '', '000000', 2, 1, NOW(), 0);

-- 插入测试答案数据
INSERT INTO `exam_answer` (`id`, `session_id`, `user_id`, `paper_id`, `question_id`, `values_json`, `duration_sec`, `submitted_at`, `role_ids`, `tenant_id`, `create_user`, `create_time`, `status`, `is_deleted`) VALUES
(1, 1, 1, 1, 1, '["B"]', 30, NOW(), '1,2', '000000', 1, NOW(), 1, 0),
(2, 1, 1, 1, 2, '["B"]', 45, NOW(), '2', '000000', 1, NOW(), 1, 0),
(3, 2, 2, 2, 3, '["B"]', 25, NOW(), '2,3', '000000', 2, NOW(), 1, 0),
(4, 3, 3, 3, 4, '["B"]', 20, NOW(), '1', '000000', 3, NOW(), 1, 0),
(5, 4, 4, 4, 5, '["A"]', 60, NOW(), '', '000000', 4, NOW(), 1, 0);

-- 2. 测试权限控制查询
-- 测试角色ID为1的用户能看到的数据

-- 科目查询（角色1应该能看到：数学、英语、物理）
SELECT * FROM exam_subjects 
WHERE is_deleted = 0
AND (
    role_ids IS NULL 
    OR role_ids = '' 
    OR FIND_IN_SET('1', role_ids) > 0
);

-- 题目查询（角色1应该能看到：数学题1、英语题4、物理题5）
SELECT eq.*, es.name as subject_name
FROM exam_questions as eq
LEFT JOIN exam_subjects as es on eq.subject_id = es.id
WHERE eq.is_deleted = 0
AND (
    eq.role_ids IS NULL 
    OR eq.role_ids = '' 
    OR FIND_IN_SET('1', eq.role_ids) > 0
);

-- 试卷查询（角色1应该能看到：数学基础测试、英语词汇测试、综合能力测试）
SELECT * FROM exam_papers 
WHERE is_deleted = 0
AND (
    role_ids IS NULL 
    OR role_ids = '' 
    OR FIND_IN_SET('1', role_ids) > 0
);

-- 答案查询（角色1应该能看到：答案1、答案4、答案5）
SELECT * FROM exam_answer 
WHERE is_deleted = 0
AND (
    role_ids IS NULL 
    OR role_ids = '' 
    OR FIND_IN_SET('1', role_ids) > 0
);

-- 3. 测试角色ID为2的用户能看到的数据

-- 科目查询（角色2应该能看到：数学、语文、物理）
SELECT * FROM exam_subjects 
WHERE is_deleted = 0
AND (
    role_ids IS NULL 
    OR role_ids = '' 
    OR FIND_IN_SET('2', role_ids) > 0
);

-- 题目查询（角色2应该能看到：数学题1、数学题2、语文题3、物理题5）
SELECT eq.*, es.name as subject_name
FROM exam_questions as eq
LEFT JOIN exam_subjects as es on eq.subject_id = es.id
WHERE eq.is_deleted = 0
AND (
    eq.role_ids IS NULL 
    OR eq.role_ids = '' 
    OR FIND_IN_SET('2', eq.role_ids) > 0
);

-- 试卷查询（角色2应该能看到：数学基础测试、语文阅读理解、综合能力测试）
SELECT * FROM exam_papers 
WHERE is_deleted = 0
AND (
    role_ids IS NULL 
    OR role_ids = '' 
    OR FIND_IN_SET('2', role_ids) > 0
);

-- 答案查询（角色2应该能看到：答案1、答案2、答案3、答案5）
SELECT * FROM exam_answer 
WHERE is_deleted = 0
AND (
    role_ids IS NULL 
    OR role_ids = '' 
    OR FIND_IN_SET('2', role_ids) > 0
);

-- 4. 测试多角色用户（角色1,2）能看到的数据
-- 模拟用户同时拥有角色1和角色2的情况

-- 科目查询（角色1,2应该能看到：数学、语文、英语、物理）
SELECT * FROM exam_subjects 
WHERE is_deleted = 0
AND (
    role_ids IS NULL 
    OR role_ids = '' 
    OR FIND_IN_SET('1', role_ids) > 0
    OR FIND_IN_SET('2', role_ids) > 0
);

-- 题目查询（角色1,2应该能看到：所有题目）
SELECT eq.*, es.name as subject_name
FROM exam_questions as eq
LEFT JOIN exam_subjects as es on eq.subject_id = es.id
WHERE eq.is_deleted = 0
AND (
    eq.role_ids IS NULL 
    OR eq.role_ids = '' 
    OR FIND_IN_SET('1', eq.role_ids) > 0
    OR FIND_IN_SET('2', eq.role_ids) > 0
);

-- 5. 清理测试数据（可选）
-- DELETE FROM exam_answer WHERE id IN (1,2,3,4,5);
-- DELETE FROM exam_papers WHERE id IN (1,2,3,4);
-- DELETE FROM exam_questions WHERE id IN (1,2,3,4,5);
-- DELETE FROM exam_subjects WHERE id IN (1,2,3,4);
