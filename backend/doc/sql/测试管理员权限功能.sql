-- 测试管理员权限功能的SQL语句
-- 验证管理员角色不受权限控制限制

-- 1. 创建测试角色数据
INSERT INTO `blade_role` (`id`, `tenant_id`, `parent_id`, `role_name`, `role_alias`, `sort`, `is_deleted`) VALUES
(1001, '000000', 0, '超级管理员', 'administrator', 1, 0),
(1002, '000000', 0, '管理员', 'admin', 2, 0),
(1003, '000000', 0, '普通用户', 'user', 3, 0),
(1004, '000000', 0, '教师', 'teacher', 4, 0);

-- 2. 创建测试用户数据
INSERT INTO `blade_user` (`id`, `tenant_id`, `code`, `account`, `password`, `name`, `real_name`, `role_id`, `dept_id`, `is_deleted`) VALUES
(2001, '000000', 'U001', 'superadmin', '$2a$10$password', '超级管理员', '超级管理员', '1001', '1', 0),
(2002, '000000', 'U002', 'admin', '$2a$10$password', '管理员', '管理员', '1002', '1', 0),
(2003, '000000', 'U003', 'user1', '$2a$10$password', '用户1', '用户1', '1003', '1', 0),
(2004, '000000', 'U004', 'teacher1', '$2a$10$password', '教师1', '教师1', '1004', '1', 0),
(2005, '000000', 'U005', 'multiuser', '$2a$10$password', '多角色用户', '多角色用户', '1002,1003', '1', 0);

-- 3. 插入有权限控制的测试数据
-- 科目数据
INSERT INTO `exam_subjects` (`id`, `name`, `code`, `order_no`, `role_ids`, `tenant_id`, `create_user`, `create_time`, `status`, `is_deleted`) VALUES
(3001, '仅管理员可见科目', 'ADMIN_ONLY', 1, '1002', '000000', 1, NOW(), 1, 0),
(3002, '教师可见科目', 'TEACHER_ONLY', 2, '1004', '000000', 1, NOW(), 1, 0),
(3003, '多角色可见科目', 'MULTI_ROLE', 3, '1003,1004', '000000', 1, NOW(), 1, 0),
(3004, '无权限限制科目', 'NO_LIMIT', 4, '', '000000', 1, NOW(), 1, 0);

-- 题目数据
INSERT INTO `exam_questions` (`id`, `subject_id`, `type`, `difficulty`, `content`, `options`, `answer`, `explanation`, `tags`, `role_ids`, `status`, `tenant_id`, `create_user`, `create_time`, `is_deleted`) VALUES
(4001, 3001, '单选', '简单', '管理员专属题目', '["A.选项1","B.选项2","C.选项3","D.选项4"]', 'A', '管理员才能看到', '["管理员"]', '1002', 1, '000000', 1, NOW(), 0),
(4002, 3002, '单选', '中等', '教师专属题目', '["A.选项1","B.选项2","C.选项3","D.选项4"]', 'B', '教师才能看到', '["教师"]', '1004', 1, '000000', 1, NOW(), 0),
(4003, 3003, '单选', '困难', '多角色题目', '["A.选项1","B.选项2","C.选项3","D.选项4"]', 'C', '多个角色可以看到', '["多角色"]', '1003,1004', 1, '000000', 1, NOW(), 0),
(4004, 3004, '单选', '简单', '公开题目', '["A.选项1","B.选项2","C.选项3","D.选项4"]', 'D', '所有人都能看到', '["公开"]', '', 1, '000000', 1, NOW(), 0);

-- 试卷数据
INSERT INTO `exam_papers` (`id`, `name`, `description`, `duration_minutes`, `pass_score`, `total_score`, `shuffle`, `allow_pause`, `show_result_detail`, `publish_at`, `role_ids`, `tenant_id`, `status`, `create_user`, `create_time`, `is_deleted`) VALUES
(5001, '管理员专属试卷', '只有管理员能看到的试卷', 60, 60.00, 100.00, 0, 1, 1, NOW(), '1002', '000000', 2, 1, NOW(), 0),
(5002, '教师专属试卷', '只有教师能看到的试卷', 90, 70.00, 100.00, 0, 1, 1, NOW(), '1004', '000000', 2, 1, NOW(), 0),
(5003, '多角色试卷', '多个角色能看到的试卷', 45, 80.00, 100.00, 1, 0, 1, NOW(), '1003,1004', '000000', 2, 1, NOW(), 0),
(5004, '公开试卷', '所有人都能看到的试卷', 120, 75.00, 150.00, 0, 1, 1, NOW(), '', '000000', 2, 1, NOW(), 0);

-- 答案数据
INSERT INTO `exam_answer` (`id`, `session_id`, `user_id`, `paper_id`, `question_id`, `values_json`, `duration_sec`, `submitted_at`, `role_ids`, `tenant_id`, `create_user`, `create_time`, `status`, `is_deleted`) VALUES
(6001, 1, 2002, 5001, 4001, '["A"]', 30, NOW(), '1002', '000000', 2002, NOW(), 1, 0),
(6002, 2, 2004, 5002, 4002, '["B"]', 45, NOW(), '1004', '000000', 2004, NOW(), 1, 0),
(6003, 3, 2003, 5003, 4003, '["C"]', 25, NOW(), '1003,1004', '000000', 2003, NOW(), 1, 0),
(6004, 4, 2003, 5004, 4004, '["D"]', 20, NOW(), '', '000000', 2003, NOW(), 1, 0);

-- 4. 测试管理员权限（模拟超级管理员查询）
-- 超级管理员应该能看到所有数据，不受权限控制限制

-- 科目查询（超级管理员应该看到所有4条记录）
SELECT '超级管理员科目查询结果' as test_name;
SELECT * FROM exam_subjects 
WHERE is_deleted = 0;
-- 预期结果：4条记录

-- 题目查询（超级管理员应该看到所有4条记录）
SELECT '超级管理员题目查询结果' as test_name;
SELECT eq.*, es.name as subject_name
FROM exam_questions as eq
LEFT JOIN exam_subjects as es on eq.subject_id = es.id
WHERE eq.is_deleted = 0;
-- 预期结果：4条记录

-- 试卷查询（超级管理员应该看到所有4条记录）
SELECT '超级管理员试卷查询结果' as test_name;
SELECT * FROM exam_papers 
WHERE is_deleted = 0;
-- 预期结果：4条记录

-- 答案查询（超级管理员应该看到所有4条记录）
SELECT '超级管理员答案查询结果' as test_name;
SELECT * FROM exam_answer 
WHERE is_deleted = 0;
-- 预期结果：4条记录

-- 5. 测试普通管理员权限（模拟管理员查询）
-- 管理员应该能看到所有数据，不受权限控制限制

-- 科目查询（管理员应该看到所有4条记录）
SELECT '管理员科目查询结果' as test_name;
SELECT * FROM exam_subjects 
WHERE is_deleted = 0;
-- 预期结果：4条记录

-- 6. 测试普通用户权限（模拟用户角色ID为1003的查询）
-- 普通用户只能看到有权限的数据

-- 科目查询（用户1003应该只看到：多角色可见科目、无权限限制科目）
SELECT '普通用户科目查询结果' as test_name;
SELECT * FROM exam_subjects 
WHERE is_deleted = 0
AND (
    role_ids IS NULL 
    OR role_ids = '' 
    OR role_ids REGEXP CONCAT('(^|,)(', REPLACE('1003', ',', '|'), ')(,|$)')
);
-- 预期结果：2条记录（3003, 3004）

-- 题目查询（用户1003应该只看到：多角色题目、公开题目）
SELECT '普通用户题目查询结果' as test_name;
SELECT eq.*, es.name as subject_name
FROM exam_questions as eq
LEFT JOIN exam_subjects as es on eq.subject_id = es.id
WHERE eq.is_deleted = 0
AND (
    eq.role_ids IS NULL 
    OR eq.role_ids = '' 
    OR eq.role_ids REGEXP CONCAT('(^|,)(', REPLACE('1003', ',', '|'), ')(,|$)')
);
-- 预期结果：2条记录（4003, 4004）

-- 试卷查询（用户1003应该只看到：多角色试卷、公开试卷）
SELECT '普通用户试卷查询结果' as test_name;
SELECT * FROM exam_papers 
WHERE is_deleted = 0
AND (
    role_ids IS NULL 
    OR role_ids = '' 
    OR role_ids REGEXP CONCAT('(^|,)(', REPLACE('1003', ',', '|'), ')(,|$)')
);
-- 预期结果：2条记录（5003, 5004）

-- 7. 测试教师权限（模拟教师角色ID为1004的查询）
-- 教师用户只能看到有权限的数据

-- 科目查询（教师1004应该只看到：教师可见科目、多角色可见科目、无权限限制科目）
SELECT '教师科目查询结果' as test_name;
SELECT * FROM exam_subjects 
WHERE is_deleted = 0
AND (
    role_ids IS NULL 
    OR role_ids = '' 
    OR role_ids REGEXP CONCAT('(^|,)(', REPLACE('1004', ',', '|'), ')(,|$)')
);
-- 预期结果：3条记录（3002, 3003, 3004）

-- 8. 测试多角色用户权限（模拟用户角色ID为1002,1003的查询）
-- 多角色用户应该能看到任一角色有权限的数据

-- 科目查询（多角色用户应该看到：仅管理员可见科目、多角色可见科目、无权限限制科目）
SELECT '多角色用户科目查询结果' as test_name;
SELECT * FROM exam_subjects 
WHERE is_deleted = 0
AND (
    role_ids IS NULL 
    OR role_ids = '' 
    OR role_ids REGEXP CONCAT('(^|,)(', REPLACE('1002,1003', ',', '|'), ')(,|$)')
);
-- 预期结果：3条记录（3001, 3003, 3004）

-- 9. 清理测试数据（可选）
-- DELETE FROM exam_answer WHERE id IN (6001,6002,6003,6004);
-- DELETE FROM exam_papers WHERE id IN (5001,5002,5003,5004);
-- DELETE FROM exam_questions WHERE id IN (4001,4002,4003,4004);
-- DELETE FROM exam_subjects WHERE id IN (3001,3002,3003,3004);
-- DELETE FROM blade_user WHERE id IN (2001,2002,2003,2004,2005);
-- DELETE FROM blade_role WHERE id IN (1001,1002,1003,1004);
