-- -----------------------------------
-- 新增主管字段
-- -----------------------------------
ALTER TABLE "blade_user"
    ADD COLUMN "leader_id" varchar(1000),
    ADD COLUMN "is_leader" int2 DEFAULT 0;

COMMENT ON COLUMN "blade_user"."leader_id" IS '主管id';

COMMENT ON COLUMN "blade_user"."is_leader" IS '是否主管';

ALTER TABLE "blade_dept"
    ADD COLUMN "leader_id" varchar(1000);

COMMENT ON COLUMN "blade_dept"."leader_id" IS '主管id';

-- -----------------------------------
-- 更新报表的菜单URL
-- -----------------------------------
UPDATE "blade_menu" SET "path" = 'http://localhost/ureport/designer?Blade-Auth=bearer ${token}' WHERE "code" = 'report_setting';

-- -----------------------------------
-- 新增数据审计菜单
-- -----------------------------------
INSERT INTO "blade_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category", "action", "is_open", "component", "remark", "is_deleted") VALUES (1934849446741721089, 0, 'data', '数据审计', 'data', '/data', 'iconfont icon-zhunbeiliangchan', 97, 1, 0, 1, '', '', 0);
INSERT INTO "blade_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category", "action", "is_open", "component", "remark", "is_deleted") VALUES (1934849932886720514, 1934849446741721089, 'record', '审计日志', 'record', '/data/record', 'iconfont icon-chaxun', 1, 1, 0, 1, '', '', 0);
INSERT INTO "blade_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category", "action", "is_open", "component", "remark", "is_deleted") VALUES (1934851779491971073, 1934849932886720514, 'record_view', '查看', 'record_view', '/record/view', 'iconfont iconicon_glass', 1, 2, 0, 1, '', '', 0);

-- -----------------------------------
-- 创建数据审计表
-- -----------------------------------
CREATE TABLE "blade_record_data" (
    "id" int8 NOT NULL,
    "tenant_id" varchar(12) DEFAULT '000000',
    "service_id" varchar(32),
    "server_host" varchar(255),
    "server_ip" varchar(255),
    "env" varchar(255),
    "record_level" varchar(12) DEFAULT '',
    "method" varchar(10),
    "request_uri" varchar(255),
    "user_agent" varchar(1000),
    "remote_ip" varchar(255),
    "operation" varchar(255) DEFAULT '',
    "table_name" varchar(255) DEFAULT '',
    "old_data" text,
    "new_data" text,
    "record_message" text,
    "record_result" text,
    "record_cost" varchar(64),
    "record_time" timestamp,
    "record_user" int8,
    "status" int4 DEFAULT 1,
    "is_deleted" int4 DEFAULT 0,
    PRIMARY KEY ("id")
);

COMMENT ON COLUMN "blade_record_data"."id" IS '主键';

COMMENT ON COLUMN "blade_record_data"."tenant_id" IS '租户ID';

COMMENT ON COLUMN "blade_record_data"."service_id" IS '服务ID';

COMMENT ON COLUMN "blade_record_data"."server_host" IS '服务器名';

COMMENT ON COLUMN "blade_record_data"."server_ip" IS '服务器IP地址';

COMMENT ON COLUMN "blade_record_data"."env" IS '服务器环境';

COMMENT ON COLUMN "blade_record_data"."record_level" IS '审计级别';

COMMENT ON COLUMN "blade_record_data"."method" IS '操作方式';

COMMENT ON COLUMN "blade_record_data"."request_uri" IS '请求URI';

COMMENT ON COLUMN "blade_record_data"."user_agent" IS '用户代理';

COMMENT ON COLUMN "blade_record_data"."remote_ip" IS '操作IP地址';

COMMENT ON COLUMN "blade_record_data"."operation" IS '操作类型';

COMMENT ON COLUMN "blade_record_data"."table_name" IS '数据表名';

COMMENT ON COLUMN "blade_record_data"."old_data" IS '操作前参数';

COMMENT ON COLUMN "blade_record_data"."new_data" IS '操作后参数';

COMMENT ON COLUMN "blade_record_data"."record_message" IS '审计消息';

COMMENT ON COLUMN "blade_record_data"."record_result" IS '审计结果';

COMMENT ON COLUMN "blade_record_data"."record_cost" IS '记录耗时';

COMMENT ON COLUMN "blade_record_data"."record_time" IS '记录时间';

COMMENT ON COLUMN "blade_record_data"."record_user" IS '记录人';

COMMENT ON COLUMN "blade_record_data"."status" IS '状态';

COMMENT ON COLUMN "blade_record_data"."is_deleted" IS '是否已删除';

COMMENT ON TABLE "blade_record_data" IS '数据审计表';
