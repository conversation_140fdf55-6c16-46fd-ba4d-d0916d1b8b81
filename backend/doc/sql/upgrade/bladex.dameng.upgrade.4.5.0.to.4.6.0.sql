-- -----------------------------------
-- 新增主管字段
-- -----------------------------------
ALTER TABLE "BLADE_USER" ADD "LEADER_ID" VARCHAR(1000);
ALTER TABLE "BLADE_USER" ADD "IS_LEADER" INT DEFAULT 0;
COMMENT ON COLUMN "BLADE_USER"."LEADER_ID" IS '主管id';
COMMENT ON COLUMN "BLADE_USER"."IS_LEADER" IS '是否主管';

ALTER TABLE "BLADE_DEPT" ADD "LEADER_ID" VARCHAR(1000);
COMMENT ON COLUMN "BLADE_DEPT"."LEADER_ID" IS '主管id';

-- -----------------------------------
-- 更新报表的菜单URL
-- -----------------------------------
UPDATE "BLADEX"."BLADE_MENU" SET "PATH" = 'http://localhost/ureport/designer?Blade-Auth=bearer ${token}' WHERE "CODE" = 'report_setting';

-- -----------------------------------
-- 新增数据审计菜单
-- -----------------------------------
INSERT INTO "BLADEX"."BLADE_MENU" ("ID", "PARENT_ID", "CODE", "NAME", "ALIAS", "PATH", "SOURCE", "SORT", "CATEGORY", "ACTION", "IS_OPEN", "COMPONENT", "REMARK", "IS_DELETED") VALUES (1934849446741721089, 0, 'data', '数据审计', 'data', '/data', 'iconfont icon-zhunbeiliangchan', 97, 1, 0, 1, '', '', 0);
INSERT INTO "BLADEX"."BLADE_MENU" ("ID", "PARENT_ID", "CODE", "NAME", "ALIAS", "PATH", "SOURCE", "SORT", "CATEGORY", "ACTION", "IS_OPEN", "COMPONENT", "REMARK", "IS_DELETED") VALUES (1934849932886720514, 1934849446741721089, 'record', '审计日志', 'record', '/data/record', 'iconfont icon-chaxun', 1, 1, 0, 1, '', '', 0);
INSERT INTO "BLADEX"."BLADE_MENU" ("ID", "PARENT_ID", "CODE", "NAME", "ALIAS", "PATH", "SOURCE", "SORT", "CATEGORY", "ACTION", "IS_OPEN", "COMPONENT", "REMARK", "IS_DELETED") VALUES (1934851779491971073, 1934849932886720514, 'record_view', '查看', 'record_view', '/record/view', 'iconfont iconicon_glass', 1, 2, 0, 1, '', '', 0);

-- -----------------------------------
-- 创建数据审计表
-- -----------------------------------
CREATE TABLE "BLADEX"."BLADE_RECORD_DATA"
(
    "ID" BIGINT NOT NULL,
    "TENANT_ID" VARCHAR(12) DEFAULT '000000',
    "SERVICE_ID" VARCHAR(32) NULL,
    "SERVER_HOST" VARCHAR(255) NULL,
    "SERVER_IP" VARCHAR(255) NULL,
    "ENV" VARCHAR(255) NULL,
    "RECORD_LEVEL" VARCHAR(12) DEFAULT '',
    "METHOD" VARCHAR(10) NULL,
    "REQUEST_URI" VARCHAR(255) NULL,
    "USER_AGENT" VARCHAR(1000) NULL,
    "REMOTE_IP" VARCHAR(255) NULL,
    "OPERATION" VARCHAR(255) DEFAULT '',
    "TABLE_NAME" VARCHAR(255) DEFAULT '',
    "OLD_DATA" CLOB NULL,
    "NEW_DATA" CLOB NULL,
    "RECORD_MESSAGE" CLOB NULL,
    "RECORD_RESULT" CLOB NULL,
    "RECORD_COST" VARCHAR(64) NULL,
    "RECORD_TIME" DATETIME NULL,
    "RECORD_USER" BIGINT NULL,
    "STATUS" INT DEFAULT 1,
    "IS_DELETED" INT DEFAULT 0,
    PRIMARY KEY ("ID")
);

COMMENT ON TABLE "BLADEX"."BLADE_RECORD_DATA" IS '数据审计表';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."ID" IS '主键';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."TENANT_ID" IS '租户ID';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."SERVICE_ID" IS '服务ID';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."SERVER_HOST" IS '服务器名';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."SERVER_IP" IS '服务器IP地址';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."ENV" IS '服务器环境';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."RECORD_LEVEL" IS '审计级别';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."METHOD" IS '操作方式';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."REQUEST_URI" IS '请求URI';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."USER_AGENT" IS '用户代理';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."REMOTE_IP" IS '操作IP地址';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."OPERATION" IS '操作类型';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."TABLE_NAME" IS '数据表名';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."OLD_DATA" IS '操作前参数';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."NEW_DATA" IS '操作后参数';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."RECORD_MESSAGE" IS '审计消息';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."RECORD_RESULT" IS '审计结果';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."RECORD_COST" IS '记录耗时';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."RECORD_TIME" IS '记录时间';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."RECORD_USER" IS '记录人';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."STATUS" IS '状态';

COMMENT ON COLUMN "BLADEX"."BLADE_RECORD_DATA"."IS_DELETED" IS '是否已删除';
