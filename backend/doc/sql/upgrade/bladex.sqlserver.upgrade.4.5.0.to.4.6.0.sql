-- -----------------------------------
-- 新增主管字段
-- -----------------------------------
ALTER TABLE [blade_user] ADD [leader_id] nvarchar(1000)
    GO

ALTER TABLE [blade_user] ADD [is_leader] int DEFAULT 0
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主管id',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_user',
    'COLUMN', N'leader_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否主管',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_user',
    'COLUMN', N'is_leader';

ALTER TABLE [blade_dept] ADD [leader_id] nvarchar(1000)
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主管id',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_dept',
    'COLUMN', N'leader_id';

-- -----------------------------------
-- 更新报表的菜单URL
-- -----------------------------------
UPDATE [blade_menu] SET [path] = N'http://localhost/ureport/designer?Blade-Auth=bearer ${token}' WHERE [code] = N'report_setting';

-- -----------------------------------
-- 新增数据审计菜单
-- -----------------------------------
INSERT INTO [blade_menu] ([id], [parent_id], [code], [name], [alias], [path], [source], [sort], [category], [action], [is_open], [component], [remark], [is_deleted])
VALUES (1934849446741721089, 0, N'data', N'数据审计', N'data', N'/data', N'iconfont icon-zhunbeiliangchan', 97, 1, 0, 1, '', '', 0);
INSERT INTO [blade_menu] ([id], [parent_id], [code], [name], [alias], [path], [source], [sort], [category], [action], [is_open], [component], [remark], [is_deleted])
VALUES (1934849932886720514, 1934849446741721089, N'record', N'审计日志', N'record', N'/data/record', N'iconfont icon-chaxun', 1, 1, 0, 1, '', '', 0);
INSERT INTO [blade_menu] ([id], [parent_id], [code], [name], [alias], [path], [source], [sort], [category], [action], [is_open], [component], [remark], [is_deleted])
VALUES (1934851779491971073, 1934849932886720514, N'record_view', N'查看', N'record_view', N'/record/view', N'iconfont iconicon_glass', 1, 2, 0, 1, '', '', 0);

-- -----------------------------------
-- 创建数据审计表
-- -----------------------------------
CREATE TABLE [blade_record_data] (
    [id] bigint NOT NULL,
    [tenant_id] nvarchar(12) DEFAULT '000000',
    [service_id] nvarchar(32),
    [server_host] nvarchar(255),
    [server_ip] nvarchar(255),
    [env] nvarchar(255),
    [record_level] nvarchar(12) DEFAULT '',
    [method] nvarchar(10),
    [request_uri] nvarchar(255),
    [user_agent] nvarchar(1000),
    [remote_ip] nvarchar(255),
    [operation] nvarchar(255) DEFAULT '',
    [table_name] nvarchar(255) DEFAULT '',
    [old_data] text,
    [new_data] text,
    [record_message] text,
    [record_result] text,
    [record_cost] nvarchar(64),
    [record_time] datetime,
    [record_user] bigint,
    [status] int DEFAULT 1 NOT NULL,
    [is_deleted] int DEFAULT 0 NOT NULL,
    PRIMARY KEY CLUSTERED ([id] ASC)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    )
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'主键',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'租户ID',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'tenant_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'服务ID',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'service_id'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'服务器名',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'server_host'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'服务器IP地址',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'server_ip'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'服务器环境',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'env'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'审计级别',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'record_level'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'操作方式',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'method'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'请求URI',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'request_uri'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'用户代理',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'user_agent'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'操作IP地址',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'remote_ip'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'操作类型',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'operation'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'数据表名',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'table_name'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'操作前参数',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'old_data'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'操作后参数',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'new_data'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'审计消息',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'record_message'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'审计结果',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'record_result'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'记录耗时',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'record_cost'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'记录时间',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'record_time'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'记录人',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'record_user'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'状态',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'status'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'是否已删除',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data',
    'COLUMN', N'is_deleted'
    GO

    EXEC sp_addextendedproperty
    'MS_Description', N'数据审计表',
    'SCHEMA', N'dbo',
    'TABLE', N'blade_record_data';
