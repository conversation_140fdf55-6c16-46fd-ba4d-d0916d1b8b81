-- -----------------------------------
-- 新增主管字段
-- -----------------------------------
ALTER TABLE `blade_user`
    ADD COLUMN `leader_id` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主管id' AFTER `post_id`,
    ADD COLUMN `is_leader` int NULL DEFAULT 0 COMMENT '是否主管' AFTER `leader_id`;

ALTER TABLE `blade_dept`
    ADD COLUMN `leader_id` varchar(1000) NULL COMMENT '主管id' AFTER `ancestors`;

-- -----------------------------------
-- 更新报表的菜单URL
-- -----------------------------------
UPDATE `blade_menu` SET `path` = 'http://localhost/ureport/designer?Blade-Auth=bearer ${token}' WHERE `code` = 'report_setting';

-- -----------------------------------
-- 新增数据审计菜单
-- -----------------------------------
INSERT INTO `blade_menu` (`id`, `parent_id`, `code`, `name`, `alias`, `path`, `source`, `sort`, `category`, `action`, `is_open`, `component`, `remark`, `is_deleted`) VALUES (1934849446741721089, 0, 'data', '数据审计', 'data', '/data', 'iconfont icon-zhunbeiliangchan', 97, 1, 0, 1, '', '', 0);
INSERT INTO `blade_menu` (`id`, `parent_id`, `code`, `name`, `alias`, `path`, `source`, `sort`, `category`, `action`, `is_open`, `component`, `remark`, `is_deleted`) VALUES (1934849932886720514, 1934849446741721089, 'record', '审计日志', 'record', '/data/record', 'iconfont icon-chaxun', 1, 1, 0, 1, '', '', 0);
INSERT INTO `blade_menu` (`id`, `parent_id`, `code`, `name`, `alias`, `path`, `source`, `sort`, `category`, `action`, `is_open`, `component`, `remark`, `is_deleted`) VALUES (1934851779491971073, 1934849932886720514, 'record_view', '查看', 'record_view', '/record/view', 'iconfont iconicon_glass', 1, 2, 0, 1, '', '', 0);

-- -----------------------------------
-- 创建数据审计表
-- -----------------------------------
CREATE TABLE `blade_record_data`  (
  `id` bigint NOT NULL COMMENT '主键',
  `tenant_id` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
  `service_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务ID',
  `server_host` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务器名',
  `server_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务器IP地址',
  `env` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务器环境',
  `record_level` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '审计级别',
  `method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作方式',
  `request_uri` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求URI',
  `user_agent` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户代理',
  `remote_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作IP地址',
  `operation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作类型',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '数据表名',
  `old_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '操作前参数',
  `new_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '操作后参数',
  `record_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '审计消息',
  `record_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '审计结果',
  `record_cost` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '记录耗时',
  `record_time` datetime NULL DEFAULT NULL COMMENT '记录时间',
  `record_user` bigint NULL DEFAULT NULL COMMENT '记录人',
  `status` int NULL DEFAULT 1 COMMENT '状态',
  `is_deleted` int NULL DEFAULT 0 COMMENT '是否已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据审计表';
