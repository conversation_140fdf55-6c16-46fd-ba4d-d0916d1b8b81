-- -----------------------------------
-- 新增主管字段
-- -----------------------------------
ALTER TABLE "BLADE_USER"
    ADD ("LEADER_ID" NVARCHAR2(1000))
    ADD ("IS_LEADER" NUMBER(11) DEFAULT 0);

COMMENT ON COLUMN "BLADE_USER"."LEADER_ID" IS '主管id';

COMMENT ON COLUMN "BLADE_USER"."IS_LEADER" IS '是否主管';

ALTER TABLE "BLADE_DEPT"
    ADD ("LEADER_ID" NVARCHAR2(1000));

COMMENT ON COLUMN "BLADE_DEPT"."LEADER_ID" IS '主管id';

-- -----------------------------------
-- 更新报表的菜单URL
-- -----------------------------------
UPDATE "BLADE_MENU" SET "PATH" = 'http://localhost/ureport/designer?Blade-Auth=bearer ${token}' WHERE "CODE" = 'report_setting';

-- -----------------------------------
-- 新增数据审计菜单
-- -----------------------------------
INSERT INTO "BLADE_MENU" ("ID", "PARENT_ID", "CODE", "NAME", "ALIAS", "PATH", "SOURCE", "SORT", "CATEGORY", "ACTION", "IS_OPEN", "COMPONENT", "REMARK", "IS_DELETED") VALUES (1934849446741721089, 0, 'data', '数据审计', 'data', '/data', 'iconfont icon-zhunbeiliangchan', 97, 1, 0, 1, '', '', 0);
INSERT INTO "BLADE_MENU" ("ID", "PARENT_ID", "CODE", "NAME", "ALIAS", "PATH", "SOURCE", "SORT", "CATEGORY", "ACTION", "IS_OPEN", "COMPONENT", "REMARK", "IS_DELETED") VALUES (1934849932886720514, 1934849446741721089, 'record', '审计日志', 'record', '/data/record', 'iconfont icon-chaxun', 1, 1, 0, 1, '', '', 0);
INSERT INTO "BLADE_MENU" ("ID", "PARENT_ID", "CODE", "NAME", "ALIAS", "PATH", "SOURCE", "SORT", "CATEGORY", "ACTION", "IS_OPEN", "COMPONENT", "REMARK", "IS_DELETED") VALUES (1934851779491971073, 1934849932886720514, 'record_view', '查看', 'record_view', '/record/view', 'iconfont iconicon_glass', 1, 2, 0, 1, '', '', 0);

-- -----------------------------------
-- 创建数据审计表
-- -----------------------------------
CREATE TABLE "BLADE_RECORD_DATA" (
      "ID" NUMBER(20,0) NOT NULL,
      "TENANT_ID" VARCHAR2(12) DEFAULT '000000',
      "SERVICE_ID" VARCHAR2(32),
      "SERVER_HOST" VARCHAR2(255),
      "SERVER_IP" VARCHAR2(255),
      "ENV" VARCHAR2(255),
      "RECORD_LEVEL" VARCHAR2(12) DEFAULT '',
      "METHOD" VARCHAR2(10),
      "REQUEST_URI" VARCHAR2(255),
      "USER_AGENT" VARCHAR2(1000),
      "REMOTE_IP" VARCHAR2(255),
      "OPERATION" VARCHAR2(255) DEFAULT '',
      "TABLE_NAME" VARCHAR2(255) DEFAULT '',
      "OLD_DATA" CLOB,
      "NEW_DATA" CLOB,
      "RECORD_MESSAGE" CLOB,
      "RECORD_RESULT" CLOB,
      "RECORD_COST" VARCHAR2(64),
      "RECORD_TIME" DATE,
      "RECORD_USER" NUMBER(20,0),
      "STATUS" NUMBER(11,0) DEFAULT 1,
      "IS_DELETED" NUMBER(11,0) DEFAULT 0,
      PRIMARY KEY ("ID")
);

COMMENT ON COLUMN "BLADE_RECORD_DATA"."ID" IS '主键';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."TENANT_ID" IS '租户ID';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."SERVICE_ID" IS '服务ID';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."SERVER_HOST" IS '服务器名';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."SERVER_IP" IS '服务器IP地址';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."ENV" IS '服务器环境';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."RECORD_LEVEL" IS '审计级别';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."METHOD" IS '操作方式';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."REQUEST_URI" IS '请求URI';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."USER_AGENT" IS '用户代理';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."REMOTE_IP" IS '操作IP地址';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."OPERATION" IS '操作类型';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."TABLE_NAME" IS '数据表名';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."OLD_DATA" IS '操作前参数';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."NEW_DATA" IS '操作后参数';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."RECORD_MESSAGE" IS '审计消息';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."RECORD_RESULT" IS '审计结果';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."RECORD_COST" IS '记录耗时';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."RECORD_TIME" IS '记录时间';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."RECORD_USER" IS '记录人';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."STATUS" IS '状态';

COMMENT ON COLUMN "BLADE_RECORD_DATA"."IS_DELETED" IS '是否已删除';

COMMENT ON TABLE "BLADE_RECORD_DATA" IS '数据审计表';
