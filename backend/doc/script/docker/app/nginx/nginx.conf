
user  root;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    #gzip  on;

    #include /etc/nginx/conf.d/*.conf;

    upstream gateway {
                 server blade-api;
             }

    server {
      listen       2888;
      server_name  web;
      root         /usr/share/nginx/html;

      gzip on;
      gzip_min_length  1k;
      gzip_buffers     4 16k;
      gzip_http_version 1.1;
      gzip_comp_level 2;
      gzip_types     text/plain application/javascript application/x-javascript text/javascript text/css application/xml;
      gzip_vary on;
      gzip_proxied   expired no-cache no-store private auth;
      gzip_disable   "MSIE [1-6]\.";

      location /{
           index index.html;
           error_page 404 /index.html;
      }

      location ~ ^/(api/)?actuator {
           return 403;
      }

      location ^~ /oauth/redirect {
           try_files $uri /index.html;
      }

      location ^~ /api/ {
           proxy_redirect   off;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_buffering off;
           rewrite ^/api/(.*)$ /$1 break;
           proxy_pass http://gateway/;
      }
    }


}
