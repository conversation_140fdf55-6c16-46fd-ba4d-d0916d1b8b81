# 权限控制功能说明

## 概述

本系统为 answer、subjects、questions、papers 四个模块实现了基于角色的权限控制功能。每个数据记录都可以设置允许查看的角色ID列表，只有拥有相应角色的用户才能查看对应的数据。

## 功能特性

1. **角色级权限控制**：基于用户角色ID进行数据访问控制
2. **多角色支持**：用户可以拥有多个角色，数据也可以分配给多个角色
3. **灵活的权限设置**：支持为每条数据单独设置权限
4. **自动权限过滤**：在分页查询和数据导出时自动过滤用户无权限查看的数据
5. **管理员特权**：管理员角色（administrator、admin）不受权限控制限制，可以查看所有数据
6. **权限管理接口**：提供接口用于批量修改数据权限

## 数据库表结构

### 权限控制字段

所有需要权限控制的表都添加了 `role_ids` 字段：

```sql
ALTER TABLE `exam_subjects` ADD COLUMN `role_ids` VARCHAR(500) NULL COMMENT '权限角色ID数组(逗号分隔字符串格式)';
ALTER TABLE `exam_questions` ADD COLUMN `role_ids` VARCHAR(500) NULL COMMENT '权限角色ID数组(逗号分隔字符串格式)';
ALTER TABLE `exam_papers` ADD COLUMN `role_ids` VARCHAR(500) NULL COMMENT '权限角色ID数组(逗号分隔字符串格式)';
ALTER TABLE `exam_answer` ADD COLUMN `role_ids` VARCHAR(500) NULL COMMENT '权限角色ID数组(逗号分隔字符串格式)';
```

### 字段说明

- `role_ids`：存储角色ID的字符串，多个角色ID用逗号分隔
- 如果字段为 NULL 或空字符串，表示所有用户都可以查看
- 如果字段包含角色ID，只有拥有对应角色的用户才能查看

## 权限控制逻辑

### 权限判断规则

1. **管理员特权**：管理员角色（administrator、admin）不受权限控制限制，可以查看所有数据
2. **无权限限制**：如果 `role_ids` 为 NULL 或空字符串，所有用户都可以查看
3. **有权限限制**：如果 `role_ids` 包含角色ID，只有用户的角色ID在该列表中才能查看
4. **多角色匹配**：用户拥有的任一角色ID在数据的 `role_ids` 中即可查看

### SQL权限过滤

系统在分页查询时自动添加权限过滤条件：

```sql
WHERE (
    role_ids IS NULL 
    OR role_ids = '' 
    OR role_ids REGEXP CONCAT('(^|,)(', REPLACE(用户角色IDs, ',', '|'), ')(,|$)')
)
```

## 接口说明

### 权限管理接口

每个模块都提供了权限管理接口：

#### 更新数据权限

**接口地址**：
- 科目：`POST /exam-subjects/subjects/update-permissions`
- 题目：`POST /exam-questions/questions/update-permissions`
- 试卷：`POST /exam-papers/papers/update-permissions`
- 答案：`POST /blade-answer/answer/update-permissions`

**请求参数**：
```json
{
    "ids": [1, 2, 3],           // 数据ID列表（支持单个或多个ID）
    "roleIds": [10, 20, 30]     // 角色ID列表
}
```

**响应示例**：
```json
{
    "code": 200,
    "success": true,
    "data": true,
    "msg": "操作成功"
}
```

### 分页查询接口

分页查询接口会自动应用权限过滤，用户只能看到有权限查看的数据：

- 科目分页：`GET /exam-subjects/subjects/page`
- 题目分页：`GET /exam-questions/questions/page`
- 试卷分页：`GET /exam-papers/papers/page`
- 答案分页：`GET /blade-answer/answer/page`

### 数据导出接口

数据导出接口同样会自动应用权限过滤，用户只能导出有权限查看的数据：

- 科目导出：`GET /exam-subjects/subjects/export-subjects`
- 题目导出：`GET /exam-questions/questions/export-questions`
- 试卷导出：`GET /exam-papers/papers/export-papers`
- 答案导出：`GET /blade-answer/answer/export-answer`

**注意**：管理员角色（administrator、admin）不受权限控制限制，可以导出所有数据。

## 使用示例

### 1. 设置数据权限

为科目ID为1的记录设置权限，只允许角色ID为10和20的用户查看：

```bash
curl -X POST "http://localhost/exam-subjects/subjects/update-permissions" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [1],
    "roleIds": [10, 20]
  }'
```

### 2. 批量设置权限

为多个试卷设置权限：

```bash
curl -X POST "http://localhost/exam-papers/papers/update-permissions" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [1, 2, 3],
    "roleIds": [10, 20, 30]
  }'
```

### 3. 清除权限限制

设置空的角色ID列表，使数据对所有用户可见：

```bash
curl -X POST "http://localhost/exam-questions/questions/update-permissions" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [1],
    "roleIds": []
  }'
```

## 权限控制工具类

系统提供了 `PermissionUtil` 工具类用于权限判断：

```java
// 检查当前用户是否有权限查看指定数据（管理员角色自动返回true）
boolean hasPermission = PermissionUtil.hasPermission("1,2,3");

// 检查指定用户角色是否有权限（管理员角色自动返回true）
boolean hasPermission = PermissionUtil.hasPermission("1,2,3", "2,4");

// 检查当前用户是否为管理员角色
boolean isAdmin = PermissionUtil.isAdministrator();

// 获取当前用户角色ID
String userRoleIds = PermissionUtil.getCurrentUserRoleIds();
```

## 测试数据

系统提供了测试SQL脚本 `backend/doc/sql/测试权限控制功能.sql`，包含：

1. 测试数据插入
2. 权限控制查询验证
3. 多角色场景测试
4. 数据清理脚本

## 注意事项

1. **管理员特权**：管理员角色（administrator、admin）不受权限控制限制，可以查看和导出所有数据
2. **角色ID格式**：角色ID必须是数字，多个角色ID用逗号分隔，不能有空格
3. **权限继承**：系统不支持角色权限继承，需要明确指定每个角色
4. **性能考虑**：大量数据时建议为 `role_ids` 字段添加索引
5. **兼容性**：权限控制使用MySQL的REGEXP函数，确保数据库版本支持
6. **默认权限**：新创建的数据默认 `role_ids` 为空，所有用户都可以查看

## 扩展说明

如需为其他模块添加权限控制，请按以下步骤操作：

1. 在数据库表中添加 `role_ids` 字段
2. 在实体类中添加 `roleIds` 属性
3. 修改Service实现类的分页方法，传入用户角色ID
4. 修改Mapper接口，添加权限参数
5. 修改Mapper XML，添加权限过滤条件
6. 添加权限管理接口
