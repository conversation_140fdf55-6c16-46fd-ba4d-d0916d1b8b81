## 目的与范围

- 目的：解析“PC端后台管理页面原型”（static-pc/index.html），生成可直接指导基于 Vue3 + Element Plus + Blade 架构实施的模块说明。
- 范围：导航与页面、字段与状态、交互与流程、权限与审计、API 约定、数据模型草案、导入导出、非功能性要求等。
- 角色：super_admin（超管）、admin（管理员）、teacher（教师）、student（学生，通常无后台权限）。

## 全局信息架构

- 导航：仪表盘｜题库管理｜试卷管理｜用户管理｜数据报表｜意见反馈｜系统设置
- 布局：左侧侧边栏（可折叠）+ 顶部用户区（个人设置/退出）+ 主内容区（路由切换）。
- 通用元素：表格+分页、筛选+搜索、状态标签、操作列、弹窗、通知。

## API 约定（建议）

- BasePath：/api/admin，REST 风格，Bearer Token 认证（Sanctum/Passport）。
- 分页：page, per_page；响应含 data, meta.pagination。
- 响应：
  - 成功：{ data, meta?, message? }
  - 失败：{ error:{ code, message, details? } }，HTTP 与错误码一致。
- 查询：统一使用 query 参数（筛选/排序）。
- 删除：软删除，重要操作需二次确认与审计。

## 数据字典

- 题型 question_type：single｜multiple｜judge
- 难度 difficulty：easy｜medium｜hard
- 试卷状态 paper_status：draft｜published｜archived
- 用户角色 role：admin｜teacher｜student
- 用户状态 user_status：active｜inactive｜banned
- 反馈类型 feedback_type：bug｜feature｜complaint｜other
- 反馈状态 feedback_status：pending｜processing｜resolved｜closed

## 模块详规

### 仪表盘 Dashboard

- 内容：指标卡（总用户、题目总数、今日练习、今日考试）、图表（练习趋势：7/30/90天；科目分布）、最近活动列表。
- 接口：
  - GET /dashboard/metrics
  - GET /dashboard/trends?range=7days|30days|3months
  - GET /dashboard/subject-distribution
  - GET /activities/recent?limit=20
- 交互：时间范围切换刷新；“查看全部”活动跳转；指标卡可跳转到对应模块。
- 权限：admin/teacher 可见（teacher 可按组织范围限制）。

### 题库管理 Questions

- 工具栏：科目筛选、题型筛选、关键词搜索、导入、导出、添加题目。
- 列表字段：题目ID｜题目内容｜科目（tag）｜类型｜难度｜创建时间｜操作（编辑/删除）。
- 分页：上一页/下一页 + 页信息。
- 数据模型（建议）：
  - Question：id, subject_id, type, difficulty, content（富文本/图片/公式）, options[], answer（single/multiple/judge）, explanation, tags[], status, created_at, updated_at, deleted_at。
- 接口：
  - GET /subjects
  - GET /questions（subject_id, type, difficulty, keyword, page, per_page, order_by）
  - GET /questions/{id}
  - POST /questions
  - PUT /questions/{id}
  - DELETE /questions/{id}
  - POST /questions/import（Excel/CSV）
  - GET /questions/export（按筛选导出）
  - GET /questions/templates/import（下载模板）
- 导入模板字段（建议）：subject_id/name, type, difficulty, content, option_A…D, answer, explanation, tags。
- 校验：题型与答案一致性；多选答案去重；选项数量范围；科目存在性；必填长度。
- 权限：admin 全量；teacher 限定（仅自建或所属组织）。

### 试卷管理 Papers

- 工具栏：状态筛选（草稿/已发布/已归档）、搜索名称、导入、批量操作、创建试卷。
- 列表字段：选择框｜试卷名称｜题目数｜考试时长｜状态｜创建时间｜操作（编辑/预览/删除）。
- 数据模型（建议）：
  - Paper：id, name, description, status, duration_minutes, pass_score, total_score, shuffle, allow_pause, show_result_detail, publish_at, created_by, created_at, updated_at, deleted_at。
  - PaperItem：paper_id, question_id, score, order_no。
- 接口：
  - GET /papers（status, keyword, page, per_page）
  - GET /papers/{id}
  - POST /papers
  - PUT /papers/{id}
  - DELETE /papers/{id}
  - POST /papers/{id}/items（批量设置题目）
  - GET /papers/{id}/items
  - POST /papers/{id}/publish
  - POST /papers/{id}/archive
  - GET /papers/preview/{id}
  - POST /papers/import
  - GET /papers/export
- 流程（建议）：向导式创建→配置题目→设置分值→保存草稿/发布。

### 用户管理 Users

- 工具栏：角色筛选（管理员/教师/学生）、状态筛选（活跃/非活跃/禁用）、关键词（用户名/邮箱）、导出、批量操作、添加用户。
- 列表字段：选择框｜用户名（头像+ID）｜邮箱｜角色（tag）｜状态（状态点）｜注册时间｜最后登录｜操作（编辑/重置密码/禁用）。
- 数据模型（建议）：
  - User：id, name, email, avatar, role, status, last_login_at, registered_at, org_id, security_flags, created_at, updated_at, deleted_at。
- 接口：
  - GET /users（role, status, keyword, page, per_page）
  - GET /users/{id}
  - POST /users
  - PUT /users/{id}
  - POST /users/{id}/reset-password
  - POST /users/{id}/ban
  - POST /users/{id}/unban
  - GET /users/export
- 校验：邮箱唯一；角色/状态合法；重置密码需审计。
- 权限：超管可管管理员账号；teacher 限读或无权。

### 数据报表 Reports

- 工具栏：时间（周/月/季/年）、数据范围（全部/用户/考试/题目）、导出 Excel/PDF、生成报表（异步）。
- 指标卡：总用户数、总题目数、今日练习、今日考试。
- 图表：用户活跃度趋势、考试通过率。
- 详细表：日期、新增用户、活跃用户、练习次数、考试次数、平均分数。
- 接口：
  - GET /reports/overview?range=week|month|quarter|year&scope=all|user|exam|question
  - GET /reports/trends/active-users?range=7d|30d
  - GET /reports/rates/passed?range=7d|30d
  - GET /reports/detail?date_from&date_to&metrics
  - POST /reports/generate（返回 task_id）
  - GET /reports/exports/{task_id}/status
  - GET /reports/exports/{task_id}/download?format=xlsx|pdf
- 口径：活跃用户=时间窗口内有登录且有练习/考试行为；通过率=成绩≥及格分/总考试次数；平均分=窗口内考试记录均值。
- 性能：指标与趋势短时缓存；导出走异步队列。

### 意见反馈 Feedback

- 工具栏：状态筛选（待处理/处理中/已解决/已关闭）、类型筛选（Bug/功能/投诉/其他）、关键词、导出、批量回复。
- 列表字段：选择框｜用户（头像/姓名/邮箱）｜反馈类型（tag）｜反馈摘要｜状态（tag）｜提交时间｜操作（详情/回复/标记已解决/重新打开）。
- 数据模型（建议）：
  - Feedback：id, user_id, type, content, status, attachments?, created_at, updated_at。
  - FeedbackReply：id, feedback_id, replier_id, content, created_at。
- 状态机：pending → processing → resolved → closed；支持 resolved→reopened。
- 接口：
  - GET /feedback（status, type, keyword, page, per_page）
  - GET /feedback/{id}
  - POST /feedback/{id}/reply
  - POST /feedback/{id}/resolve
  - POST /feedback/{id}/close
  - POST /feedback/{id}/reopen
  - GET /feedback/export
- 通知：回复/状态变更给用户（邮件/站内信）。

### 系统设置 Settings

- 分区：
  - 基本：系统名称、描述、Logo 上传
  - 考试：默认时长、及格分、允许暂停、显示结果详情
  - 用户：允许注册、邮箱验证、默认角色
  - 安全：密码最小长度、登录失败锁定次数、启用验证码、启用双因素
  - 邮件：SMTP 服务器/端口/发件人/密码，测试发送
- 数据模型（建议）：
  - Setting：key, value, group（general/exam/user/security/mail）, type（string/number/boolean/json）, is_sensitive。
- 接口：
  - GET /settings?group=...
  - PUT /settings（批量保存）
  - POST /settings/mail/test（可用当前未保存的临时值）
- 安全：敏感值加密与脱敏回显；上传限制类型与大小；数值范围校验。
- 权限：super_admin 主导；admin 可读或有限更改。

## 通用交互与组件（Element Plus）

- 表格：ElTable + 操作列；空态/加载态统一；多选行支持。
- 分页：ElPagination，支持 page-size 10/20/50/100。
- 筛选搜索：ElSelect/ElInput，防抖 300ms。
- 弹窗：ElDialog（新增/编辑/确认）。
- 通知：ElMessage/ElNotification；ElMessageBox 确认。
- 上传：ElUpload（限制大小与类型）。
- 富文本：tiptap/quill；与表单联动校验。
- 状态展示：ElTag + 自定义状态点。
- 权限指令：v-permission 控制按钮显隐。
- 侧边栏折叠：本地存储持久化。

## 权限与审计

- 权限点（示例）：
  - questions: create/read/update/delete/import/export
  - papers: create/read/update/delete/publish/archive/import/export
  - users: create/read/update/reset_password/ban/unban/export
  - feedback: read/reply/resolve/close/reopen/export
  - reports: read/export/generate
  - settings: read/update/mail_test
- 审计日志：记录模块、动作、操作者、对象、差异（敏感脱敏）、IP、时间；接口 GET /audit-logs 查询。

## 校验与错误处理

- 前端：必填、类型范围、上传类型/大小、富文本长度等。
- 后端：二次校验，明确错误码与字段信息。
- 错误提示：生产环境提示摘要，开发环境可含详情；重要操作需二次确认。
- 幂等：导入/发布等接口建议携带幂等键。

## 安全与性能

- 索引与分页；keyword 建议全文或前缀索引。
- 图表数据短缓存；导出异步化（队列重试）。
- 防注入/防XSS（题干/解析富文本清洗）。
- 上传安全检查（MIME/扩展名/大小，必要时内容检测）。
- 速率限制：登录、导出、测试邮件等。
- CSRF（Blade 表单）、CORS（API）。

## 前端结构与路由（建议）

- Blade 提供基础布局与 #app 挂载；Vue3 作为 SPA。
- 路由：/admin/dashboard｜/admin/questions｜/admin/papers｜/admin/users｜/admin/reports｜/admin/feedback｜/admin/settings。
- 状态管理：Pinia（用户/权限/字典）。
- API 封装：axios 拦截器（Auth、错误统一处理、Loading）。

## 数据模型草案（概要）

- subjects：id, name, code, order_no, created_at, updated_at
- questions：见上，含 subject_id 外键
- papers：见上
- paper_items：paper_id, question_id, score, order_no
- users：见上
- feedback：见上
- feedback_replies：见上
- settings：见上
- audit_logs：module, action, actor_id, target_id, diff, ip, created_at

## 示例请求与响应（片段）

- GET /api/admin/questions?subject_id=1&type=single&keyword=成立&page=1&per_page=20
  - 返回：data[]（id, content, subject, type, difficulty, created_at），meta.pagination。
- POST /api/admin/papers/{id}/publish
  - 成功：{ message:"已发布", data:{ id, status:"published", publish_at } }
- POST /api/admin/settings/mail/test
  - 成功：{ message:"测试邮件已发送" }；失败：{ error:{ code:"MAIL_SEND_FAILED", message:"SMTP 鉴权失败" } }

## 非功能性与运维

- 日志：系统日志与审计日志分离；导出等动作写日志。
- 监控：报表队列失败告警；接口时延监控。
- 备份：题库/试卷/导出文件定期备份。
- 多租户（可选）：按 org_id 隔离数据与权限。

## 实施建议与里程碑

1) 阶段一：题库/试卷/用户 CRUD、分页筛选、RBAC 初版、导入导出 MVP。
2) 阶段二：报表生成与导出异步化、反馈闭环、审计日志完善。
3) 阶段三：系统设置与安全策略、缓存与性能优化、富文本/LaTeX 支持。
4) 阶段四：多组织/权限细化、反作弊与合规增强。


