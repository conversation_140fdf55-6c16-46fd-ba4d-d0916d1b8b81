/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.answer.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.IsAdmin;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.answer.excel.AnswerExcel;
import org.springblade.modules.answer.pojo.entity.AnswerEntity;
import org.springblade.modules.answer.pojo.vo.AnswerVO;
import org.springblade.modules.answer.service.IAnswerService;
import org.springblade.modules.answer.wrapper.AnswerWrapper;
import org.springblade.modules.common.dto.PermissionUpdateDTO;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 考试答案记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-answer/answer")
@Tag(name = "考试答案记录表", description = "考试答案记录表接口")
public class AnswerController extends BladeController {

	private final IAnswerService answerService;

	/**
	 * 考试答案记录表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入answer")
	public R<AnswerVO> detail(AnswerEntity answer) {
		AnswerEntity detail = answerService.getOne(Condition.getQueryWrapper(answer));
		return R.data(AnswerWrapper.build().entityVO(detail));
	}
	/**
	 * 考试答案记录表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入answer")
	public R<IPage<AnswerVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> answer, Query query) {
		IPage<AnswerEntity> pages = answerService.page(Condition.getPage(query), Condition.getQueryWrapper(answer, AnswerEntity.class));
		return R.data(AnswerWrapper.build().pageVO(pages));
	}

	/**
	 * 考试答案记录表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入answer")
	public R<IPage<AnswerVO>> page(AnswerVO answer, Query query) {
		IPage<AnswerVO> pages = answerService.selectAnswerPage(Condition.getPage(query), answer);
		return R.data(pages);
	}

	/**
	 * 考试答案记录表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入answer")
	public R save(@Valid @RequestBody AnswerEntity answer) {
		return R.status(answerService.save(answer));
	}

	/**
	 * 考试答案记录表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入answer")
	public R update(@Valid @RequestBody AnswerEntity answer) {
		return R.status(answerService.updateById(answer));
	}

	/**
	 * 考试答案记录表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入answer")
	public R submit(@Valid @RequestBody AnswerEntity answer) {
		return R.status(answerService.saveOrUpdate(answer));
	}

	/**
	 * 考试答案记录表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(answerService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@IsAdmin
	@GetMapping("/export-answer")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入answer")
	public void exportAnswer(@Parameter(hidden = true) @RequestParam Map<String, Object> answer, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<AnswerEntity> queryWrapper = Condition.getQueryWrapper(answer, AnswerEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Answer::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(AnswerEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<AnswerExcel> list = answerService.exportAnswer(queryWrapper);
		ExcelUtil.export(response, "考试答案记录表数据" + DateUtil.time(), "考试答案记录表数据表", list, AnswerExcel.class);
	}

	/**
	 * 更新数据权限
	 */
	@PostMapping("/update-permissions")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "更新数据权限", description = "传入权限更新参数")
	public R updatePermissions(@Valid @RequestBody PermissionUpdateDTO permissionUpdateDTO) {
		boolean result = answerService.updatePermissions(permissionUpdateDTO);
		return R.status(result);
	}

}
