/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.answer.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.answer.excel.AnswerExcel;
import org.springblade.modules.answer.pojo.entity.AnswerEntity;
import org.springblade.modules.answer.pojo.vo.AnswerVO;
import org.springblade.modules.common.dto.PermissionUpdateDTO;

import java.util.List;

/**
 * 考试答案记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
public interface IAnswerService extends BaseService<AnswerEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param answer 查询参数
	 * @return IPage<AnswerVO>
	 */
	IPage<AnswerVO> selectAnswerPage(IPage<AnswerVO> page, AnswerVO answer);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<AnswerExcel>
	 */
	List<AnswerExcel> exportAnswer(Wrapper<AnswerEntity> queryWrapper);

	/**
	 * 更新数据权限
	 *
	 * @param permissionUpdateDTO 权限更新参数
	 * @return boolean
	 */
	boolean updatePermissions(PermissionUpdateDTO permissionUpdateDTO);

}
