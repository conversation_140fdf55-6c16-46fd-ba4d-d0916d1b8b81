package org.springblade.modules.answer.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 考试答案表 实体
 */
@Data
@TableName("exam_answer")
@Schema(description = "考试答案记录表")
@EqualsAndHashCode(callSuper = true)
public class AnswerEntity extends TenantEntity {

    @Schema(description = "考试会话ID")
    private Long sessionId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "试卷ID")
    private Long paperId;

    @Schema(description = "题目ID")
    private Long questionId;

    @Schema(description = "用户作答选项（JSON 数组）")
    private String valuesJson;

    @Schema(description = "该题耗时（秒）")
    private Integer durationSec;

    @Schema(description = "提交时间")
    private java.util.Date submittedAt;

    @Schema(description = "权限角色ID数组(JSON格式)")
    private String roleIds;

}
