<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.answer.mapper.AnswerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="answerResultMap" type="org.springblade.modules.answer.pojo.entity.AnswerEntity">
        <result column="id" property="id"/>
        <result column="session_id" property="sessionId"/>
        <result column="user_id" property="userId"/>
        <result column="paper_id" property="paperId"/>
        <result column="question_id" property="questionId"/>
        <result column="values_json" property="valuesJson"/>
        <result column="duration_sec" property="durationSec"/>
        <result column="submitted_at" property="submittedAt"/>
        <result column="role_ids" property="roleIds"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectAnswerPage" resultMap="answerResultMap">
        select * from exam_answer
        where is_deleted = 0
        <!-- 权限控制：只显示用户有权限查看的数据，管理员角色不受限制 -->
        <if test="userRoleIds != null and userRoleIds != ''">
            AND (
                role_ids IS NULL
                OR role_ids = ''
                OR role_ids REGEXP CONCAT('(^|,)(', REPLACE(#{userRoleIds}, ',', '|'), ')(,|$)')
            )
        </if>
    </select>


    <select id="exportAnswer" resultType="org.springblade.modules.answer.excel.AnswerExcel">
        SELECT * FROM exam_answer
        WHERE 1=1
        <!-- 权限控制：只导出用户有权限查看的数据 -->
        <if test="userRoleIds != null and userRoleIds != ''">
            AND (
                role_ids IS NULL
                OR role_ids = ''
                OR role_ids REGEXP CONCAT('(^|,)(', REPLACE(#{userRoleIds}, ',', '|'), ')(,|$)')
            )
        </if>
        ${ew.customSqlSegment}
    </select>

</mapper>
