/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.answer.excel;


import lombok.Data;

import java.util.Date;
import java.lang.Boolean;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 考试答案记录表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class AnswerExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键ID")
	private Long id;
	/**
	 * 考试会话ID（关联 exam_session.id）
	 */
	@ColumnWidth(20)
	@ExcelProperty("考试会话ID（关联 exam_session.id）")
	private Long sessionId;
	/**
	 * 用户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("用户ID")
	private Long userId;
	/**
	 * 试卷ID（关联 papers.id）
	 */
	@ColumnWidth(20)
	@ExcelProperty("试卷ID（关联 papers.id）")
	private Long paperId;
	/**
	 * 题目ID（关联 questions.id）
	 */
	@ColumnWidth(20)
	@ExcelProperty("题目ID（关联 questions.id）")
	private Long questionId;
	/**
	 * 用户作答选项（JSON 数组）
	 */
	@ColumnWidth(20)
	@ExcelProperty("用户作答选项（JSON 数组）")
	private String valuesJson;
	/**
	 * 该题耗时（秒）
	 */
	@ColumnWidth(20)
	@ExcelProperty("该题耗时（秒）")
	private Integer durationSec;
	/**
	 * 提交时间（保存草稿亦记录）
	 */
	@ColumnWidth(20)
	@ExcelProperty("提交时间（保存草稿亦记录）")
	private Date submittedAt;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除：0未删除，1已删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除：0未删除，1已删除")
	private Boolean isDeleted;

}
