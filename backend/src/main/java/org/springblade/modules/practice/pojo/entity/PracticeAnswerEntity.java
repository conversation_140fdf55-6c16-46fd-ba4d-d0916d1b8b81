package org.springblade.modules.practice.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.util.Date;

/**
 * 练习答案表 实体
 */
@Data
@TableName("exam_practice_answer")
@Schema(description = "练习答案记录表")
@EqualsAndHashCode(callSuper = true)
public class PracticeAnswerEntity extends TenantEntity {

    @Schema(description = "练习会话ID")
    private Long sessionId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "科目ID")
    private Long subjectId;

    @Schema(description = "题目ID")
    private Long questionId;

    @Schema(description = "用户作答选项（JSON 数组）")
    private String valuesJson;

    @Schema(description = "是否作答正确：1正确，0错误，NULL未判分")
    private Integer isCorrect;

    @Schema(description = "该题耗时（秒）")
    private Integer durationSec;

    @Schema(description = "提交时间")
    private Date submittedAt;

}

