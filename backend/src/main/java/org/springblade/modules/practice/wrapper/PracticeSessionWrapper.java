/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.practice.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.modules.dingding.pojo.vo.PracticeSessionVO;
import org.springblade.modules.practice.pojo.entity.PracticeSessionEntity;

import java.util.Objects;

/**
 * 练习会话表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
public class PracticeSessionWrapper extends BaseEntityWrapper<PracticeSessionEntity, PracticeSessionVO>  {

	public static PracticeSessionWrapper build() {
		return new PracticeSessionWrapper();
 	}

	@Override
	public PracticeSessionVO entityVO(PracticeSessionEntity practiceSession) {
		PracticeSessionVO practiceSessionVO = Objects.requireNonNull(BeanUtil.copyProperties(practiceSession, PracticeSessionVO.class));

		//User createUser = UserCache.getUser(practiceSession.getCreateUser());
		//User updateUser = UserCache.getUser(practiceSession.getUpdateUser());
		//practiceSessionVO.setCreateUserName(createUser.getName());
		//practiceSessionVO.setUpdateUserName(updateUser.getName());

		return practiceSessionVO;
	}


}
