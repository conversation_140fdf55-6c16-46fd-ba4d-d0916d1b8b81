<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.practice.mapper.SessionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sessionResultMap" type="org.springblade.modules.practice.pojo.entity.SessionEntity">
    </resultMap>


    <select id="selectSessionPage" resultMap="sessionResultMap">
        select * from exam_session where is_deleted = 0
    </select>


    <select id="exportSession" resultType="org.springblade.modules.practice.excel.SessionExcel">
        SELECT * FROM exam_session ${ew.customSqlSegment}
    </select>

</mapper>
