<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.practice.mapper.PracticeAnswerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="practiceAnswerResultMap" type="org.springblade.modules.practice.pojo.entity.PracticeAnswerEntity">
    </resultMap>


    <select id="selectPracticeAnswerPage" resultMap="practiceAnswerResultMap">
        select * from exam_practice_answer where is_deleted = 0
    </select>


    <select id="exportPracticeAnswer" resultType="org.springblade.modules.practice.excel.PracticeAnswerExcel">
        SELECT * FROM exam_practice_answer ${ew.customSqlSegment}
    </select>

    <select id="selectWrongQuestionIdsBySubject" resultMap="practiceAnswerResultMap">
        SELECT
        session_id,
        question_id
        FROM exam_practice_answer
        WHERE
        is_deleted = 0
        AND is_correct = 0
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="subjectId != null">
            AND subject_id = #{subjectId}
        </if>
    </select>
</mapper>
