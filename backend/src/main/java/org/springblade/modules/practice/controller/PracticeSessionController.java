/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.practice.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.IsAdmin;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.dingding.pojo.vo.PracticeSessionVO;
import org.springblade.modules.practice.excel.PracticeSessionExcel;
import org.springblade.modules.practice.pojo.entity.PracticeSessionEntity;
import org.springblade.modules.practice.service.IPracticeSessionService;
import org.springblade.modules.practice.wrapper.PracticeSessionWrapper;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 练习会话表 控制器
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-practicesession/practiceSession")
@Tag(name = "练习会话表", description = "练习会话表接口")
public class PracticeSessionController extends BladeController {

	private final IPracticeSessionService practiceSessionService;

	/**
	 * 练习会话表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入practiceSession")
	public R<PracticeSessionVO> detail(PracticeSessionEntity practiceSession) {
		PracticeSessionEntity detail = practiceSessionService.getOne(Condition.getQueryWrapper(practiceSession));
		return R.data(PracticeSessionWrapper.build().entityVO(detail));
	}
	/**
	 * 练习会话表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入practiceSession")
	public R<IPage<PracticeSessionVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> practiceSession, Query query) {
		IPage<PracticeSessionEntity> pages = practiceSessionService.page(Condition.getPage(query), Condition.getQueryWrapper(practiceSession, PracticeSessionEntity.class));
		return R.data(PracticeSessionWrapper.build().pageVO(pages));
	}

	/**
	 * 练习会话表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入practiceSession")
	public R<IPage<PracticeSessionVO>> page(PracticeSessionVO practiceSession, Query query) {
		IPage<PracticeSessionVO> pages = practiceSessionService.selectPracticeSessionPage(Condition.getPage(query), practiceSession);
		return R.data(pages);
	}

	/**
	 * 练习会话表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入practiceSession")
	public R save(@Valid @RequestBody PracticeSessionEntity practiceSession) {
		return R.status(practiceSessionService.save(practiceSession));
	}

	/**
	 * 练习会话表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入practiceSession")
	public R update(@Valid @RequestBody PracticeSessionEntity practiceSession) {
		return R.status(practiceSessionService.updateById(practiceSession));
	}

	/**
	 * 练习会话表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入practiceSession")
	public R submit(@Valid @RequestBody PracticeSessionEntity practiceSession) {
		return R.status(practiceSessionService.saveOrUpdate(practiceSession));
	}

	/**
	 * 练习会话表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(practiceSessionService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@IsAdmin
	@GetMapping("/export-practiceSession")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入practiceSession")
	public void exportPracticeSession(@Parameter(hidden = true) @RequestParam Map<String, Object> practiceSession, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<PracticeSessionEntity> queryWrapper = Condition.getQueryWrapper(practiceSession, PracticeSessionEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(PracticeSession::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(PracticeSessionEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<PracticeSessionExcel> list = practiceSessionService.exportPracticeSession(queryWrapper);
		ExcelUtil.export(response, "练习会话表数据" + DateUtil.time(), "练习会话表数据表", list, PracticeSessionExcel.class);
	}

}
