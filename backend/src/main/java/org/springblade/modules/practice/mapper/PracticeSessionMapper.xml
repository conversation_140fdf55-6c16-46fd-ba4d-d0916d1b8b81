<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.practice.mapper.PracticeSessionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="practiceSessionResultMap" type="org.springblade.modules.practice.pojo.entity.PracticeSessionEntity">
    </resultMap>


    <select id="selectPracticeSessionPage" resultMap="practiceSessionResultMap">
        select * from exam_practice_session where is_deleted = 0
    </select>


    <select id="exportPracticeSession" resultType="org.springblade.modules.practice.excel.PracticeSessionExcel">
        SELECT * FROM exam_practice_session ${ew.customSqlSegment}
    </select>

</mapper>
