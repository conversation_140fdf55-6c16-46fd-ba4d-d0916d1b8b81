package org.springblade.modules.practice.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.util.Date;

/**
 * 练习会话表 实体
 */
@Data
@TableName("exam_practice_session")
@Schema(description = "练习会话表")
@EqualsAndHashCode(callSuper = true)
public class PracticeSessionEntity extends TenantEntity {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "科目ID")
    private Long subjectId;

    @Schema(description = "试卷ID（用于按试卷练习）")
    private Long paperId;

    @Schema(description = "练习模式：sequence|random|wrong")
    private String mode;

    @Schema(description = "本次会话题目总量")
    private Integer questionTotal;

    @Schema(description = "当前进度指针/已答数量（0 基）")
    private Integer currentIndex;

    @Schema(description = "题目ID有序列表快照（JSON 数组，固定题序）")
    private String questionIds;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

}

