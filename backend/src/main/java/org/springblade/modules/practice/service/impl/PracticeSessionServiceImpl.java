/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.practice.service.impl;

import org.springblade.modules.dingding.pojo.vo.PracticeSessionVO;
import org.springblade.modules.practice.excel.PracticeSessionExcel;
import org.springblade.modules.practice.mapper.PracticeSessionMapper;
import org.springblade.modules.practice.pojo.entity.PracticeSessionEntity;
import org.springblade.modules.practice.service.IPracticeSessionService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 练习会话表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Service
public class PracticeSessionServiceImpl extends BaseServiceImpl<PracticeSessionMapper, PracticeSessionEntity> implements IPracticeSessionService {

	@Override
	public IPage<PracticeSessionVO> selectPracticeSessionPage(IPage<PracticeSessionVO> page, PracticeSessionVO practiceSession) {
		return page.setRecords(baseMapper.selectPracticeSessionPage(page, practiceSession));
	}


	@Override
	public List<PracticeSessionExcel> exportPracticeSession(Wrapper<PracticeSessionEntity> queryWrapper) {
		List<PracticeSessionExcel> practiceSessionList = baseMapper.exportPracticeSession(queryWrapper);
		//practiceSessionList.forEach(practiceSession -> {
		//	practiceSession.setTypeName(DictCache.getValue(DictEnum.YES_NO, PracticeSession.getType()));
		//});
		return practiceSessionList;
	}

}
