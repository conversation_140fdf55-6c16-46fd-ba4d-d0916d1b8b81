/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.practice.service.impl;

import org.springblade.modules.practice.excel.PracticeAnswerExcel;
import org.springblade.modules.practice.mapper.PracticeAnswerMapper;
import org.springblade.modules.practice.pojo.entity.PracticeAnswerEntity;
import org.springblade.modules.practice.pojo.vo.PracticeAnswerVO;
import org.springblade.modules.practice.service.IPracticeAnswerService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 练习答案记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Service
public class PracticeAnswerServiceImpl extends BaseServiceImpl<PracticeAnswerMapper, PracticeAnswerEntity> implements IPracticeAnswerService {

	@Override
	public IPage<PracticeAnswerVO> selectPracticeAnswerPage(IPage<PracticeAnswerVO> page, PracticeAnswerVO practiceAnswer) {
		return page.setRecords(baseMapper.selectPracticeAnswerPage(page, practiceAnswer));
	}


	@Override
	public List<PracticeAnswerExcel> exportPracticeAnswer(Wrapper<PracticeAnswerEntity> queryWrapper) {
		List<PracticeAnswerExcel> practiceAnswerList = baseMapper.exportPracticeAnswer(queryWrapper);
		//practiceAnswerList.forEach(practiceAnswer -> {
		//	practiceAnswer.setTypeName(DictCache.getValue(DictEnum.YES_NO, PracticeAnswer.getType()));
		//});
		return practiceAnswerList;
	}

	@Override
	public List<PracticeAnswerEntity> selectWrongQuestionIdsBySubject(Long userId, Long subjectId) {
		return baseMapper.selectWrongQuestionIdsBySubject(userId, subjectId);
	}

}
