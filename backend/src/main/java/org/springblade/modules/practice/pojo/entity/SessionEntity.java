package org.springblade.modules.practice.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 考试会话表 实体
 */
@Data
@TableName("exam_session")
@Schema(description = "考试会话表")
@EqualsAndHashCode(callSuper = true)
public class SessionEntity extends TenantEntity {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "试卷ID")
    private Long paperId;

    @Schema(description = "本次考试题目总量")
    private Integer questionTotal;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束/提交时间")
    private Date endTime;

    @Schema(description = "截止时间（倒计时结束点）")
    private Date expireTime;

    @Schema(description = "题目ID有序列表快照（JSON 数组）")
    private String questionIds;

    @Schema(description = "考试得分")
    private BigDecimal score;

    @Schema(description = "正确率百分比（0-100）")
    private BigDecimal accuracy;

}

