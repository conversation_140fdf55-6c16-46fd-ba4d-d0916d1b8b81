/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.practice.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.IsAdmin;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.practice.excel.PracticeAnswerExcel;
import org.springblade.modules.practice.pojo.entity.PracticeAnswerEntity;
import org.springblade.modules.practice.pojo.vo.PracticeAnswerVO;
import org.springblade.modules.practice.service.IPracticeAnswerService;
import org.springblade.modules.practice.wrapper.PracticeAnswerWrapper;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 练习答案记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-practiceanswer/practiceAnswer")
@Tag(name = "练习答案记录表", description = "练习答案记录表接口")
public class PracticeAnswerController extends BladeController {

	private final IPracticeAnswerService practiceAnswerService;

	/**
	 * 练习答案记录表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入practiceAnswer")
	public R<PracticeAnswerVO> detail(PracticeAnswerEntity practiceAnswer) {
		PracticeAnswerEntity detail = practiceAnswerService.getOne(Condition.getQueryWrapper(practiceAnswer));
		return R.data(PracticeAnswerWrapper.build().entityVO(detail));
	}
	/**
	 * 练习答案记录表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入practiceAnswer")
	public R<IPage<PracticeAnswerVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> practiceAnswer, Query query) {
		IPage<PracticeAnswerEntity> pages = practiceAnswerService.page(Condition.getPage(query), Condition.getQueryWrapper(practiceAnswer, PracticeAnswerEntity.class));
		return R.data(PracticeAnswerWrapper.build().pageVO(pages));
	}

	/**
	 * 练习答案记录表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入practiceAnswer")
	public R<IPage<PracticeAnswerVO>> page(PracticeAnswerVO practiceAnswer, Query query) {
		IPage<PracticeAnswerVO> pages = practiceAnswerService.selectPracticeAnswerPage(Condition.getPage(query), practiceAnswer);
		return R.data(pages);
	}

	/**
	 * 练习答案记录表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入practiceAnswer")
	public R save(@Valid @RequestBody PracticeAnswerEntity practiceAnswer) {
		return R.status(practiceAnswerService.save(practiceAnswer));
	}

	/**
	 * 练习答案记录表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入practiceAnswer")
	public R update(@Valid @RequestBody PracticeAnswerEntity practiceAnswer) {
		return R.status(practiceAnswerService.updateById(practiceAnswer));
	}

	/**
	 * 练习答案记录表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入practiceAnswer")
	public R submit(@Valid @RequestBody PracticeAnswerEntity practiceAnswer) {
		return R.status(practiceAnswerService.saveOrUpdate(practiceAnswer));
	}

	/**
	 * 练习答案记录表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(practiceAnswerService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@IsAdmin
	@GetMapping("/export-practiceAnswer")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入practiceAnswer")
	public void exportPracticeAnswer(@Parameter(hidden = true) @RequestParam Map<String, Object> practiceAnswer, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<PracticeAnswerEntity> queryWrapper = Condition.getQueryWrapper(practiceAnswer, PracticeAnswerEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(PracticeAnswer::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(PracticeAnswerEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<PracticeAnswerExcel> list = practiceAnswerService.exportPracticeAnswer(queryWrapper);
		ExcelUtil.export(response, "练习答案记录表数据" + DateUtil.time(), "练习答案记录表数据表", list, PracticeAnswerExcel.class);
	}

}
