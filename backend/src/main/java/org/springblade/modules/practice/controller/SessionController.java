/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.practice.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.IsAdmin;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.practice.excel.SessionExcel;
import org.springblade.modules.practice.pojo.entity.SessionEntity;
import org.springblade.modules.practice.pojo.vo.SessionVO;
import org.springblade.modules.practice.service.ISessionService;
import org.springblade.modules.practice.wrapper.SessionWrapper;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 考试会话表 控制器
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-session/session")
@Tag(name = "考试会话表", description = "考试会话表接口")
public class SessionController extends BladeController {

	private final ISessionService sessionService;

	/**
	 * 考试会话表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入session")
	public R<SessionVO> detail(SessionEntity session) {
		SessionEntity detail = sessionService.getOne(Condition.getQueryWrapper(session));
		return R.data(SessionWrapper.build().entityVO(detail));
	}
	/**
	 * 考试会话表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入session")
	public R<IPage<SessionVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> session, Query query) {
		IPage<SessionEntity> pages = sessionService.page(Condition.getPage(query), Condition.getQueryWrapper(session, SessionEntity.class));
		return R.data(SessionWrapper.build().pageVO(pages));
	}

	/**
	 * 考试会话表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入session")
	public R<IPage<SessionVO>> page(SessionVO session, Query query) {
		IPage<SessionVO> pages = sessionService.selectSessionPage(Condition.getPage(query), session);
		return R.data(pages);
	}

	/**
	 * 考试会话表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入session")
	public R save(@Valid @RequestBody SessionEntity session) {
		return R.status(sessionService.save(session));
	}

	/**
	 * 考试会话表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入session")
	public R update(@Valid @RequestBody SessionEntity session) {
		return R.status(sessionService.updateById(session));
	}

	/**
	 * 考试会话表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入session")
	public R submit(@Valid @RequestBody SessionEntity session) {
		return R.status(sessionService.saveOrUpdate(session));
	}

	/**
	 * 考试会话表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(sessionService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@IsAdmin
	@GetMapping("/export-session")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入session")
	public void exportSession(@Parameter(hidden = true) @RequestParam Map<String, Object> session, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<SessionEntity> queryWrapper = Condition.getQueryWrapper(session, SessionEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Session::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(SessionEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<SessionExcel> list = sessionService.exportSession(queryWrapper);
		ExcelUtil.export(response, "考试会话表数据" + DateUtil.time(), "考试会话表数据表", list, SessionExcel.class);
	}

}
