<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.feedback.mapper.FeedbackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="feedbackResultMap" type="org.springblade.modules.feedback.pojo.entity.FeedbackEntity">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="type" property="type"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="status" property="status"/>
        <result column="attachments" property="attachments"/>
        <result column="priority" property="priority"/>
        <result column="assigned_to" property="assignedTo"/>
        <result column="resolved_at" property="resolvedAt"/>
        <result column="closed_at" property="closedAt"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectFeedbackPage" resultMap="feedbackResultMap">
        select * from exam_feedback where is_deleted = 0
    </select>


    <select id="exportFeedback" resultType="org.springblade.modules.feedback.excel.FeedbackExcel">
        SELECT * FROM exam_feedback ${ew.customSqlSegment}
    </select>

</mapper>
