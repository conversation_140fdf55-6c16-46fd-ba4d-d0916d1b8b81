<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.feedback.mapper.FeedbackRepliesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="feedbackRepliesResultMap" type="org.springblade.modules.feedback.pojo.entity.FeedbackRepliesEntity">
    </resultMap>


    <select id="selectFeedbackRepliesPage" resultMap="feedbackRepliesResultMap">
        select * from exam_feedback_replies where is_deleted = 0
    </select>


    <select id="exportFeedbackReplies" resultType="org.springblade.modules.feedback.excel.FeedbackRepliesExcel">
        SELECT * FROM exam_feedback_replies ${ew.customSqlSegment}
    </select>

</mapper>
