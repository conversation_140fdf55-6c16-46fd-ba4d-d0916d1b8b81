/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.feedback.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.IsAdmin;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.feedback.excel.FeedbackRepliesExcel;
import org.springblade.modules.feedback.pojo.entity.FeedbackRepliesEntity;
import org.springblade.modules.feedback.pojo.vo.FeedbackRepliesVO;
import org.springblade.modules.feedback.service.IFeedbackRepliesService;
import org.springblade.modules.feedback.wrapper.FeedbackRepliesWrapper;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 反馈回复表 控制器
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-feedbackreplies/feedbackReplies")
@Tag(name = "反馈回复表", description = "反馈回复表接口")
public class FeedbackRepliesController extends BladeController {

	private final IFeedbackRepliesService feedbackRepliesService;

	/**
	 * 反馈回复表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入feedbackReplies")
	public R<FeedbackRepliesVO> detail(FeedbackRepliesEntity feedbackReplies) {
		FeedbackRepliesEntity detail = feedbackRepliesService.getOne(Condition.getQueryWrapper(feedbackReplies));
		return R.data(FeedbackRepliesWrapper.build().entityVO(detail));
	}
	/**
	 * 反馈回复表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入feedbackReplies")
	public R<IPage<FeedbackRepliesVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> feedbackReplies, Query query) {
		IPage<FeedbackRepliesEntity> pages = feedbackRepliesService.page(Condition.getPage(query), Condition.getQueryWrapper(feedbackReplies, FeedbackRepliesEntity.class));
		return R.data(FeedbackRepliesWrapper.build().pageVO(pages));
	}

	/**
	 * 反馈回复表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入feedbackReplies")
	public R<IPage<FeedbackRepliesVO>> page(FeedbackRepliesVO feedbackReplies, Query query) {
		IPage<FeedbackRepliesVO> pages = feedbackRepliesService.selectFeedbackRepliesPage(Condition.getPage(query), feedbackReplies);
		return R.data(pages);
	}

	/**
	 * 反馈回复表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入feedbackReplies")
	public R save(@Valid @RequestBody FeedbackRepliesEntity feedbackReplies) {
		return R.status(feedbackRepliesService.save(feedbackReplies));
	}

	/**
	 * 反馈回复表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入feedbackReplies")
	public R update(@Valid @RequestBody FeedbackRepliesEntity feedbackReplies) {
		return R.status(feedbackRepliesService.updateById(feedbackReplies));
	}

	/**
	 * 反馈回复表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入feedbackReplies")
	public R submit(@Valid @RequestBody FeedbackRepliesEntity feedbackReplies) {
		return R.status(feedbackRepliesService.saveOrUpdate(feedbackReplies));
	}

	/**
	 * 反馈回复表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(feedbackRepliesService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@IsAdmin
	@GetMapping("/export-feedbackReplies")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入feedbackReplies")
	public void exportFeedbackReplies(@Parameter(hidden = true) @RequestParam Map<String, Object> feedbackReplies, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<FeedbackRepliesEntity> queryWrapper = Condition.getQueryWrapper(feedbackReplies, FeedbackRepliesEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(FeedbackReplies::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(FeedbackRepliesEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<FeedbackRepliesExcel> list = feedbackRepliesService.exportFeedbackReplies(queryWrapper);
		ExcelUtil.export(response, "反馈回复表数据" + DateUtil.time(), "反馈回复表数据表", list, FeedbackRepliesExcel.class);
	}

}
