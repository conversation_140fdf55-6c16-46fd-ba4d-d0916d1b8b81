/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.feedback.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.IsAdmin;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.feedback.pojo.entity.FeedbackEntity;
import org.springblade.modules.feedback.pojo.vo.FeedbackVO;
import org.springblade.modules.feedback.excel.FeedbackExcel;
import org.springblade.modules.feedback.wrapper.FeedbackWrapper;
import org.springblade.modules.feedback.service.IFeedbackService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 意见反馈 控制器
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("exam-feedback/feedback")
@Tag(name = "意见反馈", description = "意见反馈接口")
public class FeedbackController extends BladeController {

	private final IFeedbackService feedbackService;

	/**
	 * 意见反馈 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入feedback")
	public R<FeedbackVO> detail(FeedbackEntity feedback) {
		FeedbackEntity detail = feedbackService.getOne(Condition.getQueryWrapper(feedback));
		return R.data(FeedbackWrapper.build().entityVO(detail));
	}
	/**
	 * 意见反馈 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入feedback")
	public R<IPage<FeedbackVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> feedback, Query query) {
		IPage<FeedbackEntity> pages = feedbackService.page(Condition.getPage(query), Condition.getQueryWrapper(feedback, FeedbackEntity.class));
		return R.data(FeedbackWrapper.build().pageVO(pages));
	}

	/**
	 * 意见反馈 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入feedback")
	public R<IPage<FeedbackVO>> page(FeedbackVO feedback, Query query) {
		IPage<FeedbackVO> pages = feedbackService.selectFeedbackPage(Condition.getPage(query), feedback);
		return R.data(pages);
	}

	/**
	 * 意见反馈 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入feedback")
	public R save(@Valid @RequestBody FeedbackEntity feedback) {
		return R.status(feedbackService.save(feedback));
	}

	/**
	 * 意见反馈 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入feedback")
	public R update(@Valid @RequestBody FeedbackEntity feedback) {
		return R.status(feedbackService.updateById(feedback));
	}

	/**
	 * 意见反馈 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入feedback")
	public R submit(@Valid @RequestBody FeedbackEntity feedback) {
		return R.status(feedbackService.saveOrUpdate(feedback));
	}

	/**
	 * 意见反馈 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(feedbackService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@IsAdmin
	@GetMapping("/export-feedback")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入feedback")
	public void exportFeedback(@Parameter(hidden = true) @RequestParam Map<String, Object> feedback, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<FeedbackEntity> queryWrapper = Condition.getQueryWrapper(feedback, FeedbackEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Feedback::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(FeedbackEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<FeedbackExcel> list = feedbackService.exportFeedback(queryWrapper);
		ExcelUtil.export(response, "意见反馈数据" + DateUtil.time(), "意见反馈数据表", list, FeedbackExcel.class);
	}

}
