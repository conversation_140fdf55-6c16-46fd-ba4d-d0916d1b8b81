/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.feedback.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.lang.Boolean;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 意见反馈 实体类
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
@Data
@TableName("exam_feedback")
@Schema(description = "Feedback对象")
@EqualsAndHashCode(callSuper = true)
public class FeedbackEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户ID
	 */
	@Schema(description = "用户ID")
	private Long userId;
	/**
	 * 反馈类型(Bug报告,功能建议,投诉,其他)
	 */
	@Schema(description = "反馈类型(Bug报告,功能建议,投诉,其他)")
	private String type;
	/**
	 * 反馈标题
	 */
	@Schema(description = "反馈标题")
	private String title;
	/**
	 * 反馈内容
	 */
	@Schema(description = "反馈内容")
	private String content;
	/**
	 * 附件信息(JSON格式)
	 */
	@Schema(description = "附件信息(JSON格式)")
	private String attachments;
	/**
	 * 优先级(低,中,高,紧急)
	 */
	@Schema(description = "优先级(低,中,高,紧急)")
	private String priority;
	/**
	 * 分配给(处理人ID)
	 */
	@Schema(description = "分配给(处理人ID)")
	private Long assignedTo;
	/**
	 * 解决时间
	 */
	@Schema(description = "解决时间")
	private Date resolvedAt;
	/**
	 * 关闭时间
	 */
	@Schema(description = "关闭时间")
	private Date closedAt;

}
