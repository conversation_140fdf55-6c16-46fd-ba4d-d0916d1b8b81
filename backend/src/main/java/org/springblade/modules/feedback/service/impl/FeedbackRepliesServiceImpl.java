/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.feedback.service.impl;

import org.springblade.modules.feedback.excel.FeedbackRepliesExcel;
import org.springblade.modules.feedback.mapper.FeedbackRepliesMapper;
import org.springblade.modules.feedback.pojo.entity.FeedbackRepliesEntity;
import org.springblade.modules.feedback.pojo.vo.FeedbackRepliesVO;
import org.springblade.modules.feedback.service.IFeedbackRepliesService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 反馈回复表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Service
public class FeedbackRepliesServiceImpl extends BaseServiceImpl<FeedbackRepliesMapper, FeedbackRepliesEntity> implements IFeedbackRepliesService {

	@Override
	public IPage<FeedbackRepliesVO> selectFeedbackRepliesPage(IPage<FeedbackRepliesVO> page, FeedbackRepliesVO feedbackReplies) {
		return page.setRecords(baseMapper.selectFeedbackRepliesPage(page, feedbackReplies));
	}


	@Override
	public List<FeedbackRepliesExcel> exportFeedbackReplies(Wrapper<FeedbackRepliesEntity> queryWrapper) {
		List<FeedbackRepliesExcel> feedbackRepliesList = baseMapper.exportFeedbackReplies(queryWrapper);
		//feedbackRepliesList.forEach(feedbackReplies -> {
		//	feedbackReplies.setTypeName(DictCache.getValue(DictEnum.YES_NO, FeedbackReplies.getType()));
		//});
		return feedbackRepliesList;
	}

}
