/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.feedback.excel;


import lombok.Data;

import java.util.Date;
import java.lang.Boolean;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.io.Serial;


/**
 * 意见反馈 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class FeedbackExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键ID")
	private Long id;
	/**
	 * 用户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("用户ID")
	private Long userId;
	/**
	 * 反馈类型(Bug报告,功能建议,投诉,其他)
	 */
	@ColumnWidth(20)
	@ExcelProperty("反馈类型(Bug报告,功能建议,投诉,其他)")
	private String type;
	/**
	 * 反馈标题
	 */
	@ColumnWidth(20)
	@ExcelProperty("反馈标题")
	private String title;
	/**
	 * 反馈内容
	 */
	@ColumnWidth(20)
	@ExcelProperty("反馈内容")
	private String content;
	/**
	 * 附件信息(JSON格式)
	 */
	@ColumnWidth(20)
	@ExcelProperty("附件信息(JSON格式)")
	private String attachments;
	/**
	 * 优先级(低,中,高,紧急)
	 */
	@ColumnWidth(20)
	@ExcelProperty("优先级(低,中,高,紧急)")
	private String priority;
	/**
	 * 分配给(处理人ID)
	 */
	@ColumnWidth(20)
	@ExcelProperty("分配给(处理人ID)")
	private Long assignedTo;
	/**
	 * 解决时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("解决时间")
	private Date resolvedAt;
	/**
	 * 关闭时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("关闭时间")
	private Date closedAt;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 是否删除(0-未删除,1-已删除)
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否删除(0-未删除,1-已删除)")
	private Boolean isDeleted;

}
