package org.springblade.modules.papers.mapper;

import org.springblade.modules.papers.pojo.entity.PapersEntity;
import org.springblade.modules.papers.pojo.vo.PapersVO;
import org.springblade.modules.papers.excel.PapersExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 试卷管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
public interface PapersMapper extends BaseMapper<PapersEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param papers 查询参数
	 * @param userRoleIds 用户角色ID列表（逗号分隔）
	 * @return List<PapersVO>
	 */
	List<PapersVO> selectPapersPage(IPage page, PapersVO papers, @Param("userRoleIds") String userRoleIds);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @param userRoleIds 用户角色ID列表（逗号分隔）
	 * @return List<PapersExcel>
	 */
	List<PapersExcel> exportPapers(@Param("ew") Wrapper<PapersEntity> queryWrapper, @Param("userRoleIds") String userRoleIds);

}
