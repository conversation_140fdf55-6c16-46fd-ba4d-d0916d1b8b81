/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.papers.service.impl;

import org.springblade.modules.papers.pojo.entity.PaperItemsEntity;
import org.springblade.modules.papers.pojo.entity.PapersEntity;
import org.springblade.modules.papers.pojo.vo.PaperItemsVO;
import org.springblade.modules.papers.excel.PaperItemsExcel;
import org.springblade.modules.papers.mapper.PaperItemsMapper;
import org.springblade.modules.papers.service.IPaperItemsService;
import org.springblade.modules.papers.service.IPapersService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.List;

/**
 * 试卷题目关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
@Service
public class PaperItemsServiceImpl extends BaseServiceImpl<PaperItemsMapper, PaperItemsEntity> implements IPaperItemsService {

	@Autowired
	private IPapersService papersService;

	@Override
	public IPage<PaperItemsVO> selectPaperItemsPage(IPage<PaperItemsVO> page, PaperItemsVO paperItems) {
		return page.setRecords(baseMapper.selectPaperItemsPage(page, paperItems));
	}

	@Override
	public List<PaperItemsVO> getByPaperId(Long paperId) {
		return baseMapper.selectByPaperId(paperId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean savePaperItems(Long paperId, List<PaperItemsEntity> paperItems) {
		// 先删除原有的题目
		baseMapper.deleteByPaperId(paperId);

		// 计算总分
		BigDecimal totalScore = BigDecimal.ZERO;

		// 批量插入新的题目
		if (paperItems != null && !paperItems.isEmpty()) {
			for (PaperItemsEntity item : paperItems) {
				item.setPaperId(paperId);
				this.save(item);
				// 累加分值
				if (item.getScore() != null) {
					totalScore = totalScore.add(item.getScore());
				}
			}
		}

		// 更新试卷总分
		PapersEntity paper = papersService.getById(paperId);
		if (paper != null) {
			paper.setTotalScore(totalScore);
			papersService.updateById(paper);
		}

		return true;
	}

	@Override
	public boolean removeByPaperId(Long paperId) {
		return baseMapper.deleteByPaperId(paperId) > 0;
	}

	@Override
	public List<PaperItemsExcel> exportPaperItems(Wrapper<PaperItemsEntity> queryWrapper) {
		List<PaperItemsExcel> paperItemsList = baseMapper.exportPaperItems(queryWrapper);
		//paperItemsList.forEach(paperItems -> {
		//	paperItems.setTypeName(DictCache.getValue(DictEnum.YES_NO, PaperItems.getType()));
		//});
		return paperItemsList;
	}

}
