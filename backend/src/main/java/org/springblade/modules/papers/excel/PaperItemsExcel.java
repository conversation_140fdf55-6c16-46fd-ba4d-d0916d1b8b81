/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.papers.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.io.Serial;

/**
 * 试卷题目关联表 Excel实体类
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class PaperItemsExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("主键ID")
	private Long id;
	/**
	 * 试卷ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("试卷ID")
	private Long paperId;
	/**
	 * 题目ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("题目ID")
	private Long questionId;
	/**
	 * 题目分值
	 */
	@ColumnWidth(20)
	@ExcelProperty("题目分值")
	private BigDecimal score;
	/**
	 * 题目排序号
	 */
	@ColumnWidth(20)
	@ExcelProperty("题目排序号")
	private Integer orderNo;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 是否删除(0-未删除,1-已删除)
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否删除(0-未删除,1-已删除)")
	private Boolean isDeleted;

}
