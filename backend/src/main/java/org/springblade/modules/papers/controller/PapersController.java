/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.papers.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.IsAdmin;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.papers.pojo.entity.PapersEntity;
import org.springblade.modules.papers.pojo.vo.PapersVO;
import org.springblade.modules.papers.excel.PapersExcel;
import org.springblade.modules.papers.wrapper.PapersWrapper;
import org.springblade.modules.papers.service.IPapersService;
import org.springblade.modules.papers.service.IPaperItemsService;
import org.springblade.modules.papers.pojo.entity.PaperItemsEntity;
import org.springblade.modules.papers.pojo.vo.PaperItemsVO;
import org.springblade.modules.common.dto.PermissionUpdateDTO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 试卷管理 控制器
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("exam-papers/papers")
@Tag(name = "试卷管理", description = "试卷管理接口")
public class PapersController extends BladeController {

	private final IPapersService papersService;
	private final IPaperItemsService paperItemsService;

	/**
	 * 试卷管理 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入papers")
	public R<PapersVO> detail(PapersEntity papers) {
		PapersEntity detail = papersService.getOne(Condition.getQueryWrapper(papers));
		return R.data(PapersWrapper.build().entityVO(detail));
	}
	/**
	 * 试卷管理 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入papers")
	public R<IPage<PapersVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> papers, Query query) {
		IPage<PapersEntity> pages = papersService.page(Condition.getPage(query), Condition.getQueryWrapper(papers, PapersEntity.class));
		return R.data(PapersWrapper.build().pageVO(pages));
	}

	/**
	 * 试卷管理 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入papers")
	public R<IPage<PapersVO>> page(PapersVO papers, Query query) {
		IPage<PapersVO> pages = papersService.selectPapersPage(Condition.getPage(query), papers);
		return R.data(pages);
	}

	/**
	 * 试卷管理 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入papers")
	public R save(@Valid @RequestBody PapersEntity papers) {
		return R.status(papersService.save(papers));
	}

	/**
	 * 试卷管理 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入papers")
	public R update(@Valid @RequestBody PapersEntity papers) {
		return R.status(papersService.updateById(papers));
	}

	/**
	 * 试卷管理 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入papers")
	public R submit(@Valid @RequestBody PapersEntity papers) {
		return R.status(papersService.saveOrUpdate(papers));
	}

	/**
	 * 试卷管理 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(papersService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@IsAdmin
	@GetMapping("/export-papers")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入papers")
	public void exportPapers(@Parameter(hidden = true) @RequestParam Map<String, Object> papers, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<PapersEntity> queryWrapper = Condition.getQueryWrapper(papers, PapersEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Papers::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(PapersEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<PapersExcel> list = papersService.exportPapers(queryWrapper);
		ExcelUtil.export(response, "试卷管理数据" + DateUtil.time(), "试卷管理数据表", list, PapersExcel.class);
	}

	/**
	 * 创建试卷草稿
	 */
	@PostMapping("/create-draft")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "创建试卷草稿", description = "传入papers")
	public R<PapersVO> createDraft(@Valid @RequestBody PapersEntity papers) {
		PapersEntity result = papersService.createDraft(papers);
		return R.data(PapersWrapper.build().entityVO(result));
	}

	/**
	 * 获取试卷题目列表
	 */
	@GetMapping("/{paperId}/items")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "获取试卷题目列表", description = "传入paperId")
	public R<List<PaperItemsVO>> getPaperItems(@PathVariable Long paperId) {
		List<PaperItemsVO> list = paperItemsService.getByPaperId(paperId);
		return R.data(list);
	}

	/**
	 * 批量设置试卷题目
	 */
	@PostMapping("/{paperId}/items")
	@ApiOperationSupport(order = 12)
	@Operation(summary = "批量设置试卷题目", description = "传入paperId和题目列表")
	public R<String> savePaperItems(@PathVariable Long paperId, @RequestBody List<PaperItemsEntity> paperItems) {
		boolean result = paperItemsService.savePaperItems(paperId, paperItems);
		return R.status(result);
	}

	/**
	 * 发布试卷
	 */
	@PostMapping("/{paperId}/publish")
	@ApiOperationSupport(order = 13)
	@Operation(summary = "发布试卷", description = "传入paperId")
	public R<String> publishPaper(@PathVariable Long paperId) {
		boolean result = papersService.publishPaper(paperId);
		return result ? R.success("试卷发布成功") : R.fail("试卷发布失败，请检查试卷状态");
	}

	/**
	 * 归档试卷
	 */
	@PostMapping("/{paperId}/archive")
	@ApiOperationSupport(order = 14)
	@Operation(summary = "归档试卷", description = "传入paperId")
	public R<String> archivePaper(@PathVariable Long paperId) {
		boolean result = papersService.archivePaper(paperId);
		return result ? R.success("试卷归档成功") : R.fail("试卷归档失败，请检查试卷状态");
	}

	/**
	 * 更新数据权限
	 */
	@PostMapping("/update-permissions")
	@ApiOperationSupport(order = 15)
	@Operation(summary = "更新数据权限", description = "传入权限更新参数")
	public R updatePermissions(@Valid @RequestBody PermissionUpdateDTO permissionUpdateDTO) {
		boolean result = papersService.updatePermissions(permissionUpdateDTO);
		return R.status(result);
	}

}
