<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.papers.mapper.PapersMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="papersResultMap" type="org.springblade.modules.papers.pojo.entity.PapersEntity">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="duration_minutes" property="durationMinutes"/>
        <result column="pass_score" property="passScore"/>
        <result column="total_score" property="totalScore"/>
        <result column="shuffle" property="shuffle"/>
        <result column="allow_pause" property="allowPause"/>
        <result column="show_result_detail" property="showResultDetail"/>
        <result column="publish_at" property="publishAt"/>
        <result column="role_ids" property="roleIds"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="papersVOResultMap" type="org.springblade.modules.papers.pojo.vo.PapersVO" extends="papersResultMap">
    </resultMap>

    <select id="selectPapersPage" resultMap="papersVOResultMap">
        select
            *
        from exam_papers
        where
            is_deleted = 0
            <!-- 权限控制：只显示用户有权限查看的数据，管理员角色不受限制 -->
            <if test="userRoleIds != null and userRoleIds != ''">
                AND (
                    role_ids IS NULL
                    OR role_ids = ''
                    OR role_ids REGEXP CONCAT('(^|,)(', REPLACE(#{userRoleIds}, ',', '|'), ')(,|$)')
                )
            </if>
    </select>


    <select id="exportPapers" resultType="org.springblade.modules.papers.excel.PapersExcel">
        SELECT * FROM exam_papers
        WHERE 1=1
        <!-- 权限控制：只导出用户有权限查看的数据 -->
        <if test="userRoleIds != null and userRoleIds != ''">
            AND (
                role_ids IS NULL
                OR role_ids = ''
                OR role_ids REGEXP CONCAT('(^|,)(', REPLACE(#{userRoleIds}, ',', '|'), ')(,|$)')
            )
        </if>
        ${ew.customSqlSegment}
    </select>

</mapper>
