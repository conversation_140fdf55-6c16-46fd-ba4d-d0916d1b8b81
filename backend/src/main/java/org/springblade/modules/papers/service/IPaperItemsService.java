package org.springblade.modules.papers.service;

import org.springblade.modules.papers.pojo.entity.PaperItemsEntity;
import org.springblade.modules.papers.pojo.vo.PaperItemsVO;
import org.springblade.modules.papers.excel.PaperItemsExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.springblade.core.mp.base.BaseService;
import java.util.List;

/**
 * 试卷题目关联表 服务类
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
public interface IPaperItemsService extends BaseService<PaperItemsEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param paperItems 查询参数
	 * @return IPage<PaperItemsVO>
	 */
	IPage<PaperItemsVO> selectPaperItemsPage(IPage<PaperItemsVO> page, PaperItemsVO paperItems);

	/**
	 * 根据试卷ID获取题目列表
	 *
	 * @param paperId 试卷ID
	 * @return List<PaperItemsVO>
	 */
	List<PaperItemsVO> getByPaperId(Long paperId);

	/**
	 * 批量设置试卷题目
	 *
	 * @param paperId 试卷ID
	 * @param paperItems 题目列表
	 * @return boolean
	 */
	boolean savePaperItems(Long paperId, List<PaperItemsEntity> paperItems);

	/**
	 * 根据试卷ID删除题目
	 *
	 * @param paperId 试卷ID
	 * @return boolean
	 */
	boolean removeByPaperId(Long paperId);

	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<PaperItemsExcel>
	 */
	List<PaperItemsExcel> exportPaperItems(Wrapper<PaperItemsEntity> queryWrapper);

}
