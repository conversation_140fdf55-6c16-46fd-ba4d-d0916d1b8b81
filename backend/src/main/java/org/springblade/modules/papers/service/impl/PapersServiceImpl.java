package org.springblade.modules.papers.service.impl;

import org.springblade.modules.papers.pojo.entity.PapersEntity;
import org.springblade.modules.papers.pojo.vo.PapersVO;
import org.springblade.modules.papers.excel.PapersExcel;
import org.springblade.modules.papers.mapper.PapersMapper;
import org.springblade.modules.papers.service.IPapersService;
import org.springblade.modules.papers.constant.PaperStatusConstant;
import org.springblade.modules.common.dto.PermissionUpdateDTO;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import java.util.Date;
import java.util.stream.Collectors;

/**
 * 试卷管理 服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
@Slf4j
@Service
public class PapersServiceImpl extends BaseServiceImpl<PapersMapper, PapersEntity> implements IPapersService {

	@Override
	public IPage<PapersVO> selectPapersPage(IPage<PapersVO> page, PapersVO papers) {
		// 获取当前用户信息
		BladeUser user = AuthUtil.getUser();
		String userRoleIds = user != null ? user.getRoleId() : "";

		// 管理员角色不受权限控制限制，传入空字符串跳过权限过滤
		if (AuthUtil.isAdministrator()) {
			userRoleIds = "";
		}

		return page.setRecords(baseMapper.selectPapersPage(page, papers, userRoleIds));
	}


	@Override
	public List<PapersExcel> exportPapers(Wrapper<PapersEntity> queryWrapper) {
		// 获取当前用户信息
		BladeUser user = AuthUtil.getUser();
		String userRoleIds = user != null ? user.getRoleId() : "";

		// 管理员角色不受权限控制限制，传入空字符串跳过权限过滤
		if (AuthUtil.isAdministrator()) {
			userRoleIds = "";
		}

		List<PapersExcel> papersList = baseMapper.exportPapers(queryWrapper, userRoleIds);
		//papersList.forEach(papers -> {
		//	papers.setTypeName(DictCache.getValue(DictEnum.YES_NO, Papers.getType()));
		//});
		return papersList;
	}

	@Override
	public boolean publishPaper(Long paperId) {
		PapersEntity paper = this.getById(paperId);
		if (paper != null && paper.getStatus().equals(PaperStatusConstant.DRAFT)) { // 只有草稿状态才能发布
			paper.setStatus(PaperStatusConstant.PUBLISHED); // 设置为已发布
			paper.setPublishAt(new java.util.Date());
			return this.updateById(paper);
		}
		return false;
	}

	@Override
	public boolean archivePaper(Long paperId) {
		PapersEntity paper = this.getById(paperId);
		if (paper != null && paper.getStatus().equals(PaperStatusConstant.PUBLISHED)) { // 只有已发布状态才能归档
			paper.setStatus(PaperStatusConstant.ARCHIVED); // 设置为已归档
			return this.updateById(paper);
		}
		return false;
	}

	@Override
	public PapersEntity createDraft(PapersEntity papers) {
		papers.setStatus(PaperStatusConstant.DRAFT); // 设置为草稿状态
		this.save(papers);
		return papers;
	}

	@Override
	public boolean updateTotalScore(Long paperId, java.math.BigDecimal totalScore) {
		PapersEntity paper = this.getById(paperId);
		if (paper != null) {
			paper.setTotalScore(totalScore);
			return this.updateById(paper);
		}
		return false;
	}

	@Override
	public int autoPublishPapers() {
		log.info("开始执行试卷自动发布任务");

		try {
			// 构建更新条件：状态为草稿且发布时间不为空且发布时间小于等于当前日期
			LambdaUpdateWrapper<PapersEntity> updateWrapper = new LambdaUpdateWrapper<>();
			updateWrapper.eq(PapersEntity::getStatus, PaperStatusConstant.DRAFT)
						.isNotNull(PapersEntity::getPublishAt)
						.le(PapersEntity::getPublishAt, new Date())
						.set(PapersEntity::getStatus, PaperStatusConstant.PUBLISHED);

			// 执行批量更新
			int updateCount = baseMapper.update(null, updateWrapper);

			log.info("试卷自动发布任务完成，共发布了 {} 份试卷", updateCount);
			return updateCount;

		} catch (Exception e) {
			log.error("试卷自动发布任务执行失败", e);
			return 0;
		}
	}

	@Override
	public boolean updatePermissions(PermissionUpdateDTO permissionUpdateDTO) {
		if (permissionUpdateDTO == null || Func.isEmpty(permissionUpdateDTO.getIds())) {
			return false;
		}

		List<Long> ids = permissionUpdateDTO.getIds();
		String roleIdsStr = permissionUpdateDTO.getRoleIds() != null ?
			permissionUpdateDTO.getRoleIds().stream()
				.map(String::valueOf)
				.collect(Collectors.joining(",")) : "";

		QueryWrapper<PapersEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.in("id", ids);

		PapersEntity updateEntity = new PapersEntity();
		updateEntity.setRoleIds(roleIdsStr);

		return this.update(updateEntity, queryWrapper);
	}

}
