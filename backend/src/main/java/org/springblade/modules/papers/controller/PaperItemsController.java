/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.papers.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.papers.pojo.entity.PaperItemsEntity;
import org.springblade.modules.papers.pojo.vo.PaperItemsVO;
import org.springblade.modules.papers.wrapper.PaperItemsWrapper;
import org.springblade.modules.papers.service.IPaperItemsService;
import org.springblade.core.boot.ctrl.BladeController;
import java.util.List;

/**
 * 试卷题目关联表 控制器
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("exam-papers/paper-items")
@Tag(name = "试卷题目关联管理", description = "试卷题目关联管理接口")
public class PaperItemsController extends BladeController {

	private final IPaperItemsService paperItemsService;

	/**
	 * 试卷题目关联表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入paperItems")
	public R<PaperItemsVO> detail(PaperItemsEntity paperItems) {
		PaperItemsEntity detail = paperItemsService.getOne(Condition.getQueryWrapper(paperItems));
		return R.data(PaperItemsWrapper.build().entityVO(detail));
	}

	/**
	 * 试卷题目关联表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入paperItems")
	public R<IPage<PaperItemsVO>> list(PaperItemsVO paperItems, Query query) {
		IPage<PaperItemsVO> pages = paperItemsService.selectPaperItemsPage(Condition.getPage(query), paperItems);
		return R.data(pages);
	}

	/**
	 * 试卷题目关联表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "新增", description = "传入paperItems")
	public R<String> save(@Valid @RequestBody PaperItemsEntity paperItems) {
		return R.status(paperItemsService.save(paperItems));
	}

	/**
	 * 试卷题目关联表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "修改", description = "传入paperItems")
	public R<String> update(@Valid @RequestBody PaperItemsEntity paperItems) {
		return R.status(paperItemsService.updateById(paperItems));
	}

	/**
	 * 试卷题目关联表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "新增或修改", description = "传入paperItems")
	public R<String> submit(@Valid @RequestBody PaperItemsEntity paperItems) {
		return R.status(paperItemsService.saveOrUpdate(paperItems));
	}

	/**
	 * 试卷题目关联表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R<String> remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(paperItemsService.deleteLogic(org.springblade.core.tool.utils.Func.toLongList(ids)));
	}

	/**
	 * 根据试卷ID获取题目列表
	 */
	@GetMapping("/by-paper/{paperId}")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "根据试卷ID获取题目列表", description = "传入paperId")
	public R<List<PaperItemsVO>> getByPaperId(@PathVariable Long paperId) {
		List<PaperItemsVO> list = paperItemsService.getByPaperId(paperId);
		return R.data(list);
	}

	/**
	 * 批量设置试卷题目
	 */
	@PostMapping("/batch-save/{paperId}")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "批量设置试卷题目", description = "传入paperId和题目列表")
	public R<String> batchSave(@PathVariable Long paperId, @RequestBody List<PaperItemsEntity> paperItems) {
		boolean result = paperItemsService.savePaperItems(paperId, paperItems);
		return R.status(result);
	}

}
