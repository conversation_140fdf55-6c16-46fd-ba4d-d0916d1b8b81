package org.springblade.modules.papers.mapper;

import org.springblade.modules.papers.pojo.entity.PaperItemsEntity;
import org.springblade.modules.papers.pojo.vo.PaperItemsVO;
import org.springblade.modules.papers.excel.PaperItemsExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 试卷题目关联表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
public interface PaperItemsMapper extends BaseMapper<PaperItemsEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param paperItems 查询参数
	 * @return List<PaperItemsVO>
	 */
	List<PaperItemsVO> selectPaperItemsPage(IPage page, PaperItemsVO paperItems);

	/**
	 * 根据试卷ID获取题目列表
	 *
	 * @param paperId 试卷ID
	 * @return List<PaperItemsVO>
	 */
	List<PaperItemsVO> selectByPaperId(@Param("paperId") Long paperId);

	/**
	 * 根据试卷ID删除题目
	 *
	 * @param paperId 试卷ID
	 * @return int
	 */
	int deleteByPaperId(@Param("paperId") Long paperId);

	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<PaperItemsExcel>
	 */
	List<PaperItemsExcel> exportPaperItems(@Param("ew") Wrapper<PaperItemsEntity> queryWrapper);

}
