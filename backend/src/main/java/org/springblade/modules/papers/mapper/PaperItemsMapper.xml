<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.papers.mapper.PaperItemsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="paperItemsResultMap" type="org.springblade.modules.papers.pojo.entity.PaperItemsEntity">
        <id column="id" property="id"/>
        <result column="paper_id" property="paperId"/>
        <result column="question_id" property="questionId"/>
        <result column="score" property="score"/>
        <result column="order_no" property="orderNo"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 自定义分页查询 -->
    <select id="selectPaperItemsPage" resultType="org.springblade.modules.papers.pojo.vo.PaperItemsVO">
        SELECT
            pi.id,
            pi.paper_id,
            pi.question_id,
            pi.score,
            pi.order_no,
            pi.tenant_id,
            pi.create_user,
            pi.create_dept,
            pi.create_time,
            pi.update_user,
            pi.update_time,
            pi.status,
            pi.is_deleted,
            q.content as question_content,
            q.type as question_type,
            s.name as subject_name
        FROM exam_paper_items pi
        LEFT JOIN exam_questions q ON pi.question_id = q.id
        LEFT JOIN exam_subjects s ON q.subject_id = s.id
        WHERE pi.is_deleted = 0
        <if test="paperItems.paperId != null">
            AND pi.paper_id = #{paperItems.paperId}
        </if>
        <if test="paperItems.questionId != null">
            AND pi.question_id = #{paperItems.questionId}
        </if>
        ORDER BY pi.order_no ASC, pi.create_time DESC
    </select>

    <!-- 根据试卷ID获取题目列表 -->
    <select id="selectByPaperId" resultType="org.springblade.modules.papers.pojo.vo.PaperItemsVO">
        SELECT
            pi.id,
            pi.paper_id,
            pi.question_id,
            pi.score,
            pi.order_no,
            pi.tenant_id,
            pi.create_user,
            pi.create_dept,
            pi.create_time,
            pi.update_user,
            pi.update_time,
            pi.status,
            pi.is_deleted,
            q.content as question_content,
            q.type as question_type,
            s.name as subject_name
        FROM exam_paper_items pi
        LEFT JOIN exam_questions q ON pi.question_id = q.id
        LEFT JOIN exam_subjects s ON q.subject_id = s.id
        WHERE pi.is_deleted = 0
        AND pi.paper_id = #{paperId}
        ORDER BY pi.order_no ASC
    </select>

    <!-- 根据试卷ID删除题目 -->
    <update id="deleteByPaperId">
        UPDATE exam_paper_items
        SET is_deleted = 1, update_time = NOW()
        WHERE paper_id = #{paperId} AND is_deleted = 0
    </update>

    <!-- 获取导出数据 -->
    <select id="exportPaperItems" resultType="org.springblade.modules.papers.excel.PaperItemsExcel">
        SELECT
            pi.id,
            pi.paper_id,
            pi.question_id,
            pi.score,
            pi.order_no,
            pi.tenant_id,
            pi.is_deleted
        FROM exam_paper_items pi
        ${ew.customSqlSegment}
    </select>

</mapper>
