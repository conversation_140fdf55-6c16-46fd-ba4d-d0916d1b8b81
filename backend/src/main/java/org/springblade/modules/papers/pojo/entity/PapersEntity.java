/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.papers.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.lang.Boolean;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 试卷管理 实体类
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
@Data
@TableName("exam_papers")
@Schema(description = "Papers对象")
@EqualsAndHashCode(callSuper = true)
public class PapersEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 试卷名称
	 */
	@Schema(description = "试卷名称")
	private String name;
	/**
	 * 试卷描述
	 */
	@Schema(description = "试卷描述")
	private String description;
	/**
	 * 考试时长(分钟)
	 */
	@Schema(description = "考试时长(分钟)")
	private Integer durationMinutes;
	/**
	 * 及格分数
	 */
	@Schema(description = "及格分数")
	private BigDecimal passScore;
	/**
	 * 总分
	 */
	@Schema(description = "总分")
	private BigDecimal totalScore;
	/**
	 * 是否打乱题目顺序(0-否,1-是)
	 */
	@Schema(description = "是否打乱题目顺序(0-否,1-是)")
	private Boolean shuffle;
	/**
	 * 是否允许暂停(0-否,1-是)
	 */
	@Schema(description = "是否允许暂停(0-否,1-是)")
	private Boolean allowPause;
	/**
	 * 是否显示结果详情(0-否,1-是)
	 */
	@Schema(description = "是否显示结果详情(0-否,1-是)")
	private Boolean showResultDetail;
	/**
	 * 发布时间
	 */
	@Schema(description = "发布时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date publishAt;

	/**
	 * 权限角色ID数组(JSON格式)
	 */
	@Schema(description = "权限角色ID数组(JSON格式)")
	private String roleIds;

}
