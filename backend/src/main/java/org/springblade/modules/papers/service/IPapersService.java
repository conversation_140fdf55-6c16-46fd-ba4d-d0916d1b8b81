/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.papers.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.springblade.modules.papers.pojo.entity.PapersEntity;
import org.springblade.modules.papers.pojo.vo.PapersVO;
import org.springblade.modules.papers.excel.PapersExcel;
import org.springblade.modules.common.dto.PermissionUpdateDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import java.util.List;

/**
 * 试卷管理 服务类
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
public interface IPapersService extends BaseService<PapersEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param papers 查询参数
	 * @return IPage<PapersVO>
	 */
	IPage<PapersVO> selectPapersPage(IPage<PapersVO> page, PapersVO papers);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<PapersExcel>
	 */
	List<PapersExcel> exportPapers(Wrapper<PapersEntity> queryWrapper);

	/**
	 * 发布试卷
	 *
	 * @param paperId 试卷ID
	 * @return boolean
	 */
	boolean publishPaper(Long paperId);

	/**
	 * 归档试卷
	 *
	 * @param paperId 试卷ID
	 * @return boolean
	 */
	boolean archivePaper(Long paperId);

	/**
	 * 创建试卷草稿
	 *
	 * @param papers 试卷信息
	 * @return PapersEntity
	 */
	PapersEntity createDraft(PapersEntity papers);

	/**
	 * 更新试卷总分
	 *
	 * @param paperId 试卷ID
	 * @param totalScore 总分
	 * @return boolean
	 */
	boolean updateTotalScore(Long paperId, java.math.BigDecimal totalScore);

	/**
	 * 自动发布到期的试卷
	 * 将发布时间小于等于当前日期且状态为草稿的试卷更新为已发布状态
	 *
	 * @return 更新的试卷数量
	 */
	int autoPublishPapers();

	/**
	 * 更新数据权限
	 *
	 * @param permissionUpdateDTO 权限更新参数
	 * @return boolean
	 */
	boolean updatePermissions(PermissionUpdateDTO permissionUpdateDTO);

}
