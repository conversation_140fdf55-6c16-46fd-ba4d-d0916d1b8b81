package org.springblade.modules.papers.task;

import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.papers.service.IPapersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 试卷定时任务
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
@Slf4j
@Component
public class PaperScheduledTask {

	@Autowired
	private IPapersService papersService;

	/**
	 * 自动发布试卷定时任务
	 * 每天早上7点执行，将发布时间小于等于当天的草稿状态试卷更新为已发布状态
	 */
	@Scheduled(cron = "0 0 7 * * ?")
	public void autoPublishPapers() {
		log.info("=== 开始执行试卷自动发布定时任务 ===");

		try {
			int publishedCount = papersService.autoPublishPapers();

			if (publishedCount > 0) {
				log.info("=== 试卷自动发布定时任务执行成功，共发布 {} 份试卷 ===", publishedCount);
			} else {
				log.info("=== 试卷自动发布定时任务执行完成，没有需要发布的试卷 ===");
			}

		} catch (Exception e) {
			log.error("=== 试卷自动发布定时任务执行失败 ===", e);
		}
	}

}
