/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.system.rule.constant;

/**
 * 租户规则常量
 *
 * <AUTHOR>
 */
public interface TenantRuleConstant {
	/**
	 * 租户规则链ID
	 */
	String TENANT_CHAIN_ID = "tenantChain";

	/**
	 * 租户规则
	 */
	String TENANT_RULE = "tenantRule";
	/**
	 * 角色规则
	 */
	String TENANT_ROLE_RULE = "tenantRoleRule";
	/**
	 * 角色菜单规则
	 */
	String TENANT_ROLE_MENU_RULE = "tenantRoleMenuRule";
	/**
	 * 部门规则
	 */
	String TENANT_DEPT_RULE = "tenantDeptRule";
	/**
	 * 岗位规则
	 */
	String TENANT_POST_RULE = "tenantPostRule";
	/**
	 * 字典业务规则
	 */
	String TENANT_DICT_BIZ_RULE = "tenantDictBizRule";
	/**
	 * 用户规则
	 */
	String TENANT_USER_RULE = "tenantUserRule";
}
