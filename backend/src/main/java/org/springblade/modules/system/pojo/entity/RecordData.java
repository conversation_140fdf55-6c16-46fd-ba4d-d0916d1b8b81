/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.system.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 数据审计表 实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("blade_record_data")
@Schema(description = "RecordData对象")
public class RecordData implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "主键")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 服务ID
	 */
	@Schema(description = "服务ID")
	private String serviceId;
	/**
	 * 服务器名
	 */
	@Schema(description = "服务器名")
	private String serverHost;
	/**
	 * 服务器IP地址
	 */
	@Schema(description = "服务器IP地址")
	private String serverIp;
	/**
	 * 服务器环境
	 */
	@Schema(description = "服务器环境")
	private String env;
	/**
	 * 审计级别
	 */
	@Schema(description = "审计级别")
	private String recordLevel;
	/**
	 * 操作方式
	 */
	@Schema(description = "操作方式")
	private String method;
	/**
	 * 请求URI
	 */
	@Schema(description = "请求URI")
	private String requestUri;
	/**
	 * 用户代理
	 */
	@Schema(description = "用户代理")
	private String userAgent;
	/**
	 * 操作IP地址
	 */
	@Schema(description = "操作IP地址")
	private String remoteIp;
	/**
	 * 操作类型
	 */
	@Schema(description = "操作类型")
	private String operation;
	/**
	 * 数据表名
	 */
	@Schema(description = "数据表名")
	private String tableName;
	/**
	 * 操作前参数
	 */
	@Schema(description = "操作前参数")
	private String oldData;
	/**
	 * 操作后参数
	 */
	@Schema(description = "操作后参数")
	private String newData;
	/**
	 * 审计消息
	 */
	@Schema(description = "审计消息")
	private String recordMessage;
	/**
	 * 审计结果
	 */
	@Schema(description = "审计结果")
	private String recordResult;
	/**
	 * 记录耗时
	 */
	@Schema(description = "记录耗时")
	private String recordCost;
	/**
	 * 记录时间
	 */
	@Schema(description = "记录时间")
	private LocalDateTime recordTime;
	/**
	 * 记录人
	 */
	@Schema(description = "记录人")
	private Long recordUser;

	/**
	 * 业务状态
	 */
	@Schema(description = "业务状态")
	private Integer status;

	/**
	 * 是否已删除
	 */
	@TableLogic
	@Schema(description = "是否已删除")
	private Integer isDeleted;

}
