<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.system.mapper.LogErrorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="errorLogResultMap" type="org.springblade.core.log.model.LogError">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="service_id" property="serviceId"/>
        <result column="server_host" property="serverHost"/>
        <result column="server_ip" property="serverIp"/>
        <result column="env" property="env"/>
        <result column="method" property="method"/>
        <result column="request_uri" property="requestUri"/>
        <result column="user_agent" property="userAgent"/>
        <result column="stack_trace" property="stackTrace"/>
        <result column="exception_name" property="exceptionName"/>
        <result column="message" property="message"/>
        <result column="line_number" property="lineNumber"/>
        <result column="method_class" property="methodClass"/>
        <result column="file_name" property="fileName"/>
        <result column="method_name" property="methodName"/>
        <result column="params" property="params"/>
        <result column="create_by" property="createBy"/>
    </resultMap>

</mapper>
