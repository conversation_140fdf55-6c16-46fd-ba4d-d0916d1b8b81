package org.springblade.modules.common.util;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;

import java.util.Arrays;
import java.util.List;

/**
 * 权限控制工具类
 *
 * <AUTHOR>
 * @since 2025-09-12
 */
public class PermissionUtil {

    /**
     * 管理员角色别名
     */
    private static final String ADMINISTRATOR_ROLE = "administrator";
    private static final String ADMIN_ROLE = "admin";

    /**
     * 检查用户是否有权限查看指定数据
     *
     * @param dataRoleIds 数据的角色ID列表（逗号分隔）
     * @return true-有权限，false-无权限
     */
    public static boolean hasPermission(String dataRoleIds) {
        // 管理员角色不受权限控制限制
        if (isAdministrator()) {
            return true;
        }

        // 获取当前用户
        BladeUser user = AuthUtil.getUser();
        if (user == null) {
            return false;
        }

        return hasPermission(dataRoleIds, user.getRoleId());
    }

    /**
     * 检查指定用户角色是否有权限查看指定数据
     *
     * @param dataRoleIds 数据的角色ID列表（逗号分隔）
     * @param userRoleIds 用户的角色ID列表（逗号分隔）
     * @return true-有权限，false-无权限
     */
    public static boolean hasPermission(String dataRoleIds, String userRoleIds) {
        // 检查是否为管理员角色
        if (isAdministrator(userRoleIds)) {
            return true;
        }

        // 如果数据没有设置权限控制，则所有人都可以查看
        if (Func.isEmpty(dataRoleIds)) {
            return true;
        }

        // 如果用户没有角色，则无权限
        if (Func.isEmpty(userRoleIds)) {
            return false;
        }

        // 将角色ID字符串转换为列表
        List<String> dataRoles = Arrays.asList(dataRoleIds.split(","));
        List<String> userRoles = Arrays.asList(userRoleIds.split(","));

        // 检查用户角色是否与数据权限角色有交集
        return userRoles.stream().anyMatch(userRole ->
            dataRoles.contains(userRole.trim())
        );
    }

    /**
     * 检查当前用户是否为管理员角色
     *
     * @return true-是管理员，false-不是管理员
     */
    public static boolean isAdministrator() {
        BladeUser user = AuthUtil.getUser();
        if (user == null) {
            return false;
        }
        return isAdministrator(user.getRoleId());
    }

    /**
     * 检查指定角色是否为管理员角色
     *
     * @param userRoleIds 用户角色ID列表（逗号分隔）
     * @return true-是管理员，false-不是管理员
     */
    public static boolean isAdministrator(String userRoleIds) {
        if (Func.isEmpty(userRoleIds)) {
            return false;
        }

        List<String> userRoles = Arrays.asList(userRoleIds.split(","));
        return userRoles.stream().anyMatch(role ->
            ADMINISTRATOR_ROLE.equals(role.trim()) || ADMIN_ROLE.equals(role.trim())
        );
    }

    /**
     * 获取当前用户的角色ID列表
     *
     * @return 角色ID列表（逗号分隔），如果用户未登录则返回空字符串
     */
    public static String getCurrentUserRoleIds() {
        BladeUser user = AuthUtil.getUser();
        return user != null ? user.getRoleId() : "";
    }

    /**
     * 构建权限过滤的SQL条件
     *
     * @param roleColumnName 角色字段名（如：role_ids）
     * @param userRoleIds 用户角色ID列表（逗号分隔）
     * @return SQL条件字符串
     */
    public static String buildPermissionSql(String roleColumnName, String userRoleIds) {
        if (Func.isEmpty(userRoleIds)) {
            return "1=0"; // 无角色则无权限
        }

        StringBuilder sql = new StringBuilder();
        sql.append("(");
        sql.append(roleColumnName).append(" IS NULL OR ");
        sql.append(roleColumnName).append(" = '' OR ");

        String[] roles = userRoleIds.split(",");
        for (int i = 0; i < roles.length; i++) {
            if (i > 0) {
                sql.append(" OR ");
            }
            sql.append("FIND_IN_SET('").append(roles[i].trim()).append("', ").append(roleColumnName).append(") > 0");
        }

        sql.append(")");
        return sql.toString();
    }
}
