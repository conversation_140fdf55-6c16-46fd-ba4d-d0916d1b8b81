package org.springblade.modules.auth.endpoint;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.NoArgsConstructor;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthRequest;
import me.zhyd.oauth.utils.AuthStateUtils;
import org.springblade.core.launch.constant.AppConstant;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.oauth2.granter.TokenGranter;
import org.springblade.core.oauth2.granter.TokenGranterFactory;
import org.springblade.core.oauth2.handler.AuthorizationHandler;
import org.springblade.core.oauth2.handler.TokenHandler;
import org.springblade.core.oauth2.provider.OAuth2Request;
import org.springblade.core.oauth2.provider.OAuth2Token;
import org.springblade.core.oauth2.service.OAuth2User;
import org.springblade.core.social.utils.SocialUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.modules.auth.request.IdaasRequest;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import static org.springblade.core.oauth2.constant.OAuth2TokenConstant.DEFAULT_TENANT_ID;

@NonDS
@ApiSort(1)
@RestController
@NoArgsConstructor(force = true)
@RequestMapping(AppConstant.APPLICATION_AUTH_NAME)
@Tag(description = "IDAAS授权认证", name = "授权接口")
public class IdassEndpoint {

    // 读配置
    @Value("${idaas.loc.client}")
    private String locClient;
    @Value("${idaas.loc.secret}")
    private String locSecret;
    @Value("${idaas.pic.client}")
    private String picClient;
    @Value("${idaas.pic.secret}")
    private String picSecret;
    @Value("${idaas.loc.redirect}")
    private String locRedirect;
    @Value("${idaas.loc.server}")
    private String locIDaaSServer;
    @Value("${idaas.loc.login}")
    private String locLogin;
    @Value("${idaas.pic.redirect}")
    private String picRedirect;
    @Value("${idaas.pic.server}")
    private String picIDaaSServer;
    @Value("${idaas.pic.login}")
    private String picLogin;

    @Autowired
    private final IUserService userService;

    @Autowired
    private final StringRedisTemplate stringRedisTemplate;

    @Autowired
    private final TokenGranterFactory granterFactory;

    @Autowired
    private final AuthorizationHandler authorizationHandler;

    @Autowired
    private final TokenHandler tokenHandler;


    /**
     * 授权完毕跳转
     */
    @Operation(summary = "授权完毕跳转")
    @RequestMapping("/idaasoauth/render")
    public void render(HttpServletResponse response, HttpServletRequest request) throws IOException {
        String redirect = "";

        AuthConfig authConfig = new AuthConfig();
        authConfig.setClientId(this.locClient);
        authConfig.setClientSecret(this.locSecret);
//        if (request.getHeaders("remote-user") != null) {
//            redirect = this.picRedirect;
//        } else {
//            redirect = this.locRedirect;
//        }
        redirect = this.locRedirect;
        authConfig.setRedirectUri(redirect);
        authConfig.setIgnoreCheckState(true);
        AuthRequest authRequest = new IdaasRequest(authConfig, SocialUtil.getAuthStateCache(), this.locIDaaSServer);
        String authorizeUrl = authRequest.authorize(AuthStateUtils.createState());
        response.sendRedirect(authorizeUrl);
    }

    /**
     * 获取认证信息
     */
    @Operation(summary = "获取认证信息")
    @RequestMapping("/idaasoauth/callback")
    public void callback(AuthCallback callback, HttpServletResponse response, HttpServletRequest request) throws IOException {
        AuthConfig authConfig = new AuthConfig();
        authConfig.setClientId(this.locClient);
        authConfig.setClientSecret(this.locSecret);
        String redirect = "";
        String login = "";
//        if (request.getHeaders("remote-user") != null) {
//            redirect = this.picRedirect;
//            login = this.picLogin;
//        } else {
//            redirect = this.locRedirect;
//            login = this.locLogin;
//        }
        redirect = this.locRedirect;
        login = this.locLogin;

        authConfig.setRedirectUri(redirect);
        authConfig.setIgnoreCheckState(true);
        AuthRequest authRequest = new IdaasRequest(authConfig, SocialUtil.getAuthStateCache(), this.locIDaaSServer);
        AuthResponse authResponse = authRequest.login(callback);
        AuthUser authUser = (AuthUser) authResponse.getData();

//        if (authResponse.getCode() != 200) {
//            throw new RuntimeException(authResponse.getMsg());
//        }

        // 判断该用户是否存在
        User user = new User();
        user.setId(Long.valueOf(authUser.getUuid()));
        QueryWrapper<User> queryWrapper = Condition.getQueryWrapper(user);
        // 不存在，创建该用户
        if (!userService.exists(queryWrapper)) {
            // 默认角色
            // user.setId(Long.valueOf(authUser.getUuid())); // 考虑可能存在重名用户，使用uuid记录用户唯一id
            user.setAccount(authUser.getUuid()); // 考虑可能存在重名用户，使用uuid记录用户唯一id
            user.setPhone(authUser.getRemark());
            user.setTenantId(DEFAULT_TENANT_ID);
            user.setName(authUser.getUsername());
            user.setRealName(authUser.getNickname());
            user.setUserType(1); //web
            user.setPassword("10470c3b4b1fed12c3baac014be15fac67c6e815"); // 默认密码123456 TODO DigestUtil.md5Hex(authUser.getUuid())
            user.setRoleId("1902907330864332802"); // 默认角色
            user.setDeptId("1123598813738675201"); // 默认部门
            user.setPostId("1123598817738675207"); // 默认职位
            user.setCreateDept(1123598813738675201L); // 默认admin
            user.setCreateUser(1123598821738675201L); // 默认admin
            user.setCreateTime(new Date());
            userService.save(user);
        }

        // 将idaas验证通过用户id保存到redis. 2小时过期
        stringRedisTemplate.opsForValue().set("authInfo:" + user.getId(), user.getId() + "", 2, TimeUnit.HOURS);
        response.sendRedirect(login + "?uid=" + user.getId());
    }

    @ApiLog("本地用户登录验证")
    @PostMapping("/idaasoauth/token")
    @Operation(summary = "本地用户登录验证", description = "传入用户UID:uid")
    public Kv token(@Parameter(description = "登录用户UID", required = true) @RequestParam String uid) {
        Kv authInfo = Kv.create();
        String key = "authInfo:" + uid;
        String value = stringRedisTemplate.opsForValue().get(key);
        if (value == null) {
            return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST).set("error_description", "用户登录已经失效，请重新登录");
        }

        OAuth2Request request = OAuth2Request.create().buildArgs();
        request.setUserId(value);
        TokenGranter tokenGranter = granterFactory.create("IDass");

        OAuth2User user;
        user = tokenGranter.user(request);

        OAuth2Token token = tokenGranter.token(user, request);
        OAuth2Token enhanceToken = this.tokenHandler.enhance(user, token, request);
        this.authorizationHandler.authSuccessful(user, request);

        if (user != null) {
            userService.updateLastLoginTime(Long.valueOf(user.getUserId()), LocalDateTime.now());
        }

        return enhanceToken.getArgs();
    }


    /**
     * 撤销授权
     */
    @Operation(summary = "撤销授权")
    @RequestMapping("/idaasoauth/revoke/{token}")
    public R revoke(@PathVariable("token") String token, HttpServletRequest request) throws UnsupportedEncodingException {
        AuthConfig authConfig = new AuthConfig();
        authConfig.setClientId(this.locClient);
        authConfig.setClientSecret(this.locSecret);
        String redirect = "";
//        if (request.getHeaders("remote-user") != null) {
//            redirect = this.picRedirect;
//        } else {
//            redirect = this.locRedirect;
//        }
        redirect = this.locRedirect;
        authConfig.setRedirectUri(redirect);
        AuthRequest authRequest = new IdaasRequest(authConfig, SocialUtil.getAuthStateCache(), this.locIDaaSServer);
        return R.data(authRequest.revoke(AuthToken.builder().accessToken(token).build()));

    }
}
