package org.springblade.modules.auth.authsource;

import me.zhyd.oauth.config.AuthSource;
import me.zhyd.oauth.request.AuthDefaultRequest;
import org.springblade.modules.auth.request.IdaasRequest;

public enum IdaasAuthSource implements AuthSource {
	IDAAS {
		@Override
		public String authorize() {
			return "{IDaaS_server}/oauth/authorize";
		}

		@Override
		public String accessToken() {
			return "{IDaaS_server}/oauth/token";
		}

		@Override
		public String userInfo() {
			return "{IDaaS_server}/api/bff/v1.2/oauth2/userinfo";
		}

		@Override
		public Class<? extends AuthDefaultRequest> getTargetClass() {
			return IdaasRequest.class;
		}

		public String idaasServer;
	}
}
