package org.springblade.modules.dingding.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "科目概览")
public class SubjectSummaryVO {
	@Schema(description = "科目ID")
    private Long id;
	@Schema(description = "科目名称")
    private String name;
    @Schema(description = "总题数（去重）")
    private Integer total;
    @Schema(description = "已完成题数（去重）")
    private Integer done;
    @Schema(description = "正确率（0-100）")
    private Double accuracy;
    @Schema(description = "已练习次数")
    private Integer practiced;
}

