package org.springblade.modules.dingding.service;

import lombok.RequiredArgsConstructor;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.stereotype.Service;
import org.springblade.modules.dingding.pojo.vo.UserMeVO;
import org.springblade.modules.dingding.pojo.vo.ReportOverviewVO;

/**
 * H5 用户门面服务（封装用户信息聚合逻辑）
 */
@Service
@RequiredArgsConstructor
public class DingUserFacade {

    private final DingMiscService miscService;

    public UserMeVO getCurrentUserInfo() {
        BladeUser user = AuthUtil.getUser();
        Long userId = user == null ? null : user.getUserId();

        UserMeVO vo = new UserMeVO();
        vo.setId(userId);
        vo.setName(user == null ? null : user.getNickName());

        // 复用学习报告统计逻辑，确保与报告页面一致
        ReportOverviewVO report = miscService.reportOverview(userId);
        UserMeVO.Stats stats = new UserMeVO.Stats();
        stats.setPracticeCount(report.getPracticeCount());
        stats.setExamCount(report.getExamCount());
        stats.setAccuracy(report.getAccuracy());
        vo.setStats(stats);

        // 学习等级（方案：XP分段制）
        int practiceCount = report.getPracticeCount() == null ? 0 : report.getPracticeCount();
        int examCount = report.getExamCount() == null ? 0 : report.getExamCount();
        double accuracy = report.getAccuracy() == null ? 0.0 : report.getAccuracy();
        long xp = 5L * practiceCount + 15L * examCount + Math.round(accuracy);
        int[] thresholds = new int[]{50, 100, 160, 230, 310, 400, 500, 620, 760};
        int level = 1;
        for (int t : thresholds) {
            if (xp >= t) level++; else break;
        }
        vo.setLevel("LV." + level);
        return vo;
    }
}

