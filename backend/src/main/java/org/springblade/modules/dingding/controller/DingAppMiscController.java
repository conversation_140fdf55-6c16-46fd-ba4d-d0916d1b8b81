package org.springblade.modules.dingding.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.modules.dingding.pojo.vo.HistoryItemVO;
import org.springblade.modules.dingding.pojo.vo.ReportOverviewVO;
import org.springblade.modules.dingding.pojo.vo.WrongItemVO;
import org.springblade.modules.dingding.service.DingMiscService;
import org.springblade.modules.feedback.pojo.entity.FeedbackEntity;
import org.springblade.modules.feedback.service.IFeedbackService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dingapp")
@Tag(name = "H5 杂项", description = "钉钉H5杂项接口：历史/错题/报告/反馈")
public class DingAppMiscController extends BladeController {

    private final IFeedbackService feedbackService;
    private final DingMiscService miscService;

    @GetMapping("/history")
    @Operation(summary = "历史记录")
    public R<List<HistoryItemVO>> history(@RequestParam(required = false) String type,
                                          @RequestParam(required = false, defaultValue = "1") Integer page,
                                          @RequestParam(required = false, defaultValue = "20") Integer size) {
		Long userId = AuthUtil.getUserId();
        return R.data(miscService.listHistory(userId, type, page, size));
    }

    @GetMapping("/wrong")
    @Operation(summary = "错题本")
    public R<List<WrongItemVO>> wrong(@RequestParam Long subjectId,
                                      @RequestParam(required = false, defaultValue = "1") Integer page,
                                      @RequestParam(required = false, defaultValue = "20") Integer size) {
		Long userId = AuthUtil.getUserId();
        return R.data(miscService.listWrong(userId, subjectId, page, size));
    }

    @GetMapping("/report/overview")
    @Operation(summary = "学习报告概览")
    public R<ReportOverviewVO> reportOverview() {
		Long userId = AuthUtil.getUserId();
        return R.data(miscService.reportOverview(userId));
    }

    @PostMapping("/feedback")
    @Operation(summary = "提交意见反馈")
    public R<Boolean> feedback(@RequestBody FeedbackEntity entity) {
		entity.setUserId(AuthUtil.getUserId());
        boolean ok = feedbackService.save(entity);
        return R.status(ok);
    }
}

