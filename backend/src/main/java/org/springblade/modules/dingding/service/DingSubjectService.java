package org.springblade.modules.dingding.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springblade.modules.answer.pojo.entity.AnswerEntity;
import org.springblade.modules.answer.service.IAnswerService;
import org.springblade.modules.practice.pojo.entity.PracticeAnswerEntity;
import org.springblade.modules.practice.service.IPracticeAnswerService;
import org.springblade.modules.questions.service.IQuestionChapterService;
import org.springblade.modules.subjects.service.ISubjectChapterService;
import org.springframework.stereotype.Service;
import org.springblade.modules.subjects.pojo.entity.SubjectChapterEntity;
import org.springblade.modules.dingding.pojo.vo.SubjectChapterVO;
import org.springblade.modules.dingding.pojo.vo.SubjectProgressVO;
import org.springblade.modules.dingding.pojo.vo.SubjectSummaryVO;
import org.springblade.modules.questions.pojo.entity.QuestionsEntity;
import org.springblade.modules.questions.service.IQuestionsService;
import org.springblade.modules.subjects.pojo.entity.SubjectsEntity;
import org.springblade.modules.subjects.service.ISubjectsService;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DingSubjectService {

    private final ISubjectsService subjectsService;
    private final IQuestionsService questionsService;
	private final ISubjectChapterService subjectChapterService;
	private final IQuestionChapterService questionChapterService;
	private final IPracticeAnswerService practiceAnswerService;
	private final IAnswerService answerService;

    /**
     * 科目进度列表：按题-章映射对科目维度去重统计总题数；done/progress 真实统计
     */
    public List<SubjectProgressVO> listSubjectProgress(Long userId) {
        List<SubjectsEntity> subjects = subjectsService.list(new QueryWrapper<SubjectsEntity>().eq("is_deleted", 0));
        // 汇总用户已完成题目（练习+考试）按科目去重
        Map<Long, Set<Long>> doneBySubject = buildDoneQuestionIdsBySubject(userId);
        return subjects.stream().map(s -> {
            SubjectProgressVO vo = new SubjectProgressVO();
            vo.setId(s.getId());
            vo.setName(s.getName());
            int total = questionChapterService.selectDistinctQuestionIdsBySubject(s.getId()).size();
            int done = doneBySubject.getOrDefault(s.getId(), Collections.emptySet()).size();
            vo.setTotal(total);
            vo.setDone(done);
            vo.setProgress(total == 0 ? 0 : (int)Math.round(done * 100.0 / total));
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 科目概览：total（题-章去重），done/accuracy/practiced 来源于练习+考试答案聚合
     */
    public SubjectSummaryVO getSubjectSummary(Long userId, Long subjectId) {
		if (subjectId == null) {
			return null;
		}

		SubjectsEntity serviceById = subjectsService.getById(subjectId);
		if (serviceById == null) {
			return null;
		}

		SubjectSummaryVO vo = new SubjectSummaryVO();
		vo.setId(subjectId);
		vo.setName(serviceById.getName());
		// 总题数
        int total = questionChapterService.selectDistinctQuestionIdsBySubject(subjectId).size();
        // 用户已作答集合与正确集合
        Set<Long> doneSet = getDoneQuestionIdsBySubject(userId, subjectId);
        Set<Long> correctSet = getCorrectQuestionIdsBySubject(userId, subjectId);
        vo.setTotal(total);
        vo.setDone(doneSet.size());
        double accuracy = doneSet.isEmpty() ? 0.0 : (correctSet.size() * 100.0 / doneSet.size());
        vo.setAccuracy(accuracy);
        vo.setPracticed(doneSet.size()); // 简化为已作答数
        return vo;
    }

    /**
     * 章节列表（含进度）：按 order_no 升序，done/progress 基于用户作答去重
     */
    public List<SubjectChapterVO> listChapters(Long userId, Long subjectId) {
        List<SubjectChapterEntity> chapters = subjectChapterService.list(
            new QueryWrapper<SubjectChapterEntity>()
                .eq("is_deleted", 0)
                .eq("subject_id", subjectId)
                .orderByAsc("order_no")
        );
        // 用户该科目的已作答集合
        Set<Long> doneSet = getDoneQuestionIdsBySubject(userId, subjectId);
        return chapters.stream().map(c -> {
            SubjectChapterVO vo = new SubjectChapterVO();
            vo.setId(c.getId());
            vo.setName(c.getName());
            vo.setOrderNo(c.getOrderNo());
            int total = Optional.ofNullable(c.getTotalQuestions()).orElse(0);
            // 该章题目集合
            List<Long> chapterQ = questionChapterService.selectQuestionIdsByChapter(subjectId, c.getId());
            int done = (int) chapterQ.stream().filter(doneSet::contains).distinct().count();
            vo.setTotal(total);
            vo.setDone(done);
            vo.setProgress(total == 0 ? 0 : (int)Math.round(done * 100.0 / total));
            vo.setStatus(done == 0 ? "未开始" : (done >= total ? "已完成" : "进行中"));
            return vo;
        }).collect(Collectors.toList());
    }

    // 构建用户在所有科目下的已作答题目集合（练习+考试，按科目聚合，题目去重）
    private Map<Long, Set<Long>> buildDoneQuestionIdsBySubject(Long userId) {
        Map<Long, Set<Long>> map = new HashMap<>();
        // 练习答案按 subject_id 统计
        practiceAnswerService.list(new QueryWrapper<PracticeAnswerEntity>()
            .eq("is_deleted", 0)
            .eq("user_id", userId)).forEach(a -> {
            map.computeIfAbsent(a.getSubjectId(), k -> new HashSet<>()).add(a.getQuestionId());
        });
        // 考试答案：根据题目所属科目补充
        List<AnswerEntity> examAnswers = answerService.list(
            new QueryWrapper<AnswerEntity>()
                .eq("is_deleted", 0)
                .eq("user_id", userId)
        );
        if (!examAnswers.isEmpty()) {
            Set<Long> qids = examAnswers.stream().map(AnswerEntity::getQuestionId).collect(Collectors.toSet());
            if (!qids.isEmpty()) {
                List<QuestionsEntity> questions = questionsService.list(new QueryWrapper<QuestionsEntity>().in("id", qids).eq("is_deleted", 0));
                Map<Long, Long> q2subj = questions.stream().collect(Collectors.toMap(QuestionsEntity::getId, QuestionsEntity::getSubjectId));
                for (AnswerEntity a : examAnswers) {
                    Long subj = q2subj.get(a.getQuestionId());
                    if (subj != null) {
                        map.computeIfAbsent(subj, k -> new HashSet<>()).add(a.getQuestionId());
                    }
                }
            }
        }
        return map;
    }

    private Set<Long> getDoneQuestionIdsBySubject(Long userId, Long subjectId) {
        Map<Long, Set<Long>> m = buildDoneQuestionIdsBySubject(userId);
        return m.getOrDefault(subjectId, Collections.emptySet());
    }

    private Set<Long> getCorrectQuestionIdsBySubject(Long userId, Long subjectId) {
        Set<Long> result = new HashSet<>();
        // 练习正确
        practiceAnswerService.list(new QueryWrapper<PracticeAnswerEntity>()
            .eq("is_deleted", 0)
            .eq("user_id", userId)
            .eq("subject_id", subjectId)
            .eq("is_correct", 1)).forEach(a -> result.add(a.getQuestionId()));
        // 考试正确（按“全对才给分”的规则，我们已在 submit 阶段按题的集合比较；这里仍需逐题计算）
        List<AnswerEntity> examAnswers = answerService.list(new QueryWrapper<AnswerEntity>()
            .eq("is_deleted", 0)
            .eq("user_id", userId));
        if (!examAnswers.isEmpty()) {
            Set<Long> qids = examAnswers.stream().map(AnswerEntity::getQuestionId).collect(Collectors.toSet());
            if (!qids.isEmpty()) {
                List<QuestionsEntity> questions = questionsService.list(new QueryWrapper<QuestionsEntity>().in("id", qids).eq("is_deleted", 0).eq("subject_id", subjectId));
                Map<Long, QuestionsEntity> qm = questions.stream().collect(Collectors.toMap(QuestionsEntity::getId, q -> q));
                for (AnswerEntity a : examAnswers) {
                    QuestionsEntity q = qm.get(a.getQuestionId());
                    if (q == null) continue;
                    Set<String> expect = splitAnswer(q.getAnswer());
                    Set<String> got = readSet(a.getValuesJson());
                    if (expect.equals(got)) result.add(a.getQuestionId());
                }
            }
        }
        return result;
    }

    private Set<String> readSet(String json) {
        if (json == null || json.isEmpty()) return Collections.emptySet();
        try { java.util.List<String> arr = new com.fasterxml.jackson.databind.ObjectMapper().readValue(json, new com.fasterxml.jackson.core.type.TypeReference<java.util.List<String>>(){}); return new HashSet<>(arr);} catch (Exception e) { return Collections.emptySet(); }
    }

    private Set<String> splitAnswer(String answer) {
        if (answer == null) return Collections.emptySet();
        String a = answer.trim();
        if (a.startsWith("[") && a.endsWith("]")) {
            try { java.util.List<String> arr = new com.fasterxml.jackson.databind.ObjectMapper().readValue(a, new com.fasterxml.jackson.core.type.TypeReference<java.util.List<String>>(){}); return new HashSet<>(arr);} catch (Exception ignored) {}
        }
        if (a.contains(",")) {
            return java.util.Arrays.stream(a.split(",")).map(String::trim).filter(s -> !s.isEmpty()).collect(java.util.stream.Collectors.toSet());
        }
        return a.isEmpty() ? Collections.emptySet() : java.util.Set.of(a);
    }
}

