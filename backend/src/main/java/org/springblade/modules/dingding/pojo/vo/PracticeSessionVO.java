package org.springblade.modules.dingding.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "练习会话返回")
public class PracticeSessionVO {
    @Schema(description = "会话ID")
    private Long sessionId;
    @Schema(description = "科目ID")
    private Long subjectId;
    @Schema(description = "模式")
    private String mode;
    @Schema(description = "总题量")
    private Integer total;
}

