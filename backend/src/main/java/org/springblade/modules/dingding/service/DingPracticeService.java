package org.springblade.modules.dingding.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springblade.modules.practice.service.IPracticeAnswerService;
import org.springblade.modules.practice.service.IPracticeSessionService;
import org.springblade.modules.questions.service.IQuestionChapterService;
import org.springblade.modules.subjects.service.ISubjectChapterService;
import org.springframework.stereotype.Service;
import org.springblade.modules.dingding.pojo.dto.CreatePracticeSessionDTO;
import org.springblade.modules.dingding.pojo.dto.SavePracticeAnswerDTO;
import org.springblade.modules.practice.pojo.entity.PracticeAnswerEntity;
import org.springblade.modules.practice.pojo.entity.PracticeSessionEntity;
import org.springblade.modules.dingding.pojo.vo.PracticeSessionVO;
import org.springblade.modules.dingding.pojo.vo.PracticeResultVO;
import org.springblade.modules.dingding.pojo.vo.QuestionPageVO;
import org.springblade.modules.dingding.pojo.vo.QuestionVO;
import org.springblade.modules.questions.pojo.entity.QuestionsEntity;
import org.springblade.modules.questions.service.IQuestionsService;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DingPracticeService {

    private final IQuestionsService questionsService;
	private final IQuestionChapterService questionChapterService;
	private final IPracticeSessionService practiceSessionService;
	private final IPracticeAnswerService practiceAnswerService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    public PracticeSessionVO createSession(Long userId, CreatePracticeSessionDTO dto) {
        // 取题集合
        List<Long> questionIds;
        if ("sequence".equalsIgnoreCase(dto.getMode())) {
            if (dto.getChapterId() != null) {
                questionIds = questionChapterService.selectQuestionIdsByChapter(dto.getSubjectId(), dto.getChapterId());
            } else {
                questionIds = questionChapterService.selectQuestionIdsBySubjectOrderByChapterOrder(dto.getSubjectId());
            }
        } else if ("random".equalsIgnoreCase(dto.getMode())) {
            questionIds = questionChapterService.selectDistinctQuestionIdsBySubject(dto.getSubjectId());
            Collections.shuffle(questionIds);
        } else if ("wrong".equalsIgnoreCase(dto.getMode())) {
            // 错题模式：从练习答案中取 is_correct=0 的题目；若暂无则直接提示
            List<PracticeAnswerEntity> wrongList = practiceAnswerService.selectWrongQuestionIdsBySubject(userId, dto.getSubjectId());
            if (wrongList == null || wrongList.isEmpty()) {
                throw new IllegalArgumentException("当前科目暂无错题，去练习或考试后再来试试");
            }
            questionIds = wrongList.stream().map(PracticeAnswerEntity::getQuestionId).collect(Collectors.toList());
        } else {
            throw new IllegalArgumentException("非法练习模式");
        }
        int total = questionIds == null ? 0 : questionIds.size();

        // 持久化会话
        PracticeSessionEntity entity = new PracticeSessionEntity();
        entity.setUserId(userId);
        entity.setSubjectId(dto.getSubjectId());
        entity.setPaperId(null);
        entity.setMode(dto.getMode());
        entity.setQuestionTotal(total);
        entity.setCurrentIndex(0);
        try { entity.setQuestionIds(objectMapper.writeValueAsString(questionIds)); } catch (Exception ignored) {}
        entity.setStartTime(new java.util.Date());
        entity.setEndTime(null);
        entity.setStatus(0);
        practiceSessionService.save(entity);

        PracticeSessionVO vo = new PracticeSessionVO();
        vo.setSessionId(entity.getId());
        vo.setSubjectId(dto.getSubjectId());
        vo.setMode(dto.getMode());
        vo.setTotal(total);
        return vo;
    }

    public QuestionPageVO getQuestionsPage(Long sessionId, Integer cursor, Integer limit) {
        PracticeSessionEntity session = practiceSessionService.getById(sessionId);
        if (session == null || session.getQuestionIds() == null) {
            return new QuestionPageVO();
        }
        List<Long> ids;
        try {
            ids = objectMapper.readValue(session.getQuestionIds(), new TypeReference<List<Long>>(){});
        } catch (Exception e) {
            ids = Collections.emptyList();
        }
        int from = Math.max(0, cursor == null ? 0 : cursor);
        int to = Math.min(ids.size(), from + (limit == null ? 10 : limit));
        List<Long> pageIds = ids.subList(from, to);

        if (pageIds.isEmpty()) {
            QuestionPageVO vo = new QuestionPageVO();
            vo.setList(Collections.emptyList());
            vo.setNextCursor(null);
            return vo;
        }

        List<QuestionsEntity> questions = questionsService.list(Wrappers.<QuestionsEntity>lambdaQuery()
            .eq(QuestionsEntity::getIsDeleted, 0)
            .in(QuestionsEntity::getId, pageIds));
        // 保持与 pageIds 顺序一致
        Map<Long, QuestionsEntity> map = questions.stream().collect(Collectors.toMap(QuestionsEntity::getId, q -> q));
        List<QuestionVO> list = pageIds.stream()
            .map(map::get)
            .filter(Objects::nonNull)
            .map(this::mapQuestion)
            .collect(Collectors.toList());

        QuestionPageVO vo = new QuestionPageVO();
        vo.setList(list);
        vo.setNextCursor(to < ids.size() ? to : null);
        return vo;
    }

    public List<PracticeAnswerEntity> saveAnswers(Long userId, Long sessionId, List<SavePracticeAnswerDTO> answers) {
        if (answers == null || answers.isEmpty()) return null;
		PracticeSessionEntity session = practiceSessionService.getById(sessionId);
		if (session == null) return null;
        Date now = new Date();
		List<PracticeAnswerEntity> list = new ArrayList<>();
        for (SavePracticeAnswerDTO a : answers) {
            PracticeAnswerEntity e = new PracticeAnswerEntity();
            e.setSessionId(sessionId);
			e.setSubjectId(session.getSubjectId());
            e.setUserId(userId);
            e.setQuestionId(a.getQuestionId());
            e.setValuesJson(writeJson(a.getValues()));
            e.setDurationSec(a.getDurationSec());
            e.setSubmittedAt(a.getSubmittedAt() != null ? a.getSubmittedAt() : now);
            // 判分（若题目有答案）：完全匹配则正确
            QuestionsEntity q = questionsService.getById(a.getQuestionId());
            if (q != null && q.getAnswer() != null) {
                Set<String> expect = splitAnswer(q.getAnswer());
                Set<String> got = new HashSet<>(a.getValues() == null ? Collections.emptyList() : a.getValues());
                e.setIsCorrect(expect.equals(got) ? 1 : 0);
            }
            // 简单 upsert：先查存在则 update，否则 insert（无唯一索引场景）
            PracticeAnswerEntity exists = practiceAnswerService.getOne(new QueryWrapper<PracticeAnswerEntity>()
                .eq("is_deleted", 0)
                .eq("session_id", sessionId)
                .eq("question_id", a.getQuestionId()));
            if (exists == null) {
                practiceAnswerService.save(e);
            } else {
                e.setId(exists.getId());
                practiceAnswerService.updateById(e);
            }
			list.add(e);
        }
		return list;
    }

    public PracticeResultVO getPracticeResult(Long sessionId) {
        PracticeSessionEntity session = practiceSessionService.getById(sessionId);
        if (session == null) return null;

        // 获取该会话的所有答案记录
        List<PracticeAnswerEntity> answers = practiceAnswerService.list(new QueryWrapper<PracticeAnswerEntity>()
            .eq("is_deleted", 0)
            .eq("session_id", sessionId));

        // 统计正确和错误题数
        int correct = 0;
        int wrong = 0;
        for (PracticeAnswerEntity answer : answers) {
            if (answer.getIsCorrect() != null) {
                if (answer.getIsCorrect() == 1) {
                    correct++;
                } else {
                    wrong++;
                }
            }
        }

        // 计算正确率
        int total = session.getQuestionTotal() != null ? session.getQuestionTotal() : 0;
        double accuracy = total == 0 ? 0.0 : (correct * 100.0 / total);

        // 计算用时
        Integer durationSec = null;
        if (session.getStartTime() != null && session.getEndTime() != null) {
            durationSec = (int) ((session.getEndTime().getTime() - session.getStartTime().getTime()) / 1000);
        }

        // 构建结果VO
        PracticeResultVO vo = new PracticeResultVO();
        vo.setSessionId(sessionId);
        vo.setSubjectId(session.getSubjectId());
        vo.setTotal(total);
        vo.setCorrect(correct);
        vo.setWrong(wrong);
        vo.setAccuracy(accuracy);
        vo.setDurationSec(durationSec);

        return vo;
    }

    public void finishPracticeSession(Long sessionId) {
        PracticeSessionEntity session = practiceSessionService.getById(sessionId);
        if (session != null && session.getEndTime() == null) {
            session.setEndTime(new Date());
            session.setStatus(1); // 标记为已完成
            practiceSessionService.updateById(session);
        }
    }

    // 工具：将 QuestionsEntity 映射为 QuestionVO（忽略解析）
    private QuestionVO mapQuestion(QuestionsEntity q) {
        QuestionVO vo = new QuestionVO();
        vo.setId(q.getId());
        vo.setType(q.getType());
        vo.setStem(q.getContent());
        List<QuestionVO.Option> opts = Collections.emptyList();
        try {
            if (q.getOptions() != null && !q.getOptions().isEmpty()) {
                Map<String, String> map = objectMapper.readValue(q.getOptions(), new TypeReference<Map<String, String>>(){});
                opts = map.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(e -> {
                        QuestionVO.Option o = new QuestionVO.Option();
                        o.setValue(e.getKey());   // value 使用选项字母，如 A/B/C
                        o.setLabel(e.getKey());   // label 显示字母
                        o.setText(e.getValue());  // text 显示正文
                        return o;
                    })
                    .collect(Collectors.toList());
            }
        } catch (Exception ignored) {}
        vo.setOptions(opts);
        // 填充正确答案
        try {
            Set<String> expect = splitAnswer(q.getAnswer());
            if (expect != null && !expect.isEmpty()) {
                List<String> sorted = new ArrayList<>(expect);
                Collections.sort(sorted);
                vo.setCorrectAnswers(sorted);
            }
        } catch (Exception ignored) {}
        return vo;
    }

    private String writeJson(Object obj) {
        try { return objectMapper.writeValueAsString(obj); } catch (Exception e) { return null; }
    }

    // 支持 “A,B,C” 或 JSON 数组格式（后续可按你的实际数据格式调整）
    private Set<String> splitAnswer(String answer) {
        if (answer == null) return Collections.emptySet();
        String a = answer.trim();
        if (a.startsWith("[") && a.endsWith("]")) {
            try { List<String> arr = objectMapper.readValue(a, new TypeReference<List<String>>(){}); return new HashSet<>(arr);} catch (Exception ignored) {}
        }
        if (a.contains(",")) {
            return Arrays.stream(a.split(",")).map(String::trim).filter(s -> !s.isEmpty()).collect(Collectors.toSet());
        }
        return a.isEmpty() ? Collections.emptySet() : Set.of(a);
    }
}

