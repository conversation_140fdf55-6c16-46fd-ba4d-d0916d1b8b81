package org.springblade.modules.dingding.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "创建练习会话入参")
public class CreatePracticeSessionDTO {
    @Schema(description = "科目ID")
    private Long subjectId;

    @Schema(description = "练习模式：sequence|random|wrong")
    private String mode;

    @Schema(description = "章节ID（可选，仅 sequence 下生效）")
    private Long chapterId;

    @Schema(description = "每页题量（可选，默认10）")
    private Integer pageSize;
}

