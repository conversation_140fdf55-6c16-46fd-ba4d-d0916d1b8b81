package org.springblade.modules.dingding.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "章节信息与进度")
public class SubjectChapterVO {
    @Schema(description = "章节ID")
    private Long id;
    @Schema(description = "章节名称")
    private String name;
    @Schema(description = "排序号")
    private Integer orderNo;
    @Schema(description = "总题数")
    private Integer total;
    @Schema(description = "已完成题数")
    private Integer done;
    @Schema(description = "进度百分比")
    private Integer progress;
    @Schema(description = "状态：未开始/进行中/已完成")
    private String status;
}

