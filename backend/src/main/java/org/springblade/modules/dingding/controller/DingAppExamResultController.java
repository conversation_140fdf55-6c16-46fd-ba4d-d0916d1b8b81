package org.springblade.modules.dingding.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.modules.dingding.pojo.vo.ResultDetailVO;
import org.springblade.modules.dingding.pojo.vo.ResultVO;
import org.springblade.modules.dingding.service.DingExamService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dingapp/exam")
@Tag(name = "H5 考试结果", description = "查询考试成绩")
public class DingAppExamResultController extends BladeController {

    private final DingExamService examService;

    @GetMapping("/sessions/{sessionId}/result")
    @Operation(summary = "获取考试结果")
    public R<ResultVO> getResult(@PathVariable Long sessionId) {
        return R.data(examService.getExamResult(sessionId));
    }

    @GetMapping("/sessions/{sessionId}/detail")
    @Operation(summary = "获取考试结果详情")
    public R<ResultDetailVO> getDetail(@PathVariable Long sessionId) {
        return R.data(examService.getExamDetail(sessionId));
    }
}

