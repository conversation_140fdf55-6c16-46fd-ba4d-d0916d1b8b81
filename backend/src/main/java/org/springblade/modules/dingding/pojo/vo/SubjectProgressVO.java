package org.springblade.modules.dingding.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "科目进度项")
public class SubjectProgressVO {
    @Schema(description = "科目ID")
    private Long id;
    @Schema(description = "科目名称")
    private String name;
    @Schema(description = "总题数（去重）")
    private Integer total;
    @Schema(description = "已完成题数（去重）")
    private Integer done;
    @Schema(description = "进度百分比")
    private Integer progress;
}

