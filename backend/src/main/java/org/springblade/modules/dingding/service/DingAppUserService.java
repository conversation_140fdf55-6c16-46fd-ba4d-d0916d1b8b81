package org.springblade.modules.dingding.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.StaticLog;
import com.aliyun.dingtalkoauth2_1_0.Client;
import com.aliyun.dingtalkoauth2_1_0.models.CreateJsapiTicketResponse;
import com.aliyun.dingtalkoauth2_1_0.models.GetTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetTokenResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.request.OapiV2UserGetuserinfoRequest;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.dingtalk.api.response.OapiV2UserGetuserinfoResponse;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.oauth2.granter.TokenGranter;
import org.springblade.core.oauth2.granter.TokenGranterFactory;
import org.springblade.core.oauth2.handler.AuthorizationHandler;
import org.springblade.core.oauth2.handler.TokenHandler;
import org.springblade.core.oauth2.provider.OAuth2Request;
import org.springblade.core.oauth2.provider.OAuth2Token;
import org.springblade.core.oauth2.service.OAuth2User;
import org.springblade.core.tool.support.Kv;
import org.springblade.modules.dingding.config.DingAppConfig;
import org.springblade.modules.dingding.pojo.vo.DingUserAccessTokenVO;
import org.springblade.modules.dingding.pojo.vo.DingUserInfoVO;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.IUserService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-22 10:18
 */
@Service
@AllArgsConstructor
public class DingAppUserService {

    private final DingAppConfig dingAppConfig;

    private static Client client;

    private final IUserService userService;

    private final TokenGranterFactory granterFactory;

    private final TokenHandler tokenHandler;

    private final AuthorizationHandler authorizationHandler;

    private final StringRedisTemplate stringRedisTemplate;

    public DingUserAccessTokenVO getAccessToken() throws Exception {

        if (client == null) {
            client = createClient();
        }

        // 获取缓存中的token
        String token = stringRedisTemplate.opsForValue().get("ding_access_token");
        if (StrUtil.isNotBlank(token)) {
            DingUserAccessTokenVO dingUserAccessTokenVO = new DingUserAccessTokenVO();
            dingUserAccessTokenVO.setAccessToken(token);
            return dingUserAccessTokenVO;
        }

        GetTokenRequest getTokenRequest = new GetTokenRequest();
        getTokenRequest.setClientId(dingAppConfig.getAppKey());
        getTokenRequest.setClientSecret(dingAppConfig.getAppSecret());
        getTokenRequest.setGrantType("client_credentials");

        try {
            GetTokenResponse tokenResponse = client.getToken(dingAppConfig.getCorpId(), getTokenRequest);
            if (tokenResponse.getStatusCode() == 200) {
                DingUserAccessTokenVO dingUserAccessTokenVO = new DingUserAccessTokenVO();
                dingUserAccessTokenVO.setAccessToken(tokenResponse.body.accessToken);
                dingUserAccessTokenVO.setExpiresIn(tokenResponse.body.expiresIn);

                // 获取token成功后，保存到redis
                stringRedisTemplate.opsForValue().set("ding_access_token", tokenResponse.body.accessToken, tokenResponse.body.expiresIn - 200, java.util.concurrent.TimeUnit.SECONDS);
				StaticLog.info("获取token成功， {}", tokenResponse.body.accessToken);
                return dingUserAccessTokenVO;
            }
            StaticLog.error("获取token失败， {}", tokenResponse.getStatusCode());
        } catch (Exception e) {
            StaticLog.error("获取token失败， {}", e.getMessage());
        }
        return null;
    }

    public DingUserInfoVO getUserInfo(String authCode, String accessToken) throws Exception {

        if (StrUtil.isBlank(authCode) || StrUtil.isBlank(accessToken)) {
            StaticLog.error("获取用户信息失败，authCode: {}, accessToken: {}", authCode, accessToken);
            return null;
        }



        // 获取userID
        DefaultDingTalkClient dingTalkClient = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/getuserinfo");
        OapiV2UserGetuserinfoRequest request = new OapiV2UserGetuserinfoRequest();
        request.setCode(authCode);
        OapiV2UserGetuserinfoResponse response = dingTalkClient.execute(request, accessToken);
        if (response.getErrcode() != 0) {
            StaticLog.error("获取用户ID失败， {}", response.getErrmsg());
            return null;
        }

        JSONObject bodyJson = JSONUtil.parseObj(response.getBody());
        JSONObject resultJson = bodyJson.getJSONObject("result");
        DingUserInfoVO dingUserInfoVO = new DingUserInfoVO();
        dingUserInfoVO.setAssociatedUnionId(resultJson.getStr("associated_unionid"));
        dingUserInfoVO.setUnionId(resultJson.getStr("unionid"));
        dingUserInfoVO.setDeviceId(resultJson.getStr("device_id"));
        dingUserInfoVO.setName(resultJson.getStr("name"));
        dingUserInfoVO.setSys(resultJson.getBool("sys"));
        dingUserInfoVO.setUserid(resultJson.getStr("userid"));

        // 读取redis中的用户信息
        String userInfo = stringRedisTemplate.opsForValue().get("ding_user_info:" + dingUserInfoVO.getUnionId());
        if (StrUtil.isNotBlank(userInfo)) {
            JSONObject jsonObject = JSONUtil.parseObj(userInfo);
            dingUserInfoVO.setAssociatedUnionId(jsonObject.getStr("associated_unionid"));
            dingUserInfoVO.setUnionId(jsonObject.getStr("unionid"));
            dingUserInfoVO.setDeviceId(jsonObject.getStr("device_id"));
            dingUserInfoVO.setName(jsonObject.getStr("name"));
            dingUserInfoVO.setSys(jsonObject.getBool("sys"));
            dingUserInfoVO.setUserid(jsonObject.getStr("userid"));
            dingUserInfoVO.setEmail(jsonObject.getStr("org_email"));
            dingUserInfoVO.setMobile(jsonObject.getStr("mobile"));
            return dingUserInfoVO;
        }

        // 获取用户信息
        dingTalkClient.resetServerUrl("https://oapi.dingtalk.com/topapi/v2/user/get");
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(resultJson.getStr("userid"));
        req.setLanguage("zh_CN");
        OapiV2UserGetResponse rsp = dingTalkClient.execute(req, accessToken);
        if (rsp.getErrcode() != 0) {
            StaticLog.error("获取用户信息失败， {}", rsp.getErrmsg());
            return null;
        }

        JSONObject resInfoBodyJson = JSONUtil.parseObj(rsp.getBody());
        JSONObject resInfoJson = resInfoBodyJson.getJSONObject("result");
        dingUserInfoVO.setEmail(resInfoJson.getStr("org_email"));
        dingUserInfoVO.setMobile(resInfoJson.getStr("mobile"));

        // 保存到redis
        stringRedisTemplate.opsForValue().set("ding_user_info:" + dingUserInfoVO.getUnionId(), JSONUtil.toJsonStr(dingUserInfoVO));

        return dingUserInfoVO;
    }

	public Kv loginByUid(String uid) {
		Kv authInfo = Kv.create();
		String key = "authInfo:" + uid;
		String value = stringRedisTemplate.opsForValue().get(key);
		if (value == null) {
			return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST).set("error_description", "用户登录已经失效，请重新登录");
		}

		OAuth2Request request = OAuth2Request.create().buildArgs();
		request.setUserId(value);
		TokenGranter tokenGranter = granterFactory.create("IDass");

		OAuth2User user;
		user = tokenGranter.user(request);

		OAuth2Token token = tokenGranter.token(user, request);
		OAuth2Token enhanceToken = this.tokenHandler.enhance(user, token, request);
		this.authorizationHandler.authSuccessful(user, request);

		if (user != null) {
			userService.updateLastLoginTime(Long.valueOf(user.getUserId()), LocalDateTime.now());
		}

		return enhanceToken.getArgs();
	}

    public Kv login(String authCode) {
        Kv authInfo = Kv.create();


        if (StrUtil.isBlank(authCode)) {
            return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST).set("error_description", "authCode不能为空");
        }

        DingUserAccessTokenVO dingUserAccessTokenVO = null;
        DingUserInfoVO dingUserInfoVO = null;
        try {
            dingUserAccessTokenVO = getAccessToken();
            if (dingUserAccessTokenVO == null) {
                return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST).set("error_description", "获取token失败");
            }

            dingUserInfoVO = getUserInfo(authCode, dingUserAccessTokenVO.getAccessToken());
            if (dingUserInfoVO == null) {
                return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST).set("error_description", "获取用户信息失败");
            }

        } catch (Exception e) {
            return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST).set("error_description", "创建钉钉客户端失败");
        }

        // 通过openId获取用户信息
        User user = null;
		if (StrUtil.isNotBlank(dingUserInfoVO.getMobile())) {
            // 单纯到openID找不到关联, 就看看手机号码行不行
            User userParam = new User();
            userParam.setPhone(dingUserInfoVO.getMobile());
            User phoneUser = userService.getOne(Condition.getQueryWrapper(userParam), false);
            if (phoneUser != null) {
                // 找到用户匹配信息, 模拟登陆
                user = phoneUser;
            }
        }

        if (user == null) {
            // 这个错误描述不要改, 小程序是通过这个relevancy_id和错误描述判断的
            return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST).set("error_description", "匹配不到对应账号")/*.set("relevancy_id", relevancyId)*/;
        }


        OAuth2Request request = OAuth2Request.create().buildArgs();
        request.setUserId(user.getId() + "");
        TokenGranter tokenGranter = granterFactory.create("IDass");

        OAuth2User oAuth2User;
        oAuth2User = tokenGranter.user(request);

        OAuth2Token token = tokenGranter.token(oAuth2User, request);
        OAuth2Token enhanceToken = this.tokenHandler.enhance(oAuth2User, token, request);
        this.authorizationHandler.authSuccessful(oAuth2User, request);

        if (oAuth2User != null) {
            userService.updateLastLoginTime(Long.valueOf(oAuth2User.getUserId()), LocalDateTime.now());
        }

        return enhanceToken.getArgs();
    }

    public String getJsapiTicket() throws Exception {
        if (client == null) {
            client = createClient();
        }
        // 获取缓存中的token
//        String ticket = stringRedisTemplate.opsForValue().get("ding_jsapi_ticket");
//        if (StrUtil.isNotBlank(ticket)) {
//            return  ticket;
//        }
        com.aliyun.dingtalkoauth2_1_0.models.CreateJsapiTicketHeaders createJsapiTicketHeaders = new com.aliyun.dingtalkoauth2_1_0.models.CreateJsapiTicketHeaders();
        DingUserAccessTokenVO token = getAccessToken();
        createJsapiTicketHeaders.xAcsDingtalkAccessToken = token.getAccessToken();
        try {
            CreateJsapiTicketResponse response=client.createJsapiTicketWithOptions(createJsapiTicketHeaders, new com.aliyun.teautil.models.RuntimeOptions());
            return response.getBody().getJsapiTicket();
        } catch (TeaException err) {
            err.printStackTrace();
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                StaticLog.error(err.getMessage());
            }

        } catch (Exception _err) {
            _err.printStackTrace();
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                StaticLog.error(err.getMessage());
            }

        }
        return null;
    }

    private Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        Client newClient = new Client(config);
        this.client = newClient;
        return newClient;
    }
}
