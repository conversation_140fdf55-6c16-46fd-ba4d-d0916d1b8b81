package org.springblade.modules.dingding.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "保存考试答案入参")
public class SaveExamAnswerDTO {
    @Schema(description = "题目ID")
    private Long questionId;
    @Schema(description = "作答选项数组")
    private List<String> values;
    @Schema(description = "该题耗时（秒）")
    private Integer durationSec;
    @Schema(description = "提交时间（可选）")
    private java.util.Date submittedAt;
}

