package org.springblade.modules.dingding.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "考试结果")
public class ResultVO {
    @Schema(description = "会话ID")
    private Long sessionId;
    @Schema(description = "试卷ID")
    private Long paperId;
    @Schema(description = "分数")
    private java.math.BigDecimal score;
    @Schema(description = "正确率（0-100）")
    private Double accuracy;
}

