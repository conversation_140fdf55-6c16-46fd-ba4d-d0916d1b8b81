package org.springblade.modules.dingding.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "历史记录项")
public class HistoryItemVO {
    @Schema(description = "记录ID")
    private Long id;
    @Schema(description = "类型 practice|exam")
    private String type;
    @Schema(description = "标题（科目/试卷名称）")
    private String title;
    @Schema(description = "开始时间")
    private Date startTime;
    @Schema(description = "结束时间")
    private Date endTime;
    @Schema(description = "题量")
    private Integer total;
    @Schema(description = "已作答")
    private Integer done;
    @Schema(description = "正确率（可选）")
    private Double accuracy;
    @Schema(description = "状态")
    private Integer status;
}

