package org.springblade.modules.dingding.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "练习结果")
public class PracticeResultVO {
    @Schema(description = "会话ID")
    private Long sessionId;
    @Schema(description = "科目ID")
    private Long subjectId;
    @Schema(description = "总题数")
    private Integer total;
    @Schema(description = "正确题数")
    private Integer correct;
    @Schema(description = "错误题数")
    private Integer wrong;
    @Schema(description = "正确率（0-100）")
    private Double accuracy;
    @Schema(description = "用时（秒）")
    private Integer durationSec;
}
