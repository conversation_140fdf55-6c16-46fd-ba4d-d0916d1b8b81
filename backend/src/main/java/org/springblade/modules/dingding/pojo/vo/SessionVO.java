package org.springblade.modules.dingding.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "考试会话返回")
public class SessionVO {
    @Schema(description = "会话ID")
    private Long sessionId;
    @Schema(description = "试卷ID")
    private Long paperId;
    @Schema(description = "总题量")
    private Integer total;
    @Schema(description = "结束时间（倒计时截止）")
    private java.util.Date endAt;
}

