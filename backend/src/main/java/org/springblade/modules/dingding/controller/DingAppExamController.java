package org.springblade.modules.dingding.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.modules.dingding.pojo.vo.ListItemVO;
import org.springblade.modules.papers.constant.PaperStatusConstant;
import org.springblade.modules.papers.mapper.PaperItemsMapper;
import org.springblade.modules.papers.pojo.entity.PaperItemsEntity;
import org.springblade.modules.papers.pojo.entity.PapersEntity;
import org.springblade.modules.papers.pojo.vo.PapersVO;
import org.springblade.modules.papers.service.IPapersService;
import org.springblade.modules.papers.wrapper.PapersWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * H5 考试（试卷）接口：仅提供 H5 所需数据视图
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/dingapp")
@Tag(name = "H5 考试", description = "钉钉H5考试接口（试卷）")
public class DingAppExamController extends BladeController {

    private final IPapersService papersService;
    private final PaperItemsMapper paperItemsMapper;

    /**
     * 获取可参加的试卷列表（status=1）
     */
    @GetMapping("/exams")
    @Operation(summary = "试卷列表（仅status=2）")
    public R<List<ListItemVO>> list() {
        QueryWrapper<PapersEntity> qw = new QueryWrapper<>();
        qw.eq("status", PaperStatusConstant.PUBLISHED).eq("is_deleted", 0);
        List<PapersEntity> list = papersService.list(qw);
        List<ListItemVO> vos = list.stream().map(p -> {
            ListItemVO vo = new ListItemVO();
            vo.setPaperId(p.getId());
            vo.setTitle(p.getName());
            vo.setAvailable(Boolean.TRUE);
            Long questionCount = paperItemsMapper.selectCount(new QueryWrapper<PaperItemsEntity>()
                .lambda()
                .eq(PaperItemsEntity::getPaperId, p.getId())
                .eq(PaperItemsEntity::getIsDeleted, 0));
            vo.setQuestionCount(questionCount == null ? 0 : questionCount.intValue());
            vo.setDuration(p.getDurationMinutes());
            vo.setDesc(p.getDescription());
            return vo;
        }).collect(Collectors.toList());
        return R.data(vos);
    }

    /**
     * 获取试卷详情（仅当 status=1）
     */
    @GetMapping("/exams/{paperId}")
    @Operation(summary = "试卷详情（仅status=2）")
    public R<PapersVO> detail(@PathVariable("paperId") Long paperId) {
        PapersEntity p = papersService.getById(paperId);
        if (p == null || p.getIsDeleted() == 1 || (p.getStatus() == null || !Objects.equals(p.getStatus(), PaperStatusConstant.PUBLISHED))) {
            return R.fail("试卷不可用或不存在");
        }

		PapersVO papersVO = PapersWrapper.build().entityVO(p);
		Long questionCount = paperItemsMapper.selectCount(new QueryWrapper<PaperItemsEntity>()
			.lambda()
			.eq(PaperItemsEntity::getPaperId, p.getId())
			.eq(PaperItemsEntity::getIsDeleted, 0));
		papersVO.setQuestionCount(questionCount == null ? 0 : questionCount.intValue());

		return R.data(papersVO);
    }
}

