package org.springblade.modules.dingding.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springblade.modules.practice.service.IPracticeAnswerService;
import org.springblade.modules.practice.service.IPracticeSessionService;
import org.springblade.modules.practice.service.ISessionService;
import org.springblade.modules.questions.pojo.entity.QuestionChapterEntity;
import org.springblade.modules.questions.service.IQuestionChapterService;
import org.springblade.modules.subjects.pojo.entity.SubjectChapterEntity;
import org.springblade.modules.subjects.service.ISubjectChapterService;
import org.springframework.stereotype.Service;
import org.springblade.modules.practice.pojo.entity.PracticeAnswerEntity;
import org.springblade.modules.practice.pojo.entity.PracticeSessionEntity;
import org.springblade.modules.practice.pojo.entity.SessionEntity;
import org.springblade.modules.dingding.pojo.vo.HistoryItemVO;
import org.springblade.modules.dingding.pojo.vo.ReportOverviewVO;
import org.springblade.modules.dingding.pojo.vo.WrongItemVO;
import org.springblade.modules.questions.pojo.entity.QuestionsEntity;
import org.springblade.modules.questions.service.IQuestionsService;
import org.springblade.modules.subjects.pojo.entity.SubjectsEntity;
import org.springblade.modules.subjects.service.ISubjectsService;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DingMiscService {

	private final IPracticeSessionService practiceSessionService;
	private final ISessionService sessionService;
	private final IPracticeAnswerService practiceAnswerService;
    private final IQuestionsService questionsService;
    private final ISubjectsService subjectsService;
	private final IQuestionChapterService questionChapterService;
	private final ISubjectChapterService subjectChapterService;

    public List<HistoryItemVO> listHistory(Long userId, String type, Integer page, Integer size) {
        List<HistoryItemVO> result = new ArrayList<>();
        if ("practice".equalsIgnoreCase(type) || type == null) {
            List<PracticeSessionEntity> ps = practiceSessionService.list(new QueryWrapper<PracticeSessionEntity>()
                .eq("is_deleted", 0).eq("user_id", userId));
            result.addAll(ps.stream().map(s -> {
                HistoryItemVO vo = new HistoryItemVO();
                vo.setId(s.getId());
                vo.setType("practice");
                vo.setTitle(subjectName(s.getSubjectId()));
                vo.setStartTime(s.getStartTime());
                vo.setEndTime(s.getEndTime());
                vo.setTotal(s.getQuestionTotal());
                // 已作答数
                Long done = practiceAnswerService.count(new QueryWrapper<PracticeAnswerEntity>()
                    .eq("is_deleted", 0).eq("session_id", s.getId()));
                vo.setDone(done == null ? 0 : done.intValue());
                vo.setAccuracy(null);
                vo.setStatus(Optional.ofNullable(s.getStatus()).orElse(0));
                return vo;
            }).collect(Collectors.toList()));
        }
        if ("exam".equalsIgnoreCase(type) || type == null) {
            List<SessionEntity> es = sessionService.list(new QueryWrapper<SessionEntity>()
                .eq("is_deleted", 0).eq("user_id", userId));
            result.addAll(es.stream().map(s -> {
                HistoryItemVO vo = new HistoryItemVO();
                vo.setId(s.getId());
                vo.setType("exam");
                vo.setTitle("试卷#" + s.getPaperId());
                vo.setStartTime(s.getStartTime());
                vo.setEndTime(s.getEndTime());
                vo.setTotal(s.getQuestionTotal());
                vo.setDone(s.getQuestionTotal());
                vo.setAccuracy(s.getAccuracy() == null ? null : s.getAccuracy().doubleValue());
                vo.setStatus(Optional.ofNullable(s.getStatus()).orElse(0));
                return vo;
            }).collect(Collectors.toList()));
        }
        // 简易分页（内存切片）
        result.sort(Comparator.comparing(HistoryItemVO::getStartTime, Comparator.nullsLast(Date::compareTo)).reversed());
        int p = page == null || page < 1 ? 1 : page;
        int sz = size == null || size < 1 ? 20 : size;
        int from = (p - 1) * sz;
        int to = Math.min(result.size(), from + sz);
        if (from >= result.size()) return Collections.emptyList();
        return result.subList(from, to);
    }

    public List<WrongItemVO> listWrong(Long userId, Long subjectId, Integer page, Integer size) {
        // 错题来自练习答案 is_correct=0（可按需叠加考试提交后的错误题）
        List<PracticeAnswerEntity> wrongList = practiceAnswerService.selectWrongQuestionIdsBySubject(userId, subjectId);
        if (wrongList == null || wrongList.isEmpty()) return Collections.emptyList();

		List<Long> wrongQids = wrongList.stream().map(PracticeAnswerEntity::getQuestionId).collect(Collectors.toList());

        List<QuestionsEntity> qs = questionsService.list(new QueryWrapper<QuestionsEntity>()
            .in("id", wrongQids).eq("is_deleted", 0));
		Map<Long, QuestionsEntity> qm = qs.stream().collect(Collectors.toMap(QuestionsEntity::getId, q -> q));

		// 根据题目 ID 获取第一个章节 ID
		Map<Long, Long> q2c = questionChapterService.list(new QueryWrapper<QuestionChapterEntity>()
			.in("question_id", wrongQids).eq("is_deleted", 0))
			.stream().collect(Collectors.toMap(QuestionChapterEntity::getQuestionId, QuestionChapterEntity::getChapterId));
		Map<Long, String> c2n = subjectChapterService.list(new QueryWrapper<SubjectChapterEntity>()
			.in("id", q2c.values()).eq("is_deleted", 0))
			.stream().collect(Collectors.toMap(SubjectChapterEntity::getId, SubjectChapterEntity::getName));


        List<WrongItemVO> list = wrongQids.stream().distinct().map(qid -> {
            WrongItemVO vo = new WrongItemVO();
            vo.setQuestionId(qid);
            QuestionsEntity q = qm.get(qid);
			if (q != null) {
				vo.setSubjectId(q.getSubjectId());
				vo.setType(q.getType());
				vo.setStem(q.getContent());
			}
			Long chapterId = q2c.get(qid);
			vo.setChapterId(chapterId);
			vo.setChapterName(c2n.get(chapterId));
            vo.setWrongCount(null);
            return vo;
        }).collect(Collectors.toList());

        // 分页
        int p = page == null || page < 1 ? 1 : page;
        int sz = size == null || size < 1 ? 20 : size;
        int from = (p - 1) * sz;
        int to = Math.min(list.size(), from + sz);
        if (from >= list.size()) return Collections.emptyList();
        return list.subList(from, to);
    }

    public ReportOverviewVO reportOverview(Long userId) {
        ReportOverviewVO vo = new ReportOverviewVO();
        // 次数统计
        Long practiceCount = practiceSessionService.count(new QueryWrapper<PracticeSessionEntity>()
            .eq("is_deleted", 0).eq("user_id", userId));
        Long examCount = sessionService.count(new QueryWrapper<SessionEntity>()
            .eq("is_deleted", 0).eq("user_id", userId));
        vo.setPracticeCount(practiceCount == null ? 0 : practiceCount.intValue());
        vo.setExamCount(examCount == null ? 0 : examCount.intValue());

        // 练习正确率（按题）：correct/total（仅练习）
        Long practiceTotal = practiceAnswerService.count(new QueryWrapper<PracticeAnswerEntity>()
            .eq("is_deleted", 0).eq("user_id", userId));
        Long practiceCorrect = practiceAnswerService.count(new QueryWrapper<PracticeAnswerEntity>()
            .eq("is_deleted", 0).eq("user_id", userId).eq("is_correct", 1));
        double practiceAcc = (practiceTotal == null || practiceTotal == 0) ? 0.0 : (practiceCorrect * 100.0 / practiceTotal);
        vo.setPracticeAccuracy(practiceAcc);

        // 考试正确率（按题逐题判分：全对才正确）
        List<SessionEntity> sessions = sessionService.list(new QueryWrapper<SessionEntity>()
            .eq("is_deleted", 0).eq("user_id", userId));

        // 考试正确率近似：使用每场 session 的 accuracy，按题量加权平均
        // 由于无公共批量查询，这里按“overall accuracy 已在 session.accuracy”近似求平均（加权）
        double examAcc;
        if (sessions.isEmpty()) {
            examAcc = 0.0;
        } else {
            // 加权：按每场考试的题量加权平均
            long sumQ = sessions.stream().mapToLong(s -> Optional.ofNullable(s.getQuestionTotal()).orElse(0)).sum();
            if (sumQ == 0) {
                examAcc = 0.0;
            } else {
                double sum = sessions.stream()
                    .mapToDouble(s -> {
                        double acc = s.getAccuracy() == null ? 0.0 : s.getAccuracy().doubleValue();
                        int qt = Optional.ofNullable(s.getQuestionTotal()).orElse(0);
                        return acc * qt;
                    }).sum();
                examAcc = sum / sumQ;
            }
        }
        vo.setExamAccuracy(examAcc);

        // 练习+考试总体正确率：加权合成
        double overall;
        long practiceQ = practiceTotal == null ? 0 : practiceTotal;
        long examQ = sessions.stream().mapToLong(s -> Optional.ofNullable(s.getQuestionTotal()).orElse(0)).sum();
        if (practiceQ + examQ == 0) {
            overall = 0.0;
        } else {
            overall = (practiceAcc * practiceQ + examAcc * examQ) / (practiceQ + examQ);
        }
        vo.setAccuracy(overall);

        // 薄弱科目Top3（错误题最多的科目，基于练习）
        Map<Long, Long> wrongBySubject = practiceAnswerService.list(new QueryWrapper<PracticeAnswerEntity>()
            .eq("is_deleted", 0).eq("user_id", userId).eq("is_correct", 0))
            .stream().collect(Collectors.groupingBy(PracticeAnswerEntity::getSubjectId, Collectors.counting()));
        List<Long> subjectIds = wrongBySubject.entrySet().stream()
            .sorted(Map.Entry.<Long, Long>comparingByValue().reversed())
            .limit(3).map(Map.Entry::getKey).collect(Collectors.toList());
        if (!subjectIds.isEmpty()) {
            List<SubjectsEntity> subs = subjectsService.list(new QueryWrapper<SubjectsEntity>().in("id", subjectIds));
            Map<Long, String> sname = subs.stream().collect(Collectors.toMap(SubjectsEntity::getId, SubjectsEntity::getName));
            vo.setWeakSubjects(subjectIds.stream().map(id -> sname.getOrDefault(id, "科目#"+id)).collect(Collectors.toList()));
        } else {
            vo.setWeakSubjects(Collections.emptyList());
        }
        return vo;
    }

    private String subjectName(Long subjectId) {
        SubjectsEntity s = subjectsService.getById(subjectId);
        return s == null ? ("科目#" + subjectId) : s.getName();
    }
}

