package org.springblade.modules.dingding.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "考试结果详情")
public class ResultDetailVO {
    @Schema(description = "会话ID")
    private Long sessionId;
    @Schema(description = "试卷ID")
    private Long paperId;
    @Schema(description = "试卷名称")
    private String paperName;
    @Schema(description = "分数")
    private BigDecimal score;
    @Schema(description = "正确率（0-100）")
    private Double accuracy;
    @Schema(description = "总题数")
    private Integer total;
    @Schema(description = "用时（秒）")
    private Integer durationSec;

    @Schema(description = "题目详情列表")
    private List<Item> list;

    @Data
    public static class Item {
        private Long questionId;
        private String type;
        private String stem;
        private List<Option> options;
        private List<String> correctAnswers;
        private List<String> userAnswers;
        private Boolean correct;
        private String analysis;
        private String knowledge; // 由标签等字段转换
    }

    @Data
    public static class Option {
        private String value;
        private String label;
        private String text;
    }
}

