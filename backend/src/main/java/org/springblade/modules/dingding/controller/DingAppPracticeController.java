package org.springblade.modules.dingding.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.modules.dingding.pojo.dto.CreatePracticeSessionDTO;
import org.springblade.modules.dingding.pojo.dto.SavePracticeAnswerDTO;
import org.springblade.modules.dingding.pojo.vo.PracticeSessionVO;
import org.springblade.modules.dingding.pojo.vo.PracticeResultVO;
import org.springblade.modules.dingding.pojo.vo.QuestionPageVO;
import org.springblade.modules.dingding.service.DingPracticeService;
import org.springblade.modules.practice.pojo.entity.PracticeAnswerEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dingapp/practice")
@Tag(name = "H5 练习", description = "钉钉H5练习接口")
public class DingAppPracticeController extends BladeController {

    private final DingPracticeService practiceService;

    @PostMapping("/sessions")
    @Operation(summary = "创建/恢复练习会话")
    public R<PracticeSessionVO> createSession(@RequestBody CreatePracticeSessionDTO dto) {
		Long userId = AuthUtil.getUserId();
        return R.data(practiceService.createSession(userId, dto));
    }

    @GetMapping("/sessions/{sessionId}/questions")
    @Operation(summary = "获取会话题目（分页/游标）")
    public R<QuestionPageVO> getQuestions(@PathVariable Long sessionId,
                                          @RequestParam(required = false, defaultValue = "0") Integer cursor,
                                          @RequestParam(required = false, defaultValue = "10") Integer limit) {
        return R.data(practiceService.getQuestionsPage(sessionId, cursor, limit));
    }

    @PostMapping("/sessions/{sessionId}/multiple_answers")
    @Operation(summary = "保存多个练习题目的作答")
    public R<List<PracticeAnswerEntity>> saveMultipleAnswers(@PathVariable Long sessionId, @RequestBody List<SavePracticeAnswerDTO> answers) {
		Long userId = AuthUtil.getUserId();
		List<PracticeAnswerEntity> practiceAnswerEntities = practiceService.saveAnswers(userId, sessionId, answers);
		return R.data(practiceAnswerEntities);
    }

	@PostMapping("/sessions/{sessionId}/answer")
	@Operation(summary = "保存一道练习题目的作答")
	public R<List<PracticeAnswerEntity>> saveAnswer(@PathVariable Long sessionId, @RequestBody SavePracticeAnswerDTO answer) {
		Long userId = AuthUtil.getUserId();
		List<PracticeAnswerEntity> practiceAnswerEntities = practiceService.saveAnswers(userId, sessionId, List.of(answer));
		return R.data(practiceAnswerEntities);
	}

	@GetMapping("/sessions/{sessionId}/result")
	@Operation(summary = "获取练习结果统计")
	public R<PracticeResultVO> getPracticeResult(@PathVariable Long sessionId) {
		return R.data(practiceService.getPracticeResult(sessionId));
	}

	@PostMapping("/sessions/{sessionId}/finish")
	@Operation(summary = "完成练习会话")
	public R<PracticeResultVO> finishPractice(@PathVariable Long sessionId) {
		practiceService.finishPracticeSession(sessionId);
		return R.data(practiceService.getPracticeResult(sessionId));
	}
}

