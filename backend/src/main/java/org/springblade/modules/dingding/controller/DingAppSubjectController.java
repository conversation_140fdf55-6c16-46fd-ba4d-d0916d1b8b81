package org.springblade.modules.dingding.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.modules.dingding.pojo.vo.SubjectProgressVO;
import org.springblade.modules.dingding.pojo.vo.SubjectSummaryVO;
import org.springblade.modules.dingding.pojo.vo.SubjectChapterVO;
import org.springblade.modules.dingding.service.DingSubjectService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dingapp/subjects")
@Tag(name = "H5 科目", description = "钉钉H5科目接口")
public class DingAppSubjectController extends BladeController {

    private final DingSubjectService subjectService;

    @GetMapping("/progress")
    @Operation(summary = "科目进度列表")
    public R<List<SubjectProgressVO>> progress() {
        Long userId = AuthUtil.getUserId();
        return R.data(subjectService.listSubjectProgress(userId));
    }

    @GetMapping("/{subjectId}/summary")
    @Operation(summary = "科目概览")
    public R<SubjectSummaryVO> summary(@PathVariable Long subjectId) {
		Long userId = AuthUtil.getUserId();
        return R.data(subjectService.getSubjectSummary(userId, subjectId));
    }

    @GetMapping("/{subjectId}/chapters")
    @Operation(summary = "章节列表（含进度）")
    public R<List<SubjectChapterVO>> chapters(@PathVariable Long subjectId) {
		Long userId = AuthUtil.getUserId();
        return R.data(subjectService.listChapters(userId, subjectId));
    }
}

