package org.springblade.modules.dingding.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "题目信息")
public class QuestionVO {
    @Schema(description = "题目ID")
    private Long id;
    @Schema(description = "题型")
    private String type;
    @Schema(description = "题干（content）")
    private String stem;
    @Schema(description = "选项")
    private List<Option> options;
    @Schema(description = "正确答案（选项值列表，如 ['A'] 或 ['A','C']）")
    private List<String> correctAnswers;

    @Data
    public static class Option {
      private String value;
      private String label;
      private String text;
    }
}

