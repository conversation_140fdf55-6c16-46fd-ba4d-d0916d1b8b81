package org.springblade.modules.dingding.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springblade.modules.answer.pojo.entity.AnswerEntity;
import org.springblade.modules.answer.service.IAnswerService;
import org.springblade.modules.dingding.pojo.dto.CreateExamSessionDTO;
import org.springblade.modules.dingding.pojo.dto.SaveExamAnswerDTO;
import org.springblade.modules.dingding.pojo.vo.*;
import org.springblade.modules.papers.constant.PaperStatusConstant;
import org.springblade.modules.papers.mapper.PaperItemsMapper;
import org.springblade.modules.papers.pojo.entity.PaperItemsEntity;
import org.springblade.modules.papers.pojo.entity.PapersEntity;
import org.springblade.modules.papers.service.IPapersService;
import org.springblade.modules.practice.pojo.entity.SessionEntity;
import org.springblade.modules.practice.service.ISessionService;
import org.springblade.modules.questions.pojo.entity.QuestionsEntity;
import org.springblade.modules.questions.service.IQuestionsService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DingExamService {

	private final IPapersService papersService;
	private final PaperItemsMapper paperItemsMapper;
	private final IQuestionsService questionsService;
	private final ISessionService sessionService;
	private final IAnswerService answerService;

	private final ObjectMapper objectMapper = new ObjectMapper();

	public SessionVO createSession(Long userId, CreateExamSessionDTO dto) {
		PapersEntity paper = papersService.getById(dto.getPaperId());
		if (paper == null || paper.getIsDeleted() == 1 || paper.getStatus() == null || !Objects.equals(paper.getStatus(), PaperStatusConstant.PUBLISHED)) {
			throw new IllegalArgumentException("试卷不可用或不存在");
		}
		List<PaperItemsEntity> items = paperItemsMapper.selectList(Wrappers.<PaperItemsEntity>lambdaQuery()
			.eq(PaperItemsEntity::getIsDeleted, 0)
			.eq(PaperItemsEntity::getPaperId, dto.getPaperId())
			.orderByAsc(PaperItemsEntity::getOrderNo));
		List<Long> questionIds = items.stream().map(PaperItemsEntity::getQuestionId).collect(Collectors.toList());

		SessionEntity session = new SessionEntity();
		session.setUserId(userId);
		session.setPaperId(dto.getPaperId());
		session.setQuestionTotal(questionIds.size());
		session.setStartTime(new Date());
		session.setEndTime(null);
		// 倒计时截止
		if (paper.getDurationMinutes() != null) {
			Calendar cal = Calendar.getInstance();
			cal.setTime(session.getStartTime());
			cal.add(Calendar.MINUTE, paper.getDurationMinutes());
			session.setExpireTime(cal.getTime());
		}
		try {
			session.setQuestionIds(objectMapper.writeValueAsString(questionIds));
		} catch (Exception ignored) {
		}
		session.setStatus(0);
		sessionService.save(session);

		SessionVO vo = new SessionVO();
		vo.setSessionId(session.getId());
		vo.setPaperId(dto.getPaperId());
		vo.setTotal(session.getQuestionTotal());
		vo.setEndAt(session.getExpireTime());
		return vo;
	}

	public QuestionPageVO getQuestionsPage(Long sessionId, Integer cursor, Integer limit) {
		SessionEntity session = sessionService.getById(sessionId);
		if (session == null || session.getQuestionIds() == null) {
			return new QuestionPageVO();
		}
		List<Long> ids;
		try {
			ids = objectMapper.readValue(session.getQuestionIds(), new TypeReference<List<Long>>() {
			});
		} catch (Exception e) {
			ids = Collections.emptyList();
		}
		int from = Math.max(0, cursor == null ? 0 : cursor);
		int to = Math.min(ids.size(), from + (limit == null ? 10 : limit));
		List<Long> pageIds = ids.subList(from, to);
		if (pageIds.isEmpty()) {
			QuestionPageVO vo = new QuestionPageVO();
			vo.setList(Collections.emptyList());
			vo.setNextCursor(null);
			return vo;
		}
		List<QuestionsEntity> questions = questionsService.list(Wrappers.<QuestionsEntity>lambdaQuery()
			.eq(QuestionsEntity::getIsDeleted, 0)
			.in(QuestionsEntity::getId, pageIds));
		Map<Long, QuestionsEntity> map = questions.stream().collect(Collectors.toMap(QuestionsEntity::getId, q -> q));
		List<QuestionVO> list = pageIds.stream().map(map::get).filter(Objects::nonNull).map(this::mapQuestion).collect(Collectors.toList());
		QuestionPageVO vo = new QuestionPageVO();
		vo.setList(list);
		vo.setNextCursor(to < ids.size() ? to : null);
		return vo;
	}

	public void saveAnswers(Long userId, Long sessionId, List<SaveExamAnswerDTO> answers) {
		Date now = new Date();
		for (SaveExamAnswerDTO a : answers) {
			AnswerEntity e = new AnswerEntity();
			e.setSessionId(sessionId);
			e.setUserId(userId);
			e.setQuestionId(a.getQuestionId());
			e.setValuesJson(writeJson(a.getValues()));
			e.setDurationSec(a.getDurationSec());
			e.setSubmittedAt(a.getSubmittedAt() != null ? a.getSubmittedAt() : now);
			AnswerEntity exists = answerService.getOne(Wrappers.<AnswerEntity>lambdaQuery()
				.eq(AnswerEntity::getIsDeleted, 0)
				.eq(AnswerEntity::getSessionId, sessionId)
				.eq(AnswerEntity::getQuestionId, a.getQuestionId()));
			if (exists == null) {
				answerService.save(e);
			} else {
				e.setId(exists.getId());
				answerService.updateById(e);
			}
		}
	}

	public Map<String, Object> submit(Long userId, Long sessionId) {
		SessionEntity session = sessionService.getById(sessionId);
		if (session == null) {
			throw new IllegalArgumentException("会话不存在");
		}
		// 判分
		List<Long> ids;
		try {
			ids = objectMapper.readValue(session.getQuestionIds(), new TypeReference<List<Long>>() {
			});
		} catch (Exception e) {
			ids = Collections.emptyList();
		}
		List<QuestionsEntity> questions = questionsService.list(Wrappers.<QuestionsEntity>lambdaQuery()
			.eq(QuestionsEntity::getIsDeleted, 0)
			.in(ids != null && !ids.isEmpty(), QuestionsEntity::getId, ids));
		Map<Long, QuestionsEntity> qm = questions.stream().collect(Collectors.toMap(QuestionsEntity::getId, q -> q));

		List<AnswerEntity> userAnswers = answerService.list(Wrappers.<AnswerEntity>lambdaQuery()
			.eq(AnswerEntity::getIsDeleted, 0)
			.eq(AnswerEntity::getSessionId, sessionId));
		int correct = 0;
		for (Long qid : ids) {
			QuestionsEntity q = qm.get(qid);
			if (q == null) continue;
			Set<String> expect = splitAnswer(q.getAnswer());
			AnswerEntity ans = userAnswers.stream().filter(a -> Objects.equals(a.getQuestionId(), qid)).findFirst().orElse(null);
			Set<String> got = ans == null ? Collections.emptySet() : readSet(ans.getValuesJson());
			if (expect.equals(got)) correct++;
		}
		double accuracy = ids.isEmpty() ? 0.0 : (correct * 100.0 / ids.size());
		// 分数计算：题全对得该题分，否则0分
		List<PaperItemsEntity> items = paperItemsMapper.selectList(Wrappers.<PaperItemsEntity>lambdaQuery()
			.eq(PaperItemsEntity::getIsDeleted, 0)
			.eq(PaperItemsEntity::getPaperId, session.getPaperId()));
		Map<Long, PaperItemsEntity> itemByQ = items.stream().collect(Collectors.toMap(PaperItemsEntity::getQuestionId, x -> x));
		java.math.BigDecimal score = java.math.BigDecimal.ZERO;
		for (Long qid : ids) {
			QuestionsEntity q = qm.get(qid);
			if (q == null) continue;
			Set<String> expect = splitAnswer(q.getAnswer());
			AnswerEntity ans = userAnswers.stream().filter(a -> Objects.equals(a.getQuestionId(), qid)).findFirst().orElse(null);
			Set<String> got = ans == null ? Collections.emptySet() : readSet(ans.getValuesJson());
			if (expect.equals(got)) {
				java.math.BigDecimal s = Optional.ofNullable(itemByQ.get(qid)).map(PaperItemsEntity::getScore).orElse(java.math.BigDecimal.ZERO);
				score = score.add(s);
			}
		}
		session.setAccuracy(java.math.BigDecimal.valueOf(accuracy));
		session.setScore(score);
		session.setEndTime(new Date());
		session.setStatus(1);
		sessionService.updateById(session);

		Map<String, Object> result = new HashMap<>();
		result.put("score", score);
		result.put("accuracy", accuracy);
		result.put("sessionId", session.getId());
		return result;
	}

	public ResultVO getExamResult(Long sessionId) {
		SessionEntity s = sessionService.getById(sessionId);
		if (s == null) return null;
		ResultVO vo = new ResultVO();
		vo.setSessionId(s.getId());
		vo.setPaperId(s.getPaperId());
		vo.setScore(s.getScore());
		vo.setAccuracy(s.getAccuracy() == null ? 0.0 : s.getAccuracy().doubleValue());
		return vo;
	}

	public ResultDetailVO getExamDetail(Long sessionId) {
		SessionEntity s = sessionService.getById(sessionId);
		if (s == null) return null;
		List<Long> ids;
		try {
			ids = objectMapper.readValue(s.getQuestionIds(), new TypeReference<>() {
			});
		} catch (Exception e) {
			ids = Collections.emptyList();
		}
		List<QuestionsEntity> questions = ids.isEmpty() ? Collections.emptyList() : questionsService.list(Wrappers.<QuestionsEntity>lambdaQuery()
			.eq(QuestionsEntity::getIsDeleted, 0)
			.in(QuestionsEntity::getId, ids));
		Map<Long, QuestionsEntity> qm = questions.stream().collect(Collectors.toMap(QuestionsEntity::getId, q -> q));
		List<AnswerEntity> answers = answerService.list(Wrappers.<AnswerEntity>lambdaQuery()
			.eq(AnswerEntity::getIsDeleted, 0)
			.eq(AnswerEntity::getSessionId, sessionId));

		ResultDetailVO vo = new ResultDetailVO();
		vo.setSessionId(s.getId());
		vo.setPaperId(s.getPaperId());
		// 试卷名称
		PapersEntity paper = papersService.getById(s.getPaperId());
		vo.setPaperName(paper == null ? null : paper.getName());
		vo.setScore(s.getScore());
		vo.setAccuracy(s.getAccuracy() == null ? 0.0 : s.getAccuracy().doubleValue());
		vo.setTotal(Optional.ofNullable(s.getQuestionTotal()).orElse(0));
		Integer dur = null;
		if (s.getStartTime() != null && s.getEndTime() != null) {
			dur = (int) ((s.getEndTime().getTime() - s.getStartTime().getTime()) / 1000);
		}
		vo.setDurationSec(dur == null ? 0 : dur);

		List<ResultDetailVO.Item> list = new ArrayList<>();
		for (Long qid : ids) {
			QuestionsEntity q = qm.get(qid);
			if (q == null) continue;
			ResultDetailVO.Item it = new ResultDetailVO.Item();
			it.setQuestionId(q.getId());
			it.setType(q.getType());
			it.setStem(q.getContent());
			// options 按 Key 排序
			List<ResultDetailVO.Option> opts;
			try {
				Map<String, String> map = new ObjectMapper().readValue(q.getOptions(), new TypeReference<Map<String, String>>() {
				});
				opts = map.entrySet().stream().sorted(Map.Entry.comparingByKey()).map(e -> {
					ResultDetailVO.Option o = new ResultDetailVO.Option();
					o.setValue(e.getKey());
					o.setLabel(e.getKey());
					o.setText(e.getValue());
					return o;
				}).collect(Collectors.toList());
			} catch (Exception e) {
				opts = Collections.emptyList();
			}
			it.setOptions(opts);
			// answers
			Set<String> expect = splitAnswer(q.getAnswer());
			it.setCorrectAnswers(new ArrayList<>(expect));
			AnswerEntity ans = answers.stream().filter(a -> Objects.equals(a.getQuestionId(), qid)).findFirst().orElse(null);
			Set<String> got = ans == null ? Collections.emptySet() : readSet(ans.getValuesJson());
			it.setUserAnswers(new ArrayList<>(got));
			it.setCorrect(expect.equals(got));
			// analysis / knowledge
			it.setAnalysis(q.getExplanation());
			it.setKnowledge(parseKnowledge(q.getTags()));
			list.add(it);
		}
		vo.setList(list);
		return vo;
	}

	private String parseKnowledge(String tagsJson) {
		if (tagsJson == null || tagsJson.isEmpty()) return null;
		try {
			List<String> arr = new ObjectMapper().readValue(tagsJson, new TypeReference<List<String>>() {
			});
			return String.join("、", arr);
		} catch (Exception e) {
			return tagsJson; // 兼容非JSON情形
		}
	}

	private QuestionVO mapQuestion(QuestionsEntity q) {
		QuestionVO vo = new QuestionVO();
		vo.setId(q.getId());
		vo.setType(q.getType());
		vo.setStem(q.getContent());
		try {
			Map<String, String> map = new ObjectMapper().readValue(q.getOptions(), new TypeReference<Map<String, String>>() {
			});
			List<QuestionVO.Option> opts = map.entrySet().stream()
				.sorted(Map.Entry.comparingByKey())
				.map(e -> {
					QuestionVO.Option o = new QuestionVO.Option();
					o.setValue(e.getKey());
					o.setLabel(e.getKey());
					o.setText(e.getValue());
					return o;
				})
				.collect(Collectors.toList());
			vo.setOptions(opts);
		} catch (Exception e) {
			vo.setOptions(Collections.emptyList());
		}
		return vo;
	}

	private String writeJson(Object obj) {
		try {
			return new ObjectMapper().writeValueAsString(obj);
		} catch (Exception e) {
			return null;
		}
	}

	private Set<String> readSet(String json) {
		if (json == null || json.isEmpty()) return Collections.emptySet();
		try {
			List<String> arr = new ObjectMapper().readValue(json, new TypeReference<List<String>>() {
			});
			return new HashSet<>(arr);
		} catch (Exception e) {
			return Collections.emptySet();
		}
	}

	private Set<String> splitAnswer(String answer) {
		if (answer == null) return Collections.emptySet();
		String a = answer.trim();
		if (a.startsWith("[") && a.endsWith("]")) {
			try {
				List<String> arr = new ObjectMapper().readValue(a, new TypeReference<List<String>>() {
				});
				return new HashSet<>(arr);
			} catch (Exception ignored) {
			}
		}
		if (a.contains(",")) {
			return Arrays.stream(a.split(",")).map(String::trim).filter(s -> !s.isEmpty()).collect(Collectors.toSet());
		}
		return a.isEmpty() ? Collections.emptySet() : Set.of(a);
	}
}

