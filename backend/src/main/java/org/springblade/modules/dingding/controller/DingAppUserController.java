package org.springblade.modules.dingding.controller;

import cn.hutool.log.StaticLog;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.request.AuthRequest;
import me.zhyd.oauth.utils.AuthStateUtils;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.social.utils.SocialUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.modules.auth.request.IdaasRequest;
import org.springblade.modules.dingding.service.DingAppUserService;
import org.springblade.modules.dingding.utils.DdConfigSign;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import org.springblade.modules.dingding.service.DingUserFacade;
import org.springblade.modules.dingding.pojo.vo.UserMeVO;

import java.io.IOException;
import java.util.Map;

/**
 * H5 用户接口（钉钉 H5 专用）
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/dingapp/user")
@Tag(name = "H5 用户", description = "钉钉H5用户接口")
public class DingAppUserController extends BladeController {

    private final DingUserFacade dingUserFacade;

	private final DingAppUserService dingAppUserService;

	@Value("${idaas.pic.client}")
	private String picClient;
	@Value("${idaas.pic.secret}")
	private String picSecret;
	@Value("${idaas.pic.redirect}")
	private String picRedirect;
	@Value("${idaas.pic.server}")
	private String picIDaaSServer;
	@Value("${idaas.pic.login}")
	private String picLogin;

	@PostMapping("/login")
	public Kv login(@Valid @RequestBody Map<String, Object> dataMap) throws Exception {
		String authCode = (String) dataMap.get("authCode");
		Kv login = dingAppUserService.login(authCode);
		return login;
	}

	@GetMapping("/loginByUid")
	public Kv loginByUid(String uid) {
		return dingAppUserService.loginByUid(uid);
	}


	@GetMapping("getDingTalkApiParam")
	public Kv getDingTalkApiParam(@RequestParam(name = "url")String url,
								  @RequestParam(name = "nonceStr")String nonceStr,
								  @RequestParam(name = "timeStamp")Long timeStamp){
		Kv kv=Kv.create();
		try{
			String ticket=dingAppUserService.getJsapiTicket();
			String signature= DdConfigSign.sign(ticket,nonceStr,timeStamp,url);
			kv.put("code",200);
			kv.put("signature",signature);
		}catch (Exception e){
			kv.put("code",500);
			kv.put("signature",e.getMessage());
			StaticLog.error(e.getMessage());
		}
		return kv;
	}

	@GetMapping("/render")
	public void render(HttpServletResponse response, HttpServletRequest request) throws IOException {
		StaticLog.info("钉钉 H5 登录重定向到 统一登录页入口");
		String redirect = "";

		AuthConfig authConfig = new AuthConfig();
		authConfig.setClientId(this.picClient);
		authConfig.setClientSecret(this.picSecret);

		redirect = this.picRedirect;

		authConfig.setRedirectUri(redirect);
		authConfig.setIgnoreCheckState(true);

		AuthRequest authRequest = new IdaasRequest(authConfig, SocialUtil.getAuthStateCache(), this.picIDaaSServer);
		String authorizeUrl = authRequest.authorize(AuthStateUtils.createState());
		StaticLog.info("钉钉 H5 登录重定向到 统一登录页入口 {}", authorizeUrl);
		response.sendRedirect(authorizeUrl);
	}

    /**
     * 获取当前用户信息（由服务层从现有用户体系聚合）
     */
    @GetMapping("/me")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "当前用户信息")
    public R<UserMeVO> me() {
        UserMeVO me = dingUserFacade.getCurrentUserInfo();
        return R.data(me);
    }
}

