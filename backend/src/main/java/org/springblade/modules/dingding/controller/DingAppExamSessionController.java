package org.springblade.modules.dingding.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.modules.dingding.pojo.dto.CreateExamSessionDTO;
import org.springblade.modules.dingding.pojo.dto.SaveExamAnswerDTO;
import org.springblade.modules.dingding.pojo.vo.SessionVO;
import org.springblade.modules.dingding.pojo.vo.QuestionPageVO;
import org.springblade.modules.dingding.service.DingExamService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dingapp/exam")
@Tag(name = "H5 考试会话", description = "钉钉H5考试会话接口")
public class DingAppExamSessionController extends BladeController {

    private final DingExamService examService;

    @PostMapping("/sessions")
    @Operation(summary = "创建考试会话")
    public R<SessionVO> createSession(@RequestBody CreateExamSessionDTO dto) {
		Long userId = AuthUtil.getUserId();
        return R.data(examService.createSession(userId, dto));
    }

    @GetMapping("/sessions/{sessionId}/questions")
    @Operation(summary = "获取考试题目（分页/游标）")
    public R<QuestionPageVO> getQuestions(@PathVariable Long sessionId,
                                          @RequestParam(required = false, defaultValue = "0") Integer cursor,
                                          @RequestParam(required = false, defaultValue = "10") Integer limit) {
        return R.data(examService.getQuestionsPage(sessionId, cursor, limit));
    }

    @PostMapping("/sessions/{sessionId}/answers")
    @Operation(summary = "保存考试作答")
    public R<?> saveAnswers(@PathVariable Long sessionId, @RequestBody List<SaveExamAnswerDTO> answers) {
		Long userId = AuthUtil.getUserId();
        examService.saveAnswers(userId, sessionId, answers);
        return R.success("");
    }

    @PostMapping("/sessions/{sessionId}/submit")
    @Operation(summary = "提交试卷")
    public R<Map<String, Object>> submit(@PathVariable Long sessionId) {
		Long userId = AuthUtil.getUserId();
        return R.data(examService.submit(userId, sessionId));
    }
}

