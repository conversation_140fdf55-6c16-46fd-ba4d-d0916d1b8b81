package org.springblade.modules.dingding.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * H5 用户信息 VO（钉钉 H5 专用）
 */
@Data
@Schema(description = "H5 用户信息")
public class UserMeVO {
    @Schema(description = "用户ID")
    private Long id;

    @Schema(description = "昵称/姓名")
    private String name;

    @Schema(description = "学习等级，例如 LV.1")
    private String level;

    @Schema(description = "统计信息")
    private Stats stats;

    @Data
    @Schema(description = "学习统计")
    public static class Stats {
        @Schema(description = "练习次数")
        private Integer practiceCount;
        @Schema(description = "考试次数")
        private Integer examCount;
        @Schema(description = "正确率百分比（0-100）")
        private Double accuracy;
    }
}

