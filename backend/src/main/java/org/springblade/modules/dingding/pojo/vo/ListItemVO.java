package org.springblade.modules.dingding.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "H5 考试列表项")
public class ListItemVO {
    @Schema(description = "试卷ID")
    private Long paperId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "是否可参加")
    private Boolean available;

    @Schema(description = "题量")
    private Integer questionCount;

    @Schema(description = "时长(分钟)")
    private Integer duration;

    @Schema(description = "描述")
    private String desc;
}

