package org.springblade.modules.dingding.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "错题项")
public class WrongItemVO {
	@Schema(description = "章节ID")
	private Long chapterId;
	@Schema(description = "章节名称")
	private String chapterName;
	@Schema(description = "科目ID")
    private Long subjectId;
	@Schema(description = "题目类型")
    private String type;
    @Schema(description = "题目ID")
    private Long questionId;
    @Schema(description = "题干摘要")
    private String stem;
    @Schema(description = "错误次数（可选）")
    private Integer wrongCount;
}

