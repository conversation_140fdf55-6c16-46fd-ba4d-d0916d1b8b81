package org.springblade.modules.dingding.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "学习报告概览")
public class ReportOverviewVO {
    @Schema(description = "总体正确率（0-100）")
    private Double accuracy;
    @Schema(description = "练习正确率（0-100）")
    private Double practiceAccuracy;
    @Schema(description = "考试正确率（0-100）")
    private Double examAccuracy;

    @Schema(description = "练习次数")
    private Integer practiceCount;
    @Schema(description = "考试次数")
    private Integer examCount;
    @Schema(description = "薄弱科目Top3（科目名称）")
    private List<String> weakSubjects;
}

