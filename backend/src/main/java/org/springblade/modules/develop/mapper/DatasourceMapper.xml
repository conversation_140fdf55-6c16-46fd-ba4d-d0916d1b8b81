<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.develop.mapper.DatasourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="datasourceResultMap" type="org.springblade.modules.develop.pojo.entity.Datasource">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="driver_class" property="driverClass"/>
        <result column="url" property="url"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="remark" property="remark"/>
    </resultMap>

</mapper>
