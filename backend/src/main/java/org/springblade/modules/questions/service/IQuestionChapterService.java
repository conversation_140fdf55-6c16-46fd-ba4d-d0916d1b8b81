/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.questions.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.modules.questions.excel.QuestionChapterExcel;
import org.springblade.modules.questions.pojo.entity.QuestionChapterEntity;
import org.springblade.modules.questions.pojo.vo.QuestionChapterVO;

import java.util.List;

/**
 * 题目-章节关联中间表 服务类
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
public interface IQuestionChapterService extends BaseService<QuestionChapterEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param questionChapter 查询参数
	 * @return IPage<QuestionChapterVO>
	 */
	IPage<QuestionChapterVO> selectQuestionChapterPage(IPage<QuestionChapterVO> page, QuestionChapterVO questionChapter);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<QuestionChapterExcel>
	 */
	List<QuestionChapterExcel> exportQuestionChapter(Wrapper<QuestionChapterEntity> queryWrapper);

	/**
	 * 查询某科目下某章节的题目ID集合（过滤逻辑删除）
	 * @param subjectId
	 * @param chapterId
	 * @return
	 */
	List<Long> selectQuestionIdsByChapter(Long subjectId, Long chapterId);

	/**
	 * 查询某科目下所有章节的去重题目ID集合
	 * @param subjectId
	 * @return
	 */
	List<Long> selectDistinctQuestionIdsBySubject(Long subjectId);

	/**
	 * 查询某科目下所有章节的题目ID（按章节顺序、题目ID升序）
	 * @param subjectId
	 * @return
	 */
	List<Long> selectQuestionIdsBySubjectOrderByChapterOrder(Long subjectId);

	/**
	 * 批量保存章节关联题目（全量替换、维护总数）
	 * @param chapterId 章节ID
	 * @param items 题目关联列表（questionId, orderNo）
	 */
	boolean saveChapterItems(Long chapterId, java.util.List<QuestionChapterEntity> items);
}
