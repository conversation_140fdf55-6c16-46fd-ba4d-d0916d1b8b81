<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.questions.mapper.QuestionsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="questionsResultMap" type="org.springblade.modules.questions.pojo.entity.QuestionsEntity">
        <result column="id" property="id"/>
        <result column="subject_id" property="subjectId"/>
        <result column="type" property="type"/>
        <result column="difficulty" property="difficulty"/>
        <result column="content" property="content"/>
        <result column="options" property="options"/>
        <result column="answer" property="answer"/>
        <result column="explanation" property="explanation"/>
        <result column="tags" property="tags"/>
        <result column="role_ids" property="roleIds"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="questionsVOResultMap" type="org.springblade.modules.questions.pojo.vo.QuestionsVO" extends="questionsResultMap">
        <result column="subject_name" property="subjectName"/>
    </resultMap>

    <select id="selectQuestionsPage" resultMap="questionsVOResultMap">
        select
            eq.*,
            es.name as subject_name
        from exam_questions as eq
            left join exam_subjects as es on eq.subject_id = es.id
        where eq.is_deleted = 0
        <!-- 权限控制：只显示用户有权限查看的数据，管理员角色不受限制 -->
        <if test="userRoleIds != null and userRoleIds != ''">
            AND (
                eq.role_ids IS NULL
                OR eq.role_ids = ''
                OR eq.role_ids REGEXP CONCAT('(^|,)(', REPLACE(#{userRoleIds}, ',', '|'), ')(,|$)')
            )
        </if>
        <if test="questions.subjectId!=null">
            and eq.subject_id = #{questions.subjectId}
        </if>
        <if test="questions.type!=null and questions.type!=''">
            and eq.type = #{questions.type}
        </if>
        <if test="questions.difficulty!=null and questions.difficulty!=''">
            and eq.difficulty = #{questions.difficulty}
        </if>
        <if test="questions.content!=null and questions.content!=''">
            and eq.content like concat(concat('%', #{questions.content}),'%')
        </if>
        <if test="questions.tags!=null and questions.tags!=''">
            and eq.tags like concat(concat('%', #{questions.tags}),'%')
        </if>
        <if test="questions.status!=null and questions.status>=0">
            and eq.status = #{questions.status}
        </if>
        <if test="questions.subjectName!=null and questions.subjectName!=''">
            and es.name like concat(concat('%', #{questions.subjectName}),'%')
        </if>
        <if test="questions.idList != null and questions.idList.size > 0">
            and eq.id in
            <foreach collection="questions.idList" item="id" open="(" separator="," close=")">#{id}</foreach>
        </if>
    </select>


    <select id="exportQuestions" resultType="org.springblade.modules.questions.excel.QuestionsExcel">
        SELECT * FROM exam_questions
        WHERE 1=1
        <!-- 权限控制：只导出用户有权限查看的数据 -->
        <if test="userRoleIds != null and userRoleIds != ''">
            AND (
                role_ids IS NULL
                OR role_ids = ''
                OR role_ids REGEXP CONCAT('(^|,)(', REPLACE(#{userRoleIds}, ',', '|'), ')(,|$)')
            )
        </if>
        ${ew.customSqlSegment}
    </select>

</mapper>
