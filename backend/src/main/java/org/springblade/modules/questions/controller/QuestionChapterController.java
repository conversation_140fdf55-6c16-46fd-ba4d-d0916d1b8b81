/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.questions.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.IsAdmin;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.questions.excel.QuestionChapterExcel;
import org.springblade.modules.questions.pojo.entity.QuestionChapterEntity;
import org.springblade.modules.questions.pojo.vo.QuestionChapterVO;
import org.springblade.modules.questions.service.IQuestionChapterService;
import org.springblade.modules.questions.wrapper.QuestionChapterWrapper;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 题目-章节关联中间表 控制器
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-questionchapter/questionChapter")
@Tag(name = "题目-章节关联中间表", description = "题目-章节关联中间表接口")
public class QuestionChapterController extends BladeController {

	private final IQuestionChapterService questionChapterService;

	/**
	 * 题目-章节关联中间表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入questionChapter")
	public R<QuestionChapterVO> detail(QuestionChapterEntity questionChapter) {
		QuestionChapterEntity detail = questionChapterService.getOne(Condition.getQueryWrapper(questionChapter));
		return R.data(QuestionChapterWrapper.build().entityVO(detail));
	}
	/**
	 * 题目-章节关联中间表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入questionChapter")
	public R<IPage<QuestionChapterVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> questionChapter, Query query) {
		IPage<QuestionChapterEntity> pages = questionChapterService.page(Condition.getPage(query), Condition.getQueryWrapper(questionChapter, QuestionChapterEntity.class));
		return R.data(QuestionChapterWrapper.build().pageVO(pages));
	}

	/**
	 * 题目-章节关联中间表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入questionChapter")
	public R<IPage<QuestionChapterVO>> page(QuestionChapterVO questionChapter, Query query) {
		IPage<QuestionChapterVO> pages = questionChapterService.selectQuestionChapterPage(Condition.getPage(query), questionChapter);
		return R.data(pages);
	}

	/**
	 * 题目-章节关联中间表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入questionChapter")
	public R save(@Valid @RequestBody QuestionChapterEntity questionChapter) {
		return R.status(questionChapterService.save(questionChapter));
	}

	/**
	 * 题目-章节关联中间表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入questionChapter")
	public R update(@Valid @RequestBody QuestionChapterEntity questionChapter) {
		return R.status(questionChapterService.updateById(questionChapter));
	}

	/**
	 * 题目-章节关联中间表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入questionChapter")
	public R submit(@Valid @RequestBody QuestionChapterEntity questionChapter) {
		return R.status(questionChapterService.saveOrUpdate(questionChapter));
	}

	/**
	 * 题目-章节关联中间表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(questionChapterService.deleteLogic(Func.toLongList(ids)));
	}

		/**
		 * 批量设置章节题目（全量替换并维护总数）
		 */
		@PostMapping("/batch-save/{chapterId}")
		@ApiOperationSupport(order = 8)
		@Operation(summary = "批量设置章节题目", description = "传入chapterId和题目列表（questionId, orderNo）")
		public R<String> batchSave(@PathVariable Long chapterId, @RequestBody List<QuestionChapterEntity> items) {
			boolean result = questionChapterService.saveChapterItems(chapterId, items);
			return R.status(result);
		}



	/**
	 * 导出数据
	 */
	@IsAdmin
	@GetMapping("/export-questionChapter")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入questionChapter")
	public void exportQuestionChapter(@Parameter(hidden = true) @RequestParam Map<String, Object> questionChapter, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<QuestionChapterEntity> queryWrapper = Condition.getQueryWrapper(questionChapter, QuestionChapterEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(QuestionChapter::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(QuestionChapterEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<QuestionChapterExcel> list = questionChapterService.exportQuestionChapter(queryWrapper);
		ExcelUtil.export(response, "题目-章节关联中间表数据" + DateUtil.time(), "题目-章节关联中间表数据表", list, QuestionChapterExcel.class);
	}

}
