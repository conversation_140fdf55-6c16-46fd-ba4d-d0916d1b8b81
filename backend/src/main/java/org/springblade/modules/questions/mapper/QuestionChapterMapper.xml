<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.questions.mapper.QuestionChapterMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="questionChapterResultMap" type="org.springblade.modules.questions.pojo.entity.QuestionChapterEntity">
    </resultMap>


    <select id="selectQuestionChapterPage" resultMap="questionChapterResultMap">
        select *
        from exam_question_chapter
        where is_deleted = 0
    </select>


    <select id="exportQuestionChapter" resultType="org.springblade.modules.questions.excel.QuestionChapterExcel">
        SELECT *
        FROM exam_question_chapter ${ew.customSqlSegment}
    </select>

    <select id="selectQuestionIdsByChapter" resultType="java.lang.Long">
        SELECT qci.question_id
        FROM exam_question_chapter qci
        JOIN exam_subject_chapter c ON c.id = qci.chapter_id
        WHERE qci.is_deleted = 0
        AND c.is_deleted = 0
        <if test="subjectId != null">
            AND c.subject_id = #{subjectId}
        </if>
        <if test="chapterId != null">
            AND c.id = #{chapterId}
        </if>
    </select>

    <select id="selectDistinctQuestionIdsBySubject" resultType="java.lang.Long">
        SELECT DISTINCT qci.question_id
        FROM exam_question_chapter qci
        JOIN exam_subject_chapter c ON c.id = qci.chapter_id
        WHERE qci.is_deleted = 0
        AND c.is_deleted = 0
        <if test="subjectId != null">
            AND c.subject_id = #{subjectId}
        </if>
    </select>

    <select id="selectQuestionIdsBySubjectOrderByChapterOrder" resultType="java.lang.Long">
        SELECT qci.question_id
        FROM exam_question_chapter qci
        JOIN exam_subject_chapter c ON c.id = qci.chapter_id
        WHERE qci.is_deleted = 0
        AND c.is_deleted = 0
        <if test="subjectId != null">
            AND c.subject_id = #{subjectId}
        </if>
        ORDER BY c.order_no ASC, qci.question_id ASC
    </select>
</mapper>
