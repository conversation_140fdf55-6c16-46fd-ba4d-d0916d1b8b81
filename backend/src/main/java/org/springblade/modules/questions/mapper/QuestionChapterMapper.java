/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.questions.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.questions.excel.QuestionChapterExcel;
import org.springblade.modules.questions.pojo.entity.QuestionChapterEntity;
import org.springblade.modules.questions.pojo.vo.QuestionChapterVO;

import java.util.List;

/**
 * 题目-章节关联中间表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
public interface QuestionChapterMapper extends BaseMapper<QuestionChapterEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page 分页参数
	 * @param questionChapter 查询参数
	 * @return List<QuestionChapterVO>
	 */
	List<QuestionChapterVO> selectQuestionChapterPage(IPage page, QuestionChapterVO questionChapter);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询条件
	 * @return List<QuestionChapterExcel>
	 */
	List<QuestionChapterExcel> exportQuestionChapter(@Param("ew") Wrapper<QuestionChapterEntity> queryWrapper);

	/**
	 * 查询某科目下某章节的题目ID集合（过滤逻辑删除）
	 * @param subjectId
	 * @param chapterId
	 * @return
	 */
	List<Long> selectQuestionIdsByChapter(@Param("subjectId") Long subjectId, @Param("chapterId") Long chapterId);

	/**
	 * 查询某科目下所有章节的去重题目ID集合
	 * @param subjectId
	 * @return
	 */
	List<Long> selectDistinctQuestionIdsBySubject(@Param("subjectId") Long subjectId);

	/**
	 * 查询某科目下所有章节的题目ID（按章节顺序、题目ID升序）
	 * @param subjectId
	 * @return
	 */
	List<Long> selectQuestionIdsBySubjectOrderByChapterOrder(@Param("subjectId") Long subjectId);
}
