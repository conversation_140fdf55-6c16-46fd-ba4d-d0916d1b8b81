package org.springblade.modules.questions.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 题目-章节中间表 实体
 */
@Data
@TableName("exam_question_chapter")
@Schema(description = "题目-章节关联中间表")
@EqualsAndHashCode(callSuper = true)
public class QuestionChapterEntity extends TenantEntity {

    /** 题目ID（关联 exam_questions.id） */
    @Schema(description = "题目ID")
    private Long questionId;

    /** 章节ID（关联 exam_subject_chapter.id） */
    @Schema(description = "章节ID")
    private Long chapterId;

    /** 排序号（升序） */
    @Schema(description = "排序号")
    @TableField("order_no")
    private Integer orderNo;
}

