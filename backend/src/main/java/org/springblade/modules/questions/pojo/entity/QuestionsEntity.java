/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.questions.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.lang.Boolean;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 题库管理 实体类
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
@Data
@TableName("exam_questions")
@Schema(description = "Questions对象")
@EqualsAndHashCode(callSuper = true)
public class QuestionsEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 科目ID
	 */
	@Schema(description = "科目ID")
	private Long subjectId;
	/**
	 * 题型(单选,多选,判断,问答)
	 */
	@Schema(description = "题型(单选,多选,判断,问答)")
	private String type;
	/**
	 * 难度(简单,中等,困难)
	 */
	@Schema(description = "难度(简单,中等,困难)")
	private String difficulty;
	/**
	 * 题目内容(支持富文本)
	 */
	@Schema(description = "题目内容(支持富文本)")
	private String content;
	/**
	 * 选项(JSON格式)
	 */
	@Schema(description = "选项(JSON格式)")
	private String options;
	/**
	 * 答案
	 */
	@Schema(description = "答案")
	private String answer;
	/**
	 * 解析
	 */
	@Schema(description = "解析")
	private String explanation;
	/**
	 * 标签(JSON格式)
	 */
	@Schema(description = "标签(JSON格式)")
	private String tags;

	/**
	 * 权限角色ID数组(JSON格式)
	 */
	@Schema(description = "权限角色ID数组(JSON格式)")
	private String roleIds;

}
