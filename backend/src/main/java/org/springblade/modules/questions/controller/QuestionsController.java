/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.questions.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.IsAdmin;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.questions.pojo.entity.QuestionsEntity;
import org.springblade.modules.questions.pojo.vo.QuestionsVO;
import org.springblade.modules.questions.excel.QuestionsExcel;
import org.springblade.modules.questions.wrapper.QuestionsWrapper;
import org.springblade.modules.questions.service.IQuestionsService;
import org.springblade.modules.common.dto.PermissionUpdateDTO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 题库管理 控制器
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("exam-questions/questions")
@Tag(name = "题库管理", description = "题库管理接口")
public class QuestionsController extends BladeController {

	private final IQuestionsService questionsService;

	/**
	 * 题库管理 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入questions")
	public R<QuestionsVO> detail(QuestionsEntity questions) {
		QuestionsEntity detail = questionsService.getOne(Condition.getQueryWrapper(questions));
		return R.data(QuestionsWrapper.build().entityVO(detail));
	}
	/**
	 * 题库管理 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入questions")
	public R<IPage<QuestionsVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> questions, Query query) {
		IPage<QuestionsEntity> pages = questionsService.page(Condition.getPage(query), Condition.getQueryWrapper(questions, QuestionsEntity.class));
		return R.data(QuestionsWrapper.build().pageVO(pages));
	}

	/**
	 * 题库管理 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入questions")
	public R<IPage<QuestionsVO>> page(QuestionsVO questions,
	                                 @RequestParam(value = "id_in", required = false) String idIn,
	                                 Query query) {
		if (idIn != null && !idIn.isEmpty()) {
			java.util.List<Long> idList = org.springblade.core.tool.utils.Func.toLongList(idIn);
			questions.setIdList(idList);
		}
		IPage<QuestionsVO> pages = questionsService.selectQuestionsPage(Condition.getPage(query), questions);
		return R.data(pages);
	}

	/**
	 * 题库管理 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入questions")
	public R save(@Valid @RequestBody QuestionsEntity questions) {
		return R.status(questionsService.save(questions));
	}

	/**
	 * 题库管理 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入questions")
	public R update(@Valid @RequestBody QuestionsEntity questions) {
		return R.status(questionsService.updateById(questions));
	}

	/**
	 * 题库管理 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入questions")
	public R submit(@Valid @RequestBody QuestionsEntity questions) {
		return R.status(questionsService.saveOrUpdate(questions));
	}

	/**
	 * 题库管理 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(questionsService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@IsAdmin
	@GetMapping("/export-questions")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入questions")
	public void exportQuestions(@Parameter(hidden = true) @RequestParam Map<String, Object> questions, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<QuestionsEntity> queryWrapper = Condition.getQueryWrapper(questions, QuestionsEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Questions::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(QuestionsEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<QuestionsExcel> list = questionsService.exportQuestions(queryWrapper);
		ExcelUtil.export(response, "题库管理数据" + DateUtil.time(), "题库管理数据表", list, QuestionsExcel.class);
	}

	/**
	 * 更新数据权限
	 */
	@PostMapping("/update-permissions")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "更新数据权限", description = "传入权限更新参数")
	public R updatePermissions(@Valid @RequestBody PermissionUpdateDTO permissionUpdateDTO) {
		boolean result = questionsService.updatePermissions(permissionUpdateDTO);
		return R.status(result);
	}

}
