/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.questions.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springblade.modules.subjects.pojo.entity.SubjectChapterEntity;
import org.springblade.modules.subjects.service.ISubjectChapterService;


import org.springblade.modules.questions.excel.QuestionChapterExcel;
import org.springblade.modules.questions.mapper.QuestionChapterMapper;
import org.springblade.modules.questions.pojo.entity.QuestionChapterEntity;
import org.springblade.modules.questions.pojo.vo.QuestionChapterVO;
import org.springblade.modules.questions.service.IQuestionChapterService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 题目-章节关联中间表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Service
public class QuestionChapterServiceImpl extends BaseServiceImpl<QuestionChapterMapper, QuestionChapterEntity> implements IQuestionChapterService {

	@Override
	public IPage<QuestionChapterVO> selectQuestionChapterPage(IPage<QuestionChapterVO> page, QuestionChapterVO questionChapter) {
		return page.setRecords(baseMapper.selectQuestionChapterPage(page, questionChapter));
	}


	@Override
	public List<QuestionChapterExcel> exportQuestionChapter(Wrapper<QuestionChapterEntity> queryWrapper) {
		List<QuestionChapterExcel> questionChapterList = baseMapper.exportQuestionChapter(queryWrapper);
		//questionChapterList.forEach(questionChapter -> {
		//	questionChapter.setTypeName(DictCache.getValue(DictEnum.YES_NO, QuestionChapter.getType()));
		//});
		return questionChapterList;
	}

	@Override
	public List<Long> selectQuestionIdsByChapter(Long subjectId, Long chapterId) {
		return baseMapper.selectQuestionIdsByChapter(subjectId, chapterId);
	}

	@Override
	public List<Long> selectDistinctQuestionIdsBySubject(Long subjectId) {
		return baseMapper.selectDistinctQuestionIdsBySubject(subjectId);
	}

	@Override
	public List<Long> selectQuestionIdsBySubjectOrderByChapterOrder(Long subjectId) {
		return baseMapper.selectQuestionIdsBySubjectOrderByChapterOrder(subjectId);
	}

	@Override
	@org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
	public boolean saveChapterItems(Long chapterId, java.util.List<QuestionChapterEntity> items) {
		// 1) 逻辑删除旧关联
		this.update(new com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper<QuestionChapterEntity>()
				.set(QuestionChapterEntity::getIsDeleted, 1)
				.eq(QuestionChapterEntity::getChapterId, chapterId)
				.eq(QuestionChapterEntity::getIsDeleted, 0)
		);
		// 2) 批量插入新数据（过滤空列表）
		if (items != null && !items.isEmpty()) {
			for (int i = 0; i < items.size(); i++) {
				QuestionChapterEntity e = items.get(i);
				e.setChapterId(chapterId);
				if (e.getOrderNo() == null || e.getOrderNo() <= 0) {
					e.setOrderNo(i + 1);
				}
				// 逻辑未删除
				e.setIsDeleted(0);
			}
			this.saveBatch(items);
		}
		// 3) 统计数量并回写章节 totalQuestions
		long count = this.count(new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<QuestionChapterEntity>()
				.eq(QuestionChapterEntity::getChapterId, chapterId)
				.eq(QuestionChapterEntity::getIsDeleted, 0)
		);
		// 通过 Spring 上下文获取 SubjectChapterService（为了避免循环依赖，可在构造器注入）
		org.springblade.modules.subjects.service.ISubjectChapterService subjectChapterService =
			(org.springblade.modules.subjects.service.ISubjectChapterService) org.springblade.core.tool.utils.SpringUtil.getBean(org.springblade.modules.subjects.service.ISubjectChapterService.class);
		org.springblade.modules.subjects.pojo.entity.SubjectChapterEntity chapter = new org.springblade.modules.subjects.pojo.entity.SubjectChapterEntity();
		chapter.setId(chapterId);
		chapter.setTotalQuestions((int) count);
		return subjectChapterService.updateById(chapter);
	}

}
