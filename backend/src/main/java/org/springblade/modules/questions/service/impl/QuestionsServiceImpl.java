/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.questions.service.impl;

import org.springblade.modules.questions.pojo.entity.QuestionsEntity;
import org.springblade.modules.questions.pojo.vo.QuestionsVO;
import org.springblade.modules.questions.excel.QuestionsExcel;
import org.springblade.modules.questions.mapper.QuestionsMapper;
import org.springblade.modules.questions.service.IQuestionsService;
import org.springblade.modules.common.dto.PermissionUpdateDTO;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 题库管理 服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
@Service
public class QuestionsServiceImpl extends BaseServiceImpl<QuestionsMapper, QuestionsEntity> implements IQuestionsService {

	@Override
	public IPage<QuestionsVO> selectQuestionsPage(IPage<QuestionsVO> page, QuestionsVO questions) {
		// 获取当前用户信息
		BladeUser user = AuthUtil.getUser();
		String userRoleIds = user != null ? user.getRoleId() : "";

		// 管理员角色不受权限控制限制，传入空字符串跳过权限过滤
		if (AuthUtil.isAdministrator()) {
			userRoleIds = "";
		}

		return page.setRecords(baseMapper.selectQuestionsPage(page, questions, userRoleIds));
	}


	@Override
	public List<QuestionsExcel> exportQuestions(Wrapper<QuestionsEntity> queryWrapper) {
		// 获取当前用户信息
		BladeUser user = AuthUtil.getUser();
		String userRoleIds = user != null ? user.getRoleId() : "";

		// 管理员角色不受权限控制限制，传入空字符串跳过权限过滤
		if (AuthUtil.isAdministrator()) {
			userRoleIds = "";
		}

		List<QuestionsExcel> questionsList = baseMapper.exportQuestions(queryWrapper, userRoleIds);
		//questionsList.forEach(questions -> {
		//	questions.setTypeName(DictCache.getValue(DictEnum.YES_NO, Questions.getType()));
		//});
		return questionsList;
	}

	@Override
	public boolean updatePermissions(PermissionUpdateDTO permissionUpdateDTO) {
		if (permissionUpdateDTO == null || Func.isEmpty(permissionUpdateDTO.getIds())) {
			return false;
		}

		List<Long> ids = permissionUpdateDTO.getIds();
		String roleIdsStr = permissionUpdateDTO.getRoleIds() != null ?
			permissionUpdateDTO.getRoleIds().stream()
				.map(String::valueOf)
				.collect(Collectors.joining(",")) : "";

		QueryWrapper<QuestionsEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.in("id", ids);

		QuestionsEntity updateEntity = new QuestionsEntity();
		updateEntity.setRoleIds(roleIdsStr);

		return this.update(updateEntity, queryWrapper);
	}

}
