<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.subjects.mapper.SubjectsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="subjectsResultMap" type="org.springblade.modules.subjects.pojo.entity.SubjectsEntity">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="order_no" property="orderNo"/>
        <result column="role_ids" property="roleIds"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectSubjectsPage" resultMap="subjectsResultMap">
        select * from exam_subjects
        where is_deleted = 0
        <!-- 权限控制：只显示用户有权限查看的数据，管理员角色不受限制 -->
        <if test="userRoleIds != null and userRoleIds != ''">
            AND (
                role_ids IS NULL
                OR role_ids = ''
                OR role_ids REGEXP CONCAT('(^|,)(', REPLACE(#{userRoleIds}, ',', '|'), ')(,|$)')
            )
        </if>
    </select>


    <select id="exportSubjects" resultType="org.springblade.modules.subjects.excel.SubjectsExcel">
        SELECT * FROM exam_subjects
        WHERE 1=1
        <!-- 权限控制：只导出用户有权限查看的数据 -->
        <if test="userRoleIds != null and userRoleIds != ''">
            AND (
                role_ids IS NULL
                OR role_ids = ''
                OR role_ids REGEXP CONCAT('(^|,)(', REPLACE(#{userRoleIds}, ',', '|'), ')(,|$)')
            )
        </if>
        ${ew.customSqlSegment}
    </select>

</mapper>
