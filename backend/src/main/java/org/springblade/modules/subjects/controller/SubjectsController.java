/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.subjects.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.IsAdmin;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.subjects.pojo.entity.SubjectsEntity;
import org.springblade.modules.subjects.pojo.vo.SubjectsVO;
import org.springblade.modules.subjects.excel.SubjectsExcel;
import org.springblade.modules.subjects.wrapper.SubjectsWrapper;
import org.springblade.modules.subjects.service.ISubjectsService;
import org.springblade.modules.common.dto.PermissionUpdateDTO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 科目管理 控制器
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("exam-subjects/subjects")
@Tag(name = "科目管理", description = "科目管理接口")
public class SubjectsController extends BladeController {

	private final ISubjectsService subjectsService;

	/**
	 * 科目管理 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入subjects")
	public R<SubjectsVO> detail(SubjectsEntity subjects) {
		SubjectsEntity detail = subjectsService.getOne(Condition.getQueryWrapper(subjects));
		return R.data(SubjectsWrapper.build().entityVO(detail));
	}
	/**
	 * 科目管理 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入subjects")
	public R<IPage<SubjectsVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> subjects, Query query) {
		IPage<SubjectsEntity> pages = subjectsService.page(Condition.getPage(query), Condition.getQueryWrapper(subjects, SubjectsEntity.class));
		return R.data(SubjectsWrapper.build().pageVO(pages));
	}

	/**
	 * 科目管理 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入subjects")
	public R<IPage<SubjectsVO>> page(SubjectsVO subjects, Query query) {
		IPage<SubjectsVO> pages = subjectsService.selectSubjectsPage(Condition.getPage(query), subjects);
		return R.data(pages);
	}

	/**
	 * 科目管理 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入subjects")
	public R save(@Valid @RequestBody SubjectsEntity subjects) {
		return R.status(subjectsService.save(subjects));
	}

	/**
	 * 科目管理 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入subjects")
	public R update(@Valid @RequestBody SubjectsEntity subjects) {
		return R.status(subjectsService.updateById(subjects));
	}

	/**
	 * 科目管理 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入subjects")
	public R submit(@Valid @RequestBody SubjectsEntity subjects) {
		return R.status(subjectsService.saveOrUpdate(subjects));
	}

	/**
	 * 科目管理 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(subjectsService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@IsAdmin
	@GetMapping("/export-subjects")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入subjects")
	public void exportSubjects(@Parameter(hidden = true) @RequestParam Map<String, Object> subjects, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<SubjectsEntity> queryWrapper = Condition.getQueryWrapper(subjects, SubjectsEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Subjects::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(SubjectsEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<SubjectsExcel> list = subjectsService.exportSubjects(queryWrapper);
		ExcelUtil.export(response, "科目管理数据" + DateUtil.time(), "科目管理数据表", list, SubjectsExcel.class);
	}

	/**
	 * 科目管理 下拉数据源
	 */
	@GetMapping("/select-subjects-options")
	public R<List<SubjectsEntity>> selectSubjectsOptions(String name) {
		QueryWrapper<SubjectsEntity> queryWrapper = new QueryWrapper<>();
		if (StrUtil.isNotBlank(name)) {
			queryWrapper.like("name", name);
		}
		queryWrapper.eq("is_deleted", BladeConstant.DB_NOT_DELETED);

		List<SubjectsEntity> list = subjectsService.list(queryWrapper);
		return R.data(list);
	}

	/**
	 * 更新数据权限
	 */
	@PostMapping("/update-permissions")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "更新数据权限", description = "传入权限更新参数")
	public R updatePermissions(@Valid @RequestBody PermissionUpdateDTO permissionUpdateDTO) {
		boolean result = subjectsService.updatePermissions(permissionUpdateDTO);
		return R.status(result);
	}

}
