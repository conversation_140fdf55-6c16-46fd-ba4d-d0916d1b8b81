/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.subjects.service.impl;


import org.springblade.modules.subjects.excel.UserSubjectProgressExcel;
import org.springblade.modules.subjects.mapper.UserSubjectProgressMapper;
import org.springblade.modules.subjects.pojo.entity.UserSubjectProgressEntity;
import org.springblade.modules.subjects.pojo.vo.UserSubjectProgressVO;
import org.springblade.modules.subjects.service.IUserSubjectProgressService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 用户科目进度预聚合表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Service
public class UserSubjectProgressServiceImpl extends BaseServiceImpl<UserSubjectProgressMapper, UserSubjectProgressEntity> implements IUserSubjectProgressService {

	@Override
	public IPage<UserSubjectProgressVO> selectUserSubjectProgressPage(IPage<UserSubjectProgressVO> page, UserSubjectProgressVO userSubjectProgress) {
		return page.setRecords(baseMapper.selectUserSubjectProgressPage(page, userSubjectProgress));
	}


	@Override
	public List<UserSubjectProgressExcel> exportUserSubjectProgress(Wrapper<UserSubjectProgressEntity> queryWrapper) {
		List<UserSubjectProgressExcel> userSubjectProgressList = baseMapper.exportUserSubjectProgress(queryWrapper);
		//userSubjectProgressList.forEach(userSubjectProgress -> {
		//	userSubjectProgress.setTypeName(DictCache.getValue(DictEnum.YES_NO, UserSubjectProgress.getType()));
		//});
		return userSubjectProgressList;
	}

}
