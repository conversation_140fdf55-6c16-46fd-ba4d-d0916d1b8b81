/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.subjects.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.lang.Boolean;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import java.io.Serial;

/**
 * 科目管理 实体类
 *
 * <AUTHOR>
 * @since 2025-09-01
 */
@Data
@TableName("exam_subjects")
@Schema(description = "Subjects对象")
@EqualsAndHashCode(callSuper = true)
public class SubjectsEntity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 科目名称
	 */
	@Schema(description = "科目名称")
	private String name;
	/**
	 * 科目编码
	 */
	@Schema(description = "科目编码")
	private String code;
	/**
	 * 排序号
	 */
	@Schema(description = "排序号")
	private Integer orderNo;

	/**
	 * 权限角色ID数组(JSON格式)
	 */
	@Schema(description = "权限角色ID数组(JSON格式)")
	private String roleIds;

}
