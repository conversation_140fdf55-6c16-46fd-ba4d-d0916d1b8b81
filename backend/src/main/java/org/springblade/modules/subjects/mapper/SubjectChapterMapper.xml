<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.subjects.mapper.SubjectChapterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="subjectChapterResultMap" type="org.springblade.modules.subjects.pojo.entity.SubjectChapterEntity">
        <result column="id" property="id"/>
        <result column="subject_id" property="subjectId"/>
        <result column="name" property="name"/>
        <result column="order_no" property="orderNo"/>
        <result column="total_questions" property="totalQuestions"/>
        <result column="role_ids" property="roleIds"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="subjectChapterVOResultMap" type="org.springblade.modules.subjects.pojo.vo.SubjectChapterVO" extends="subjectChapterResultMap">
        <result column="subject_name" property="subjectName"/>
    </resultMap>

    <select id="selectSubjectChapterPage" resultMap="subjectChapterVOResultMap">
        select
            c.*,
            s.name as subject_name
        from exam_subject_chapter c
            left join exam_subjects s on s.id = c.subject_id and s.is_deleted = 0
        where c.is_deleted = 0
        <!-- 权限控制：只显示用户有权限查看的数据，管理员角色不受限制 -->
        <if test="userRoleIds != null and userRoleIds != ''">
            AND (
                c.role_ids IS NULL
                OR c.role_ids = ''
                OR c.role_ids REGEXP CONCAT('(^|,)(', REPLACE(#{userRoleIds}, ',', '|'), ')(,|$)')
            )
        </if>
        <if test="subjectChapter.name != null and subjectChapter.name != ''">
            and c.name like concat('%', #{subjectChapter.name}, '%')
        </if>
        <if test="subjectChapter.subjectName != null and subjectChapter.subjectName != ''">
            and s.name like concat('%', #{subjectChapter.subjectName}, '%')
        </if>
        order by c.order_no asc, c.id desc
    </select>


    <select id="exportSubjectChapter" resultType="org.springblade.modules.subjects.excel.SubjectChapterExcel">
        SELECT * FROM exam_subject_chapter
        WHERE 1=1
        <!-- 权限控制：只导出用户有权限查看的数据 -->
        <if test="userRoleIds != null and userRoleIds != ''">
            AND (
                role_ids IS NULL
                OR role_ids = ''
                OR role_ids REGEXP CONCAT('(^|,)(', REPLACE(#{userRoleIds}, ',', '|'), ')(,|$)')
            )
        </if>
        ${ew.customSqlSegment}
    </select>

</mapper>
