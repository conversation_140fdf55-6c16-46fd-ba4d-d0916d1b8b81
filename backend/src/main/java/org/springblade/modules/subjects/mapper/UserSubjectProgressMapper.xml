<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.subjects.mapper.UserSubjectProgressMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="userSubjectProgressResultMap" type="org.springblade.modules.subjects.pojo.entity.UserSubjectProgressEntity">
    </resultMap>


    <select id="selectUserSubjectProgressPage" resultMap="userSubjectProgressResultMap">
        select * from exam_user_subject_progress where is_deleted = 0
    </select>


    <select id="exportUserSubjectProgress" resultType="org.springblade.modules.subjects.excel.UserSubjectProgressExcel">
        SELECT * FROM exam_user_subject_progress ${ew.customSqlSegment}
    </select>

</mapper>
