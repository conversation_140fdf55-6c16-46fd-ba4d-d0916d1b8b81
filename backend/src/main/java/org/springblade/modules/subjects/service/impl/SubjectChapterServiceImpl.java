/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.subjects.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.transaction.annotation.Transactional;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.modules.common.dto.PermissionUpdateDTO;
import org.springblade.core.tool.utils.Func;



import org.springblade.modules.subjects.excel.SubjectChapterExcel;
import org.springblade.modules.subjects.mapper.SubjectChapterMapper;
import org.springblade.modules.subjects.pojo.entity.SubjectChapterEntity;
import org.springblade.modules.subjects.pojo.vo.SubjectChapterVO;
import org.springblade.modules.subjects.service.ISubjectChapterService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 科目章节表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Service
public class SubjectChapterServiceImpl extends BaseServiceImpl<SubjectChapterMapper, SubjectChapterEntity> implements ISubjectChapterService {

	@Override
	public IPage<SubjectChapterVO> selectSubjectChapterPage(IPage<SubjectChapterVO> page, SubjectChapterVO subjectChapter) {
		// 获取当前用户信息
		BladeUser user = AuthUtil.getUser();
		String userRoleIds = user != null ? user.getRoleId() : "";

		// 管理员角色不受权限控制限制，传入空字符串跳过权限过滤
		if (AuthUtil.isAdministrator()) {
			userRoleIds = "";
		}

		return page.setRecords(baseMapper.selectSubjectChapterPage(page, subjectChapter, userRoleIds));
	}


	@Override
	public List<SubjectChapterExcel> exportSubjectChapter(Wrapper<SubjectChapterEntity> queryWrapper) {
		// 获取当前用户信息
		BladeUser user = AuthUtil.getUser();
		String userRoleIds = user != null ? user.getRoleId() : "";

		// 管理员角色不受权限控制限制，传入空字符串跳过权限过滤
		if (AuthUtil.isAdministrator()) {
			userRoleIds = "";
		}

		List<SubjectChapterExcel> subjectChapterList = baseMapper.exportSubjectChapter(queryWrapper, userRoleIds);
		//subjectChapterList.forEach(subjectChapter -> {
		//	subjectChapter.setTypeName(DictCache.getValue(DictEnum.YES_NO, SubjectChapter.getType()));
		//});
		return subjectChapterList;
	}

	@Override
	@org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
	public boolean removeChaptersWithRelations(java.util.List<Long> ids) {
		// 1) 逻辑删除章节
		this.deleteLogic(ids);
		// 2) 联动逻辑删除章节-题目关联
		org.springblade.modules.questions.service.IQuestionChapterService questionChapterService =
			(org.springblade.modules.questions.service.IQuestionChapterService) org.springblade.core.tool.utils.SpringUtil.getBean(org.springblade.modules.questions.service.IQuestionChapterService.class);
		for (Long chapterId : ids) {
			questionChapterService.update(new com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper<org.springblade.modules.questions.pojo.entity.QuestionChapterEntity>()
					.set(org.springblade.modules.questions.pojo.entity.QuestionChapterEntity::getIsDeleted, 1)
					.eq(org.springblade.modules.questions.pojo.entity.QuestionChapterEntity::getChapterId, chapterId)
					.eq(org.springblade.modules.questions.pojo.entity.QuestionChapterEntity::getIsDeleted, 0)
			);
		}
		return true;
	}

	@Override
	public boolean updatePermissions(PermissionUpdateDTO permissionUpdateDTO) {
		if (permissionUpdateDTO == null || Func.isEmpty(permissionUpdateDTO.getIds())) {
			return false;
		}

		// 将角色ID列表转换为逗号分隔的字符串
		String roleIds = "";
		if (Func.isNotEmpty(permissionUpdateDTO.getRoleIds())) {
			roleIds = String.join(",", permissionUpdateDTO.getRoleIds().stream()
				.map(String::valueOf)
				.toArray(String[]::new));
		}

		// 批量更新权限
		LambdaUpdateWrapper<SubjectChapterEntity> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.in(SubjectChapterEntity::getId, permissionUpdateDTO.getIds())
			.set(SubjectChapterEntity::getRoleIds, roleIds);

		return this.update(updateWrapper);
	}


}
