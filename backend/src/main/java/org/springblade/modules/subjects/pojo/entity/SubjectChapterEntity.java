package org.springblade.modules.subjects.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 科目章节表 实体
 */
@Data
@TableName("exam_subject_chapter")
@Schema(description = "科目章节表")
@EqualsAndHashCode(callSuper = true)
public class SubjectChapterEntity extends TenantEntity {

    /** 科目ID（关联 subjects.id） */
    @Schema(description = "科目ID")
    private Long subjectId;

    /** 章节名称 */
    @Schema(description = "章节名称")
    private String name;

    /** 排序号（升序） */
    @Schema(description = "排序号")
    private Integer orderNo;

    /** 章节题目总数快照（便于加速展示） */
    @Schema(description = "章节题目总数快照")
    private Integer totalQuestions;

    /** 权限角色ID数组(逗号分隔字符串格式) */
    @Schema(description = "权限角色ID数组(逗号分隔字符串格式)")
    private String roleIds;
}

