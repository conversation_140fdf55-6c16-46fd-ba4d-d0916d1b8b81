/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.subjects.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.IsAdmin;
import org.springblade.modules.common.dto.PermissionUpdateDTO;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.subjects.excel.SubjectChapterExcel;
import org.springblade.modules.subjects.pojo.entity.SubjectChapterEntity;
import org.springblade.modules.subjects.pojo.vo.SubjectChapterVO;
import org.springblade.modules.subjects.service.ISubjectChapterService;
import org.springblade.modules.subjects.wrapper.SubjectChapterWrapper;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.validation.Valid;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 科目章节表 控制器
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-subjectchapter/subjectChapter")
@Tag(name = "科目章节表", description = "科目章节表接口")
public class SubjectChapterController extends BladeController {

	private final ISubjectChapterService subjectChapterService;

	/**
	 * 科目章节表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入subjectChapter")
	public R<SubjectChapterVO> detail(SubjectChapterEntity subjectChapter) {
		SubjectChapterEntity detail = subjectChapterService.getOne(Condition.getQueryWrapper(subjectChapter));
		return R.data(SubjectChapterWrapper.build().entityVO(detail));
	}
	/**
	 * 科目章节表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入subjectChapter")
	public R<IPage<SubjectChapterVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> subjectChapter, Query query) {
		IPage<SubjectChapterEntity> pages = subjectChapterService.page(Condition.getPage(query), Condition.getQueryWrapper(subjectChapter, SubjectChapterEntity.class));
		return R.data(SubjectChapterWrapper.build().pageVO(pages));
	}

	/**
	 * 科目章节表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入subjectChapter")
	public R<IPage<SubjectChapterVO>> page(SubjectChapterVO subjectChapter, Query query) {
		IPage<SubjectChapterVO> pages = subjectChapterService.selectSubjectChapterPage(Condition.getPage(query), subjectChapter);
		return R.data(pages);
	}

	/**
	 * 科目章节表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入subjectChapter")
	public R save(@Valid @RequestBody SubjectChapterEntity subjectChapter) {
		return R.status(subjectChapterService.save(subjectChapter));
	}

	/**
	 * 科目章节表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入subjectChapter")
	public R update(@Valid @RequestBody SubjectChapterEntity subjectChapter) {
		return R.status(subjectChapterService.updateById(subjectChapter));
	}

	/**
	 * 科目章节表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入subjectChapter")
	public R submit(@Valid @RequestBody SubjectChapterEntity subjectChapter) {
		return R.status(subjectChapterService.saveOrUpdate(subjectChapter));
	}

	/**
	 * 科目章节表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(subjectChapterService.removeChaptersWithRelations(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@IsAdmin
	@GetMapping("/export-subjectChapter")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入subjectChapter")
	public void exportSubjectChapter(@Parameter(hidden = true) @RequestParam Map<String, Object> subjectChapter, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<SubjectChapterEntity> queryWrapper = Condition.getQueryWrapper(subjectChapter, SubjectChapterEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(SubjectChapter::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(SubjectChapterEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<SubjectChapterExcel> list = subjectChapterService.exportSubjectChapter(queryWrapper);
		ExcelUtil.export(response, "科目章节表数据" + DateUtil.time(), "科目章节表数据表", list, SubjectChapterExcel.class);
	}

	/**
	 * 更新数据权限
	 */
	@PostMapping("/update-permissions")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "更新数据权限", description = "传入权限更新参数")
	public R updatePermissions(@Valid @RequestBody PermissionUpdateDTO permissionUpdateDTO) {
		boolean result = subjectChapterService.updatePermissions(permissionUpdateDTO);
		return R.status(result);
	}

}
