/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.subjects.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.IsAdmin;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.subjects.excel.UserSubjectProgressExcel;
import org.springblade.modules.subjects.pojo.entity.UserSubjectProgressEntity;
import org.springblade.modules.subjects.pojo.vo.UserSubjectProgressVO;
import org.springblade.modules.subjects.service.IUserSubjectProgressService;
import org.springblade.modules.subjects.wrapper.UserSubjectProgressWrapper;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 用户科目进度预聚合表 控制器
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-usersubjectprogress/userSubjectProgress")
@Tag(name = "用户科目进度预聚合表", description = "用户科目进度预聚合表接口")
public class UserSubjectProgressController extends BladeController {

	private final IUserSubjectProgressService userSubjectProgressService;

	/**
	 * 用户科目进度预聚合表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入userSubjectProgress")
	public R<UserSubjectProgressVO> detail(UserSubjectProgressEntity userSubjectProgress) {
		UserSubjectProgressEntity detail = userSubjectProgressService.getOne(Condition.getQueryWrapper(userSubjectProgress));
		return R.data(UserSubjectProgressWrapper.build().entityVO(detail));
	}
	/**
	 * 用户科目进度预聚合表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入userSubjectProgress")
	public R<IPage<UserSubjectProgressVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> userSubjectProgress, Query query) {
		IPage<UserSubjectProgressEntity> pages = userSubjectProgressService.page(Condition.getPage(query), Condition.getQueryWrapper(userSubjectProgress, UserSubjectProgressEntity.class));
		return R.data(UserSubjectProgressWrapper.build().pageVO(pages));
	}

	/**
	 * 用户科目进度预聚合表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入userSubjectProgress")
	public R<IPage<UserSubjectProgressVO>> page(UserSubjectProgressVO userSubjectProgress, Query query) {
		IPage<UserSubjectProgressVO> pages = userSubjectProgressService.selectUserSubjectProgressPage(Condition.getPage(query), userSubjectProgress);
		return R.data(pages);
	}

	/**
	 * 用户科目进度预聚合表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入userSubjectProgress")
	public R save(@Valid @RequestBody UserSubjectProgressEntity userSubjectProgress) {
		return R.status(userSubjectProgressService.save(userSubjectProgress));
	}

	/**
	 * 用户科目进度预聚合表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入userSubjectProgress")
	public R update(@Valid @RequestBody UserSubjectProgressEntity userSubjectProgress) {
		return R.status(userSubjectProgressService.updateById(userSubjectProgress));
	}

	/**
	 * 用户科目进度预聚合表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入userSubjectProgress")
	public R submit(@Valid @RequestBody UserSubjectProgressEntity userSubjectProgress) {
		return R.status(userSubjectProgressService.saveOrUpdate(userSubjectProgress));
	}

	/**
	 * 用户科目进度预聚合表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(userSubjectProgressService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@IsAdmin
	@GetMapping("/export-userSubjectProgress")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入userSubjectProgress")
	public void exportUserSubjectProgress(@Parameter(hidden = true) @RequestParam Map<String, Object> userSubjectProgress, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<UserSubjectProgressEntity> queryWrapper = Condition.getQueryWrapper(userSubjectProgress, UserSubjectProgressEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(UserSubjectProgress::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(UserSubjectProgressEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<UserSubjectProgressExcel> list = userSubjectProgressService.exportUserSubjectProgress(queryWrapper);
		ExcelUtil.export(response, "用户科目进度预聚合表数据" + DateUtil.time(), "用户科目进度预聚合表数据表", list, UserSubjectProgressExcel.class);
	}

}
