package org.springblade.modules.resource.rule.constant;

/**
 * OssRuleConstant
 *
 * <AUTHOR>
 */
public interface OssRuleConstant {
	/**
	 * OSS规则链ID
	 */
	String OSS_CHAIN_ID = "ossChain";

	/**
	 * 预处理OSS规则
	 */
	String PRE_OSS_RULE = "preOssRule";

	/**
	 * OSS缓存判断规则
	 */
	String OSS_CACHE_RULE = "ossCacheRule";

	/**
	 * OSS读取规则
	 */
	String OSS_READ_RULE = "ossReadRule";

	/**
	 * OSS新建规则ID
	 */
	String OSS_NEW_RULE = "ossNewRule";

	/**
	 * OSS数据规则
	 */
	String OSS_DATA_RULE = "ossDataRule";

	/**
	 * OSS构建条件判断规则
	 */
	String OSS_BUILD_RULE = "ossBuildRule";

	/**
	 * 阿里云OSS规则
	 */
	String ALI_OSS_RULE = "aliOssRule";

	/**
	 * 亚马逊S3规则
	 */
	String AMAZON_S3_RULE = "amazonS3Rule";

	/**
	 * 华为云OBS规则
	 */
	String HUAWEI_OBS_RULE = "huaweiObsRule";

	/**
	 * MinIO规则
	 */
	String MINIO_RULE = "minioRule";

	/**
	 * 七牛云OSS规则
	 */
	String QINIU_OSS_RULE = "qiniuOssRule";

	/**
	 * 腾讯云COS规则
	 */
	String TENCENT_COS_RULE = "tencentCosRule";

	/**
	 * 本地文件规则
	 */
	String LOCAL_FILE_RULE = "localFileRule";

	/**
	 * OSS模板规则
	 */
	String OSS_TEMPLATE_RULE = "ossTemplateRule";

	/**
	 * 最终OSS规则
	 */
	String FINALLY_OSS_RULE = "finallyOssRule";
}
