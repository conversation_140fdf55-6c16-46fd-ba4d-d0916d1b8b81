<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.resource.mapper.SmsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="smsResultMap" type="org.springblade.modules.resource.pojo.entity.Sms">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="sms_code" property="smsCode"/>
        <result column="template_id" property="templateId"/>
        <result column="category" property="category"/>
        <result column="access_key" property="accessKey"/>
        <result column="secret_key" property="secretKey"/>
        <result column="region_id" property="regionId"/>
        <result column="sign_name" property="signName"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <select id="selectSmsPage" resultMap="smsResultMap">
        select * from blade_sms where is_deleted = 0
    </select>

</mapper>
