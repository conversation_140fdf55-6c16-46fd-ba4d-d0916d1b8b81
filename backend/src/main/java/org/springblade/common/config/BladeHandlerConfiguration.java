/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */

package org.springblade.common.config;

import lombok.AllArgsConstructor;
import org.springblade.common.handler.BladePermissionHandler;
import org.springblade.common.handler.BladeRecordHandler;
import org.springblade.common.handler.BladeScopeModelHandler;
import org.springblade.core.datarecord.processor.DataRecordHandler;
import org.springblade.core.datascope.handler.ScopeModelHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * Blade处理器自动配置
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@AllArgsConstructor
public class BladeHandlerConfiguration {

	private final JdbcTemplate jdbcTemplate;

	@Bean
	public DataRecordHandler dataRecordHandler() {
		return new BladeRecordHandler(jdbcTemplate);
	}

	@Bean
	public ScopeModelHandler scopeModelHandler() {
		return new BladeScopeModelHandler(jdbcTemplate);
	}

	@Bean
	public BladePermissionHandler permissionHandler() {
		return new BladePermissionHandler(jdbcTemplate);
	}

}
