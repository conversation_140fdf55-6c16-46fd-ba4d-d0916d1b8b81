#数据源配置
spring:
  data:
    redis:
      ##redis 单机环境配置
      host: server.lan
      port: 6379
      password:
      database: 3
      ssl:
        enabled: false
      ##redis 集群环境配置
      #cluster:
      #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
      #  commandTimeout: 5000
  datasource:
    # MySql
    url: ******************************************************************************************************************************************************************************************************************************************************************************
    username: mlops
    password: Yueshutech@20240210

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:2888

#oss默认配置
oss:
  #开启oss配置
  enabled: true
  #开启oss类型
  #minio、s3、qiniu、alioss、huaweiobs、tencentcos
  name: minio
  #租户模式
  tenant-mode: true
  #oss服务地址
  endpoint: http://server.lan:9000
  #oss转换服务地址，用于内网上传后将返回地址改为转换的外网地址
  transform-endpoint: http://server.lan:9000
  #访问key
  access-key: yanjiang
  #密钥key
  secret-key: yanjiang
  #存储桶
  bucket-name: yanjiang-exam

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: false
    ##redis服务地址
    address: redis://127.0.0.1:6379
