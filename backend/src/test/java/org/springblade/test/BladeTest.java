package org.springblade.test;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springblade.core.test.BladeBootTest;
import org.springblade.core.test.BladeSpringExtension;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Blade单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(BladeSpringExtension.class)
@BladeBootTest(appName = "blade-runner", enableLoader = true)
public class BladeTest {


}
