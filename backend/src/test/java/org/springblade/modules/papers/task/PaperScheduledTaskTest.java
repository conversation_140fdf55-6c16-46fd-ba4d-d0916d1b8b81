package org.springblade.modules.papers.task;

import org.junit.jupiter.api.Test;
import org.springblade.modules.papers.service.IPapersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 试卷定时任务测试类
 *
 * <AUTHOR>
 * @since 2025-09-04
 */
@SpringBootTest
@ActiveProfiles("test")
public class PaperScheduledTaskTest {

	@Autowired
	private IPapersService papersService;

	@Autowired
	private PaperScheduledTask paperScheduledTask;

	/**
	 * 测试自动发布试卷功能
	 */
	@Test
	public void testAutoPublishPapers() {
		// 直接调用服务方法测试
		int publishedCount = papersService.autoPublishPapers();
		System.out.println("自动发布试卷数量: " + publishedCount);
	}

	/**
	 * 测试定时任务方法
	 */
	@Test
	public void testScheduledTask() {
		// 直接调用定时任务方法测试
		paperScheduledTask.autoPublishPapers();
	}

}
