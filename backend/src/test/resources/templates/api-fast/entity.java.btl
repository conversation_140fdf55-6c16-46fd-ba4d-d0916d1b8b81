/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package ${package.Entity!};

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
#for(x in table.importPackages){
#if(isNotEmpty(x)){
#if(hasSuperEntity&&!strutil.contain(x,"Serializable")){
import ${x!};
#}
#if(!hasSuperEntity&&!strutil.contain(x,"TenantEntity")){
import ${x!};
#}
#}
#}
#if(hasSuperEntity){
import lombok.EqualsAndHashCode;
#}else{
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
#}
import java.io.Serial;

/**
 * ${table.comment!} 实体类
 *
 * <AUTHOR>
 * @since ${date!}
 */
@Data
@TableName("${table.name!}")
@Schema(description = "${entity!}对象")
#if(hasSuperEntity){
@EqualsAndHashCode(callSuper = true)
public class ${entityKey!}Entity extends TenantEntity {

	@Serial
	private static final long serialVersionUID = 1L;
#}else{
public class ${entityKey!}Entity implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "主键")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
#}

#for(x in table.fields) {
	#if(hasSuperEntity){
	#if(x.propertyName!="id"&&x.propertyName!="createUser"&&x.propertyName!="createDept"&&x.propertyName!="createTime"&&x.propertyName!="updateUser"&&x.propertyName!="updateTime"&&x.propertyName!="status"&&x.propertyName!="isDeleted"&&x.propertyName!="tenantId"){
	/**
	 * ${x.comment!}
	 */
	@Schema(description = "${x.comment!}")
	private ${x.propertyType!} ${x.propertyName!};
	#}
	#}else{
	#if(x.propertyName!="id"){
	/**
	 * ${x.comment!}
	 */
	@Schema(description = "${x.comment!}")
	private ${x.propertyType!} ${x.propertyName!};
	#}
	#}
#}

}
