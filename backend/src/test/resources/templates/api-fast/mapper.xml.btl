<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${package.Mapper!}.${table.mapperName!}">

#if(enableCache){
    <!-- 开启二级缓存 -->
    <cache type="org.mybatis.caches.ehcache.LoggingEhcache"/>
#}
    <!-- 通用查询映射结果 -->
    <resultMap id="${entityKeyPath!}ResultMap" type="${package.Entity!}.${entityKey!}Entity">
        #for(x in table.fields) {
        <result column="${x.name!}" property="${x.propertyName!}"/>
        #}
    </resultMap>

    <select id="select${entityKey!}Page" resultMap="${entityKeyPath!}ResultMap">
        select * from ${table.name} where is_deleted = 0
    </select>

    <select id="export${entityKey!}" resultType="${packageName!}.excel.${entityKey!}Excel">
        SELECT * FROM ${table.name!} \${ew.customSqlSegment}
    </select>

</mapper>
