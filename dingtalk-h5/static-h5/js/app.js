// 应用主控制器
class App {
    constructor() {
        this.currentPage = 'home';
        this.pages = ['home', 'practice', 'exam', 'profile'];
        this.currentQuestionIndex = 0;
        this.practiceQuestions = [];
        this.userAnswers = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.showPage('home');
        this.updateNavBar('home');
    }

    bindEvents() {
        // 底部导航栏点击事件
        const tabbarItems = document.querySelectorAll('.tabbar-item');
        tabbarItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const tab = e.currentTarget.dataset.tab;
                this.switchTab(tab);
            });
        });

        // 返回按钮事件
        const navBack = document.getElementById('navBack');
        if (navBack) {
            navBack.addEventListener('click', () => {
                this.goBack();
            });
        }

        // 首页快速操作事件
        const actionItems = document.querySelectorAll('.action-item');
        actionItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.handleQuickAction(action);
            });
        });

        // 学习进度点击事件
        const progressItems = document.querySelectorAll('.progress-item');
        progressItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const subjectId = e.currentTarget.dataset.subject;
                this.goToPracticeDetail(subjectId);
            });
        });

        // 推荐卡片点击事件
        const recommendCards = document.querySelectorAll('.recommend-card');
        recommendCards.forEach(card => {
            card.addEventListener('click', (e) => {
                this.goToPracticeDetail('1');
            });
        });

        // 练习页面科目点击事件
        const subjectItems = document.querySelectorAll('.subject-item');
        subjectItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const subjectId = e.currentTarget.dataset.subject;
                this.goToPracticeDetail(subjectId);
            });
        });

        // 考试项目点击事件
        const examItems = document.querySelectorAll('.exam-item');
        examItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const examId = e.currentTarget.dataset.exam;
                this.goToExamDetail(examId);
            });
        });

        // 个人中心菜单点击事件
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.handleMenuAction(action);
            });
        });
    }

    switchTab(tab) {
        // 更新底部导航栏状态
        const tabbarItems = document.querySelectorAll('.tabbar-item');
        tabbarItems.forEach(item => {
            item.classList.remove('active');
            if (item.dataset.tab === tab) {
                item.classList.add('active');
            }
        });

        // 显示对应页面
        this.showPage(tab);
        this.updateNavBar(tab);
    }

    showPage(pageId) {
        // 隐藏所有页面
        const pages = document.querySelectorAll('.page');
        pages.forEach(page => {
            page.style.display = 'none';
        });

        // 显示目标页面
        const targetPage = document.getElementById(this.getPageElementId(pageId));
        if (targetPage) {
            targetPage.style.display = 'block';
            this.currentPage = pageId;
        }
    }

    getPageElementId(pageId) {
        const pageMap = {
            'home': 'homePage',
            'practice': 'practicePage',
            'exam': 'examPage',
            'profile': 'profilePage',
            'practiceDetail': 'practiceDetailPage',
            'examDetail': 'examDetailPage',
            'practiceQuestion': 'practiceQuestionPage',
            'examQuestion': 'examQuestionPage'
        };
        return pageMap[pageId] || 'homePage';
    }

    updateNavBar(pageId) {
        const navTitle = document.getElementById('navTitle');
        const navBack = document.getElementById('navBack');
        const tabbar = document.getElementById('tabbar');

        const pageTitles = {
            home: '考试刷题系统',
            practice: '练习',
            exam: '考试',
            profile: '个人中心',
            practiceDetail: '练习详情',
            examDetail: '考试详情',
            practiceQuestion: '连续练习',
            examQuestion: '考试进行中'
        };

        if (navTitle) navTitle.textContent = pageTitles[pageId] || '考试刷题系统';
        
        // 主页面显示底部导航栏，隐藏返回按钮
        if (this.pages.includes(pageId)) {
            if (navBack) navBack.style.display = 'none';
            if (tabbar) tabbar.style.display = 'flex';
        } else {
            if (navBack) navBack.style.display = 'block';
            if (tabbar) tabbar.style.display = 'none';
        }
    }

    goBack() {
        // 简单的返回逻辑，回到首页
        this.switchTab('home');
    }

    handleQuickAction(action) {
        switch (action) {
            case 'practice':
                this.switchTab('practice');
                break;
            case 'exam':
                this.switchTab('exam');
                break;
            case 'wrong':
                this.showWrongQuestions();
                break;
            case 'report':
                this.showReport();
                break;
        }
    }

    goToPracticeDetail(subjectId) {
        // 跳转到练习详情页面
        this.showPage('practiceDetail');
        this.updateNavBar('practiceDetail');
        this.bindPracticeDetailEvents();
    }

    goToExamDetail(examId) {
        // 跳转到考试详情页面
        this.showPage('examDetail');
        this.updateNavBar('examDetail');
        this.bindExamDetailEvents();
    }

    bindPracticeDetailEvents() {
        // 练习模式点击事件
        const modeItems = document.querySelectorAll('.mode-item');
        modeItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const mode = e.currentTarget.dataset.mode;
                this.startPractice(mode);
            });
        });

        // 章节点击事件
        const chapterItems = document.querySelectorAll('.chapter-item');
        chapterItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const chapter = e.currentTarget.dataset.chapter;
                this.startChapterPractice(chapter);
            });
        });
    }

    bindExamDetailEvents() {
        // 开始考试按钮事件
        const startExamBtn = document.getElementById('startExamBtn');
        if (startExamBtn) {
            startExamBtn.addEventListener('click', () => {
                this.startExam();
            });
        }
    }

    startPractice(mode) {
        const modeNames = {
            sequence: '顺序练习',
            random: '随机练习',
            wrong: '错题练习'
        };
        this.showNotification(`开始${modeNames[mode]}...`);
        
        // 跳转到做题页面
        setTimeout(() => {
            this.showPage('practiceQuestion');
            this.updateNavBar('practiceQuestion');
            this.initPracticeQuestion();
        }, 1000);
    }

    startChapterPractice(chapter) {
        this.showNotification(`开始第${chapter}章练习...`);
        
        // 跳转到做题页面
        setTimeout(() => {
            this.showPage('practiceQuestion');
            this.updateNavBar('practiceQuestion');
            this.initPracticeQuestion();
        }, 1000);
    }

    startExam() {
        this.showNotification('正在进入考试...', 'info', 2000);
        
        // 跳转到考试页面
        setTimeout(() => {
            this.showPage('examQuestion');
            this.updateNavBar('examQuestion');
            this.initExamQuestion();
        }, 2000);
    }

    handleMenuAction(action) {
        switch (action) {
            case 'history':
                this.showHistory();
                break;
            case 'wrong':
                this.showWrongQuestions();
                break;
            case 'feedback':
                this.showFeedback();
                break;
            case 'settings':
                this.showSettings();
                break;
        }
    }

    showHistory() {
        this.showNotification('历史记录功能开发中...');
    }

    showWrongQuestions() {
        this.showNotification('错题本功能开发中...');
    }

    showFeedback() {
        this.showNotification('意见反馈功能开发中...');
    }

    showSettings() {
        this.showNotification('设置功能开发中...');
    }

    showReport() {
        this.showNotification('学习报告功能开发中...');
    }

    showNotification(message, type = 'info', duration = 3000) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // 添加样式
        Object.assign(notification.style, {
            position: 'fixed',
            top: '70px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: type === 'success' ? '#52c41a' : 
                       type === 'error' ? '#ff4d4f' : 
                       type === 'warning' ? '#fa8c16' : '#1989fa',
            color: 'white',
            padding: '12px 20px',
            borderRadius: '6px',
            fontSize: '14px',
            zIndex: '9999',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            opacity: '0',
            transition: 'all 0.3s ease'
        });

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(-50%) translateY(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(-50%) translateY(-10px)';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, duration);
    }

    // 练习做题功能
    initPracticeQuestion() {
        this.currentQuestionIndex = 0;
        this.practiceQuestions = this.loadPracticeQuestions();
        this.userAnswers = [];
        this.startTime = Date.now();

        this.bindPracticeQuestionEvents();
        this.displayCurrentQuestion();
        this.startTimer();
    }

    bindPracticeQuestionEvents() {
        // 提交答案按钮
        const submitBtn = document.getElementById('submitAnswerBtn');
        if (submitBtn) {
            submitBtn.addEventListener('click', () => {
                this.submitAnswer();
            });
        }

        // 下一题按钮
        const nextBtn = document.getElementById('nextQuestionBtn');
        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                this.nextQuestion();
            });
        }

        // 上一题按钮
        const prevBtn = document.getElementById('prevQuestionBtn');
        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                this.prevQuestion();
            });
        }
    }

    selectOption(optionElement) {
        // 清除其他选项的选中状态
        const options = document.querySelectorAll('#questionOptions .option-item');
        options.forEach(opt => opt.classList.remove('selected'));

        // 选中当前选项
        optionElement.classList.add('selected');

        // 启用提交按钮
        const submitBtn = document.getElementById('submitAnswerBtn');
        if (submitBtn) {
            submitBtn.disabled = false;
        }
    }

    submitAnswer() {
        const selectedOption = document.querySelector('#questionOptions .option-item.selected');
        if (!selectedOption) return;

        const userAnswer = selectedOption.dataset.value;
        const currentQuestion = this.practiceQuestions[this.currentQuestionIndex];
        const isCorrect = userAnswer === currentQuestion.correctAnswer;

        // 保存用户答案
        this.userAnswers[this.currentQuestionIndex] = {
            answer: userAnswer,
            correct: isCorrect,
            timeSpent: Date.now() - this.questionStartTime
        };

        // 显示答案状态
        this.showAnswerResult(selectedOption, currentQuestion, isCorrect);

        // 显示AI解析
        this.showAIAnalysis(currentQuestion, userAnswer, isCorrect);

        // 更新按钮状态
        const submitBtn = document.getElementById('submitAnswerBtn');
        const nextBtn = document.getElementById('nextQuestionBtn');
        if (submitBtn) submitBtn.style.display = 'none';
        if (nextBtn) nextBtn.style.display = 'block';
    }

    showAnswerResult(selectedOption, question, isCorrect) {
        const options = document.querySelectorAll('#questionOptions .option-item');

        // 标记正确答案
        options.forEach(option => {
            if (option.dataset.value === question.correctAnswer) {
                option.classList.add('correct');
            }
        });

        // 如果选错了，标记错误答案
        if (!isCorrect) {
            selectedOption.classList.add('wrong');
        }
    }

    showAIAnalysis(question, userAnswer, isCorrect) {
        const analysisPanel = document.getElementById('aiAnalysis');
        if (!analysisPanel) return;

        const resultElement = document.getElementById('analysisResult');
        const explanationElement = document.getElementById('answerExplanation');
        const knowledgeElement = document.getElementById('knowledgePoints');
        const errorSection = document.getElementById('errorAnalysisSection');
        const errorAnalysis = document.getElementById('errorAnalysis');

        // 设置结果状态
        if (resultElement) {
            resultElement.className = `analysis-result ${isCorrect ? 'correct' : 'wrong'}`;
            resultElement.innerHTML = `
                <span class="result-icon">${isCorrect ? '✅' : '❌'}</span>
                <span class="result-text">${isCorrect ? '回答正确' : '回答错误'}</span>
            `;
        }

        // 设置解析内容
        if (explanationElement) {
            explanationElement.textContent = question.explanation;
        }

        // 设置知识点
        if (knowledgeElement) {
            knowledgeElement.innerHTML = question.knowledgePoints
                .map(point => `<div class="knowledge-tag">${point}</div>`)
                .join('');
        }

        // 如果答错了，显示错误分析
        if (!isCorrect && errorSection && errorAnalysis) {
            errorSection.style.display = 'block';
            errorAnalysis.textContent = this.generateErrorAnalysis(question, userAnswer);
        } else if (errorSection) {
            errorSection.style.display = 'none';
        }

        // 显示解析面板
        analysisPanel.style.display = 'block';

        // 滚动到解析面板
        setTimeout(() => {
            analysisPanel.scrollIntoView({ behavior: 'smooth' });
        }, 300);
    }

    generateErrorAnalysis(question, userAnswer) {
        const wrongOption = question.options.find(opt => opt.value === userAnswer);
        return `您选择了"${wrongOption.text}"，这是错误的。${question.errorAnalysis || '请仔细阅读题目和选项，理解正确答案的含义。'}`;
    }

    nextQuestion() {
        if (this.currentQuestionIndex < this.practiceQuestions.length - 1) {
            this.currentQuestionIndex++;
            this.displayCurrentQuestion();
            this.resetQuestionState();
        } else {
            this.completePractice();
        }
    }

    prevQuestion() {
        if (this.currentQuestionIndex > 0) {
            this.currentQuestionIndex--;
            this.displayCurrentQuestion();
            this.resetQuestionState();
        }
    }

    resetQuestionState() {
        // 重置选项状态
        const options = document.querySelectorAll('#questionOptions .option-item');
        options.forEach(opt => {
            opt.classList.remove('selected', 'correct', 'wrong');
        });

        // 重置按钮状态
        const submitBtn = document.getElementById('submitAnswerBtn');
        const nextBtn = document.getElementById('nextQuestionBtn');
        if (submitBtn) {
            submitBtn.style.display = 'block';
            submitBtn.disabled = true;
        }
        if (nextBtn) {
            nextBtn.style.display = 'none';
        }

        // 隐藏AI解析
        const analysisPanel = document.getElementById('aiAnalysis');
        if (analysisPanel) {
            analysisPanel.style.display = 'none';
        }

        // 重置计时
        this.questionStartTime = Date.now();
    }

    displayCurrentQuestion() {
        const question = this.practiceQuestions[this.currentQuestionIndex];

        // 更新题目信息
        const currentQuestionNum = document.getElementById('currentQuestionNum');
        const totalQuestions = document.getElementById('totalQuestions');
        const questionType = document.getElementById('questionType');
        const questionText = document.getElementById('questionText');

        if (currentQuestionNum) currentQuestionNum.textContent = this.currentQuestionIndex + 1;
        if (totalQuestions) totalQuestions.textContent = this.practiceQuestions.length;
        if (questionType) questionType.textContent = question.type;
        if (questionText) questionText.textContent = question.question;

        // 更新进度条
        const progress = ((this.currentQuestionIndex + 1) / this.practiceQuestions.length) * 100;
        const questionProgress = document.getElementById('questionProgress');
        if (questionProgress) {
            questionProgress.style.width = `${progress}%`;
        }

        // 更新选项
        const optionsContainer = document.getElementById('questionOptions');
        if (optionsContainer) {
            optionsContainer.innerHTML = question.options.map(option => `
                <div class="option-item" data-value="${option.value}">
                    <div class="option-label">${option.value}</div>
                    <div class="option-text">${option.text}</div>
                </div>
            `).join('');

            // 重新绑定选项事件
            const options = optionsContainer.querySelectorAll('.option-item');
            options.forEach(option => {
                option.addEventListener('click', (e) => {
                    this.selectOption(e.currentTarget);
                });
            });
        }

        // 更新按钮状态
        const prevBtn = document.getElementById('prevQuestionBtn');
        if (prevBtn) {
            prevBtn.disabled = this.currentQuestionIndex === 0;
        }

        this.questionStartTime = Date.now();
    }

    startTimer() {
        this.timerInterval = setInterval(() => {
            const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            const timerElement = document.getElementById('questionTimer');
            if (timerElement) {
                timerElement.textContent =
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }, 1000);
    }

    completePractice() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }
        const totalTime = Date.now() - this.startTime;
        const correctCount = this.userAnswers.filter(answer => answer.correct).length;
        const accuracy = Math.round((correctCount / this.practiceQuestions.length) * 100);

        this.showNotification(`练习完成！正确率：${accuracy}%`, 'success', 5000);

        // 返回首页
        setTimeout(() => {
            this.switchTab('home');
        }, 2000);
    }

    // 考试功能
    initExamQuestion() {
        this.currentExamQuestionIndex = 0;
        this.examQuestions = this.loadExamQuestions();
        this.examAnswers = new Array(this.examQuestions.length).fill(null);
        this.examStartTime = Date.now();
        this.examDuration = 120 * 60 * 1000; // 120分钟

        this.bindExamQuestionEvents();
        this.displayCurrentExamQuestion();
        this.startExamTimer();
        this.generateQuestionGrid();
    }

    // 模拟数据
    loadPracticeQuestions() {
        return [
            {
                id: 1,
                type: '单选题',
                question: '中华人民共和国成立于哪一年？',
                options: [
                    { value: 'A', text: '1948年' },
                    { value: 'B', text: '1949年' },
                    { value: 'C', text: '1950年' },
                    { value: 'D', text: '1951年' }
                ],
                correctAnswer: 'B',
                explanation: '中华人民共和国成立于1949年10月1日，这是中国历史上的重要时刻，标志着新中国的诞生。',
                knowledgePoints: ['中国近现代史', '新中国成立', '重要历史事件'],
                errorAnalysis: '1948年是中华人民共和国成立的前一年，当时正值解放战争的关键时期。'
            },
            {
                id: 2,
                type: '单选题',
                question: '以下哪项不属于可再生能源？',
                options: [
                    { value: 'A', text: '太阳能' },
                    { value: 'B', text: '风能' },
                    { value: 'C', text: '煤炭' },
                    { value: 'D', text: '水能' }
                ],
                correctAnswer: 'C',
                explanation: '煤炭是化石燃料，属于不可再生能源。一旦开采使用后就无法在短时间内再生。',
                knowledgePoints: ['环境科学', '能源分类', '可持续发展'],
                errorAnalysis: '可再生能源是指在自然界中可以持续再生的能源。'
            },
            {
                id: 3,
                type: '判断题',
                question: '人工智能是计算机科学的一个分支。',
                options: [
                    { value: 'A', text: '正确' },
                    { value: 'B', text: '错误' }
                ],
                correctAnswer: 'A',
                explanation: '人工智能确实是计算机科学的一个重要分支，研究如何让机器模拟人类智能。',
                knowledgePoints: ['计算机科学', '人工智能', '技术发展'],
                errorAnalysis: '人工智能涉及机器学习、深度学习等多个技术领域。'
            }
        ];
    }

    bindExamQuestionEvents() {
        // 导航按钮
        const prevBtn = document.getElementById('examPrevBtn');
        const nextBtn = document.getElementById('examNextBtn');
        const submitBtn = document.getElementById('examSubmitBtn');

        if (prevBtn) {
            prevBtn.addEventListener('click', () => this.prevExamQuestion());
        }
        if (nextBtn) {
            nextBtn.addEventListener('click', () => this.nextExamQuestion());
        }
        if (submitBtn) {
            submitBtn.addEventListener('click', () => this.submitExam());
        }
    }

    selectExamOption(optionElement) {
        const options = document.querySelectorAll('#examQuestionOptions .option-item');
        options.forEach(opt => opt.classList.remove('selected'));

        optionElement.classList.add('selected');

        // 保存答案
        this.examAnswers[this.currentExamQuestionIndex] = optionElement.dataset.value;

        // 更新统计
        this.updateExamStats();

        // 更新题目导航状态
        this.updateQuestionNavStatus();
    }

    displayCurrentExamQuestion() {
        const question = this.examQuestions[this.currentExamQuestionIndex];

        const examQuestionNum = document.getElementById('examQuestionNum');
        const examQuestionType = document.getElementById('examQuestionType');
        const examQuestionText = document.getElementById('examQuestionText');

        if (examQuestionNum) examQuestionNum.textContent = this.currentExamQuestionIndex + 1;
        if (examQuestionType) examQuestionType.textContent = question.type;
        if (examQuestionText) examQuestionText.textContent = question.question;

        // 更新选项
        const optionsContainer = document.getElementById('examQuestionOptions');
        if (optionsContainer) {
            optionsContainer.innerHTML = question.options.map(option => `
                <div class="option-item" data-value="${option.value}">
                    <div class="option-label">${option.value}</div>
                    <div class="option-text">${option.text}</div>
                </div>
            `).join('');

            // 重新绑定选项事件
            const options = optionsContainer.querySelectorAll('.option-item');
            options.forEach(option => {
                option.addEventListener('click', (e) => {
                    this.selectExamOption(e.currentTarget);
                });
            });
        }

        // 恢复之前的选择
        const savedAnswer = this.examAnswers[this.currentExamQuestionIndex];
        if (savedAnswer && optionsContainer) {
            const savedOption = optionsContainer.querySelector(`[data-value="${savedAnswer}"]`);
            if (savedOption) {
                savedOption.classList.add('selected');
            }
        }

        // 更新题目导航
        this.updateQuestionNavCurrent();
    }

    generateQuestionGrid() {
        const grid = document.getElementById('questionGrid');
        if (!grid) return;

        grid.innerHTML = '';

        for (let i = 0; i < this.examQuestions.length; i++) {
            const item = document.createElement('div');
            item.className = 'question-nav-item';
            item.textContent = i + 1;
            item.addEventListener('click', () => {
                this.currentExamQuestionIndex = i;
                this.displayCurrentExamQuestion();
            });
            grid.appendChild(item);
        }

        this.updateQuestionNavStatus();
    }

    updateQuestionNavStatus() {
        const navItems = document.querySelectorAll('.question-nav-item');
        navItems.forEach((item, index) => {
            item.classList.remove('current', 'answered');

            if (index === this.currentExamQuestionIndex) {
                item.classList.add('current');
            }

            if (this.examAnswers[index] !== null) {
                item.classList.add('answered');
            }
        });
    }

    updateQuestionNavCurrent() {
        const navItems = document.querySelectorAll('.question-nav-item');
        navItems.forEach((item, index) => {
            item.classList.remove('current');
            if (index === this.currentExamQuestionIndex) {
                item.classList.add('current');
            }
        });
    }

    updateExamStats() {
        const answeredCount = this.examAnswers.filter(answer => answer !== null).length;
        const unansweredCount = this.examQuestions.length - answeredCount;

        const answeredCountEl = document.getElementById('answeredCount');
        const unansweredCountEl = document.getElementById('unansweredCount');

        if (answeredCountEl) answeredCountEl.textContent = answeredCount;
        if (unansweredCountEl) unansweredCountEl.textContent = unansweredCount;

        const progress = (answeredCount / this.examQuestions.length) * 100;
        const examProgress = document.getElementById('examProgress');
        if (examProgress) {
            examProgress.style.width = `${progress}%`;
        }
    }

    startExamTimer() {
        this.examTimerInterval = setInterval(() => {
            const elapsed = Date.now() - this.examStartTime;
            const remaining = Math.max(0, this.examDuration - elapsed);

            if (remaining === 0) {
                this.submitExam();
                return;
            }

            const minutes = Math.floor(remaining / 60000);
            const seconds = Math.floor((remaining % 60000) / 1000);

            const examTimer = document.getElementById('examTimer');
            if (examTimer) {
                examTimer.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

                // 最后5分钟变红色警告
                if (remaining <= 5 * 60 * 1000) {
                    examTimer.style.color = '#ff4d4f';
                }
            }
        }, 1000);
    }

    prevExamQuestion() {
        if (this.currentExamQuestionIndex > 0) {
            this.currentExamQuestionIndex--;
            this.displayCurrentExamQuestion();
        }
    }

    nextExamQuestion() {
        if (this.currentExamQuestionIndex < this.examQuestions.length - 1) {
            this.currentExamQuestionIndex++;
            this.displayCurrentExamQuestion();
        }
    }

    submitExam() {
        const unansweredCount = this.examAnswers.filter(answer => answer === null).length;

        let confirmMessage = '确定要提交试卷吗？提交后将无法修改答案。';
        if (unansweredCount > 0) {
            confirmMessage = `还有${unansweredCount}道题未作答，确定要提交试卷吗？`;
        }

        if (confirm(confirmMessage)) {
            if (this.examTimerInterval) {
                clearInterval(this.examTimerInterval);
            }

            // 计算成绩
            let correctCount = 0;
            this.examQuestions.forEach((question, index) => {
                if (this.examAnswers[index] === question.correctAnswer) {
                    correctCount++;
                }
            });

            const score = Math.round((correctCount / this.examQuestions.length) * 100);
            const totalTime = Date.now() - this.examStartTime;
            const timeUsed = Math.floor(totalTime / 60000);

            this.showNotification(`考试完成！得分：${score}分，用时：${timeUsed}分钟`, 'success', 8000);

            // 返回首页
            setTimeout(() => {
                this.switchTab('home');
            }, 3000);
        }
    }

    loadExamQuestions() {
        // 返回更多题目用于考试
        const practiceQuestions = this.loadPracticeQuestions();
        // 扩展题目，实际应用中应该从服务器获取
        const examQuestions = [
            ...practiceQuestions,
            {
                id: 4,
                type: '单选题',
                question: '《论语》的作者是谁？',
                options: [
                    { value: 'A', text: '孔子' },
                    { value: 'B', text: '孟子' },
                    { value: 'C', text: '孔子弟子及再传弟子' },
                    { value: 'D', text: '老子' }
                ],
                correctAnswer: 'C',
                explanation: '《论语》并非孔子本人所著，而是由孔子的弟子及再传弟子编撰而成。',
                knowledgePoints: ['中国古代文学', '儒家思想', '经典著作']
            },
            {
                id: 5,
                type: '多选题',
                question: '以下哪些属于中国的四大发明？',
                options: [
                    { value: 'A', text: '造纸术' },
                    { value: 'B', text: '指南针' },
                    { value: 'C', text: '火药' },
                    { value: 'D', text: '印刷术' }
                ],
                correctAnswer: 'ABCD',
                explanation: '中国古代四大发明是造纸术、指南针、火药和印刷术。',
                knowledgePoints: ['中国古代史', '科技发明', '文化传承']
            }
        ];

        return examQuestions.slice(0, 10); // 限制为10题用于演示
    }
}

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
});
