/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.5;
    min-height: 100vh;
}

#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
}

/* 顶部导航栏 */
.nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 46px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-bottom: 1px solid #ebedf0;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-left, .nav-right {
    width: 60px;
    display: flex;
    align-items: center;
}

.nav-back {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
}

.nav-back .icon {
    width: 20px;
    height: 20px;
    fill: #333;
}

.nav-title {
    flex: 1;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

/* 页面容器 */
.page-container {
    flex: 1;
    padding-top: 46px;
    padding-bottom: 60px;
    overflow-y: auto;
}

.page {
    padding: 16px;
    min-height: calc(100vh - 106px);
}

/* 用户信息卡片 */
.user-card {
    margin-bottom: 20px;
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.avatar {
    width: 50px;
    height: 50px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1989fa;
    border-radius: 50%;
}

.avatar .icon {
    width: 30px;
    height: 30px;
    fill: #fff;
}

.info .name {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.info .level {
    font-size: 14px;
    color: #666;
}

.stats {
    display: flex;
    justify-content: space-around;
}

.stat-item {
    text-align: center;
}

.stat-item .number {
    font-size: 20px;
    font-weight: 600;
    color: #1989fa;
    margin-bottom: 4px;
}

.stat-item .label {
    font-size: 12px;
    color: #666;
}

/* 快速入口 */
.quick-actions {
    margin-bottom: 20px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
}

.grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.grid-item {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.grid-item:active {
    transform: scale(0.98);
    background: #f8f9fa;
}

.grid-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.grid-text {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

/* 学习进度 */
.progress-section {
    margin-bottom: 20px;
}

.progress-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.progress-item {
    cursor: pointer;
    transition: all 0.3s ease;
}

.progress-item:active {
    transform: scale(0.98);
}

.subject-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.subject-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.progress-text {
    font-size: 14px;
    color: #666;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #f2f3f5;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #1989fa;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-fill.complete {
    background: #07c160;
}

/* 推荐练习 */
.recommend-section {
    margin-bottom: 20px;
}

.card-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.recommend-card {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.recommend-card:active {
    transform: scale(0.98);
}

.card-thumb {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-right: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.card-content {
    flex: 1;
}

.card-title {
    font-size: 16px;
    font-weight: 500;
    color: #323233;
    margin-bottom: 4px;
}

.card-desc {
    font-size: 14px;
    color: #969799;
    margin-bottom: 8px;
}

.card-tag {
    display: inline-block;
    padding: 2px 8px;
    background: #fff2e8;
    color: #ff976a;
    font-size: 12px;
    border-radius: 12px;
}

/* 练习页面 */
.subject-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.subject-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.subject-item:active {
    transform: scale(0.98);
}

.subject-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-right: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.subject-content {
    flex: 1;
}

.subject-desc {
    font-size: 14px;
    color: #969799;
    margin-bottom: 8px;
}

.subject-progress {
    display: flex;
    align-items: center;
    gap: 8px;
}

.subject-progress .progress-bar {
    flex: 1;
}

.progress-percent {
    font-size: 12px;
    color: #969799;
    min-width: 35px;
}

.subject-arrow {
    font-size: 18px;
    color: #c8c9cc;
    margin-left: 8px;
}

/* 考试页面 */
.exam-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.exam-item {
    cursor: pointer;
    transition: all 0.3s ease;
}

.exam-item:active {
    transform: scale(0.98);
}

.exam-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.exam-title {
    font-size: 16px;
    font-weight: 500;
    color: #323233;
}

.exam-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.exam-status.available {
    background: #e8f5e8;
    color: #07c160;
}

.exam-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.exam-detail {
    display: flex;
    gap: 16px;
    font-size: 14px;
    color: #969799;
}

.exam-desc {
    font-size: 14px;
    color: #646566;
}

/* 个人中心页面 */
.profile-header {
    display: flex;
    align-items: center;
    padding: 20px 0;
    margin-bottom: 20px;
}

.profile-avatar {
    width: 60px;
    height: 60px;
    margin-right: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f9ff;
    border-radius: 50%;
}

.profile-avatar .icon {
    width: 36px;
    height: 36px;
    fill: #1989fa;
}

.profile-name {
    font-size: 20px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 4px;
}

.profile-level {
    font-size: 14px;
    color: #969799;
}

.menu-list {
    display: flex;
    flex-direction: column;
    gap: 1px;
    background: #ebedf0;
    border-radius: 8px;
    overflow: hidden;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.menu-item:active {
    background: #f8f9fa;
}

.menu-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 18px;
}

.menu-text {
    flex: 1;
    font-size: 16px;
    color: #323233;
}

.menu-arrow {
    font-size: 18px;
    color: #c8c9cc;
}

/* 底部导航栏 */
.tabbar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: #fff;
    display: flex;
    border-top: 1px solid #ebedf0;
    z-index: 1000;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.tabbar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tabbar-item.active .tabbar-icon {
    color: #1989fa;
}

.tabbar-item.active .tabbar-text {
    color: #1989fa;
}

.tabbar-icon {
    font-size: 20px;
    margin-bottom: 4px;
    color: #666;
    transition: all 0.3s ease;
}

.tabbar-text {
    font-size: 12px;
    color: #666;
    transition: all 0.3s ease;
}

/* 练习详情页面 */
.practice-header {
    margin-bottom: 20px;
}

.practice-info {
    margin-bottom: 16px;
}

.practice-title {
    font-size: 18px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 8px;
}

.practice-progress {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #969799;
}

.practice-stats {
    display: flex;
    justify-content: space-around;
    padding-top: 16px;
    border-top: 1px solid #ebedf0;
}

.practice-modes {
    margin-bottom: 20px;
}

.mode-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.mode-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mode-item:active {
    transform: scale(0.98);
}

.mode-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-right: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.mode-content {
    flex: 1;
}

.mode-name {
    font-size: 16px;
    font-weight: 500;
    color: #323233;
    margin-bottom: 4px;
}

.mode-desc {
    font-size: 14px;
    color: #969799;
}

.mode-arrow {
    font-size: 18px;
    color: #c8c9cc;
    margin-left: 8px;
}

.chapter-list {
    margin-bottom: 20px;
}

.chapters {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.chapter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chapter-item:active {
    transform: scale(0.98);
}

.chapter-name {
    font-size: 16px;
    font-weight: 500;
    color: #323233;
    margin-bottom: 4px;
}

.chapter-progress {
    font-size: 14px;
    color: #969799;
}

.chapter-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.chapter-status.complete {
    background: #e8f5e8;
    color: #07c160;
}

.chapter-status.progress {
    background: #e8f4fd;
    color: #1989fa;
}

.chapter-status:not(.complete):not(.progress) {
    background: #f7f8fa;
    color: #969799;
}

/* 考试详情页面 */
.exam-header {
    margin-bottom: 20px;
}

.exam-header .exam-title {
    font-size: 20px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 12px;
}

.exam-header .exam-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.exam-header .exam-detail {
    display: flex;
    gap: 16px;
    font-size: 14px;
    color: #646566;
}

.exam-header .exam-desc {
    font-size: 14px;
    color: #969799;
}

.exam-rules {
    margin-bottom: 20px;
}

.rules-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.rule-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.rule-icon {
    font-size: 16px;
    margin-top: 2px;
}

.rule-text {
    flex: 1;
    font-size: 14px;
    color: #646566;
    line-height: 1.5;
}

.exam-actions {
    position: fixed;
    bottom: 80px;
    left: 16px;
    right: 16px;
}

/* 连续练习做题页面 */
.question-header {
    padding: 16px;
    background: #fff;
    border-bottom: 1px solid #ebedf0;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.question-progress {
    margin-bottom: 12px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
}

.current-question {
    color: #1989fa;
    font-weight: 600;
}

.question-timer {
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    color: #1989fa;
}

.question-content {
    margin-bottom: 20px;
}

.question-type {
    display: inline-block;
    padding: 4px 12px;
    background: #1989fa;
    color: #fff;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 16px;
}

.question-text {
    font-size: 16px;
    line-height: 1.6;
    color: #333;
}

.question-options {
    margin-bottom: 20px;
}

.option-item {
    display: flex;
    align-items: center;
    padding: 16px;
    margin-bottom: 12px;
    background: #fff;
    border: 1px solid #ebedf0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.option-item:hover {
    border-color: #1989fa;
    background: #f0f9ff;
}

.option-item.selected {
    border-color: #1989fa;
    background: #e6f7ff;
    box-shadow: 0 0 0 2px rgba(25, 137, 250, 0.2);
}

.option-item.correct {
    border-color: #52c41a;
    background: #f6ffed;
}

.option-item.wrong {
    border-color: #ff4d4f;
    background: #fff2f0;
}

.option-label {
    width: 32px;
    height: 32px;
    background: #1989fa;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 12px;
    flex-shrink: 0;
}

.option-item.selected .option-label {
    background: #1989fa;
    box-shadow: 0 0 0 2px rgba(25, 137, 250, 0.3);
}

.option-text {
    flex: 1;
    font-size: 15px;
    color: #333;
    line-height: 1.5;
}

.question-actions {
    display: flex;
    gap: 12px;
    padding: 16px;
    position: fixed;
    bottom: 80px;
    left: 16px;
    right: 16px;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #ebedf0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.question-actions .btn {
    flex: 1;
}

/* AI解析面板 */
.ai-analysis {
    margin-bottom: 100px;
    border: 1px solid #1989fa;
    background: #f0f9ff;
}

.analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #d9ecff;
}

.analysis-title {
    font-size: 16px;
    font-weight: 600;
    color: #1989fa;
}

.analysis-result {
    display: flex;
    align-items: center;
    gap: 6px;
}

.result-icon {
    font-size: 16px;
}

.result-text {
    font-size: 14px;
    font-weight: 600;
}

.analysis-result.correct {
    color: #52c41a;
}

.analysis-result.wrong {
    color: #ff4d4f;
}

.analysis-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.analysis-section .section-title {
    font-size: 14px;
    font-weight: 600;
    color: #1989fa;
    margin-bottom: 8px;
}

.analysis-section .section-content {
    font-size: 14px;
    line-height: 1.6;
    color: #333;
}

.knowledge-tag {
    display: inline-block;
    padding: 4px 8px;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 12px;
    font-size: 12px;
    color: #1989fa;
    margin-right: 8px;
    margin-bottom: 4px;
}

/* 考试进行页面 */
.exam-progress-header {
    padding: 16px;
    background: #fff;
    border-bottom: 1px solid #ebedf0;
    margin-bottom: 16px;
    position: sticky;
    top: 46px;
    z-index: 999;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.exam-info-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.exam-title-small {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.exam-timer {
    font-size: 18px;
    font-weight: 600;
    color: #ff4d4f;
}

.exam-progress {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.progress-stats {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #666;
}

.exam-question-content {
    margin-bottom: 20px;
}

.question-number {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.exam-question-options {
    margin-bottom: 20px;
}

.exam-question-actions {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    padding: 16px;
    margin-bottom: 20px;
}

.exam-question-actions .btn {
    flex: 1;
}

/* 题目导航 */
.question-navigator {
    margin-bottom: 100px;
}

.navigator-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
    text-align: center;
}

.question-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
}

.question-nav-item {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    border: 1px solid #ebedf0;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
}

.question-nav-item:hover {
    border-color: #1989fa;
    background: #f0f9ff;
}

.question-nav-item.current {
    background: #1989fa;
    color: #fff;
    border-color: #1989fa;
}

.question-nav-item.answered {
    background: #f6ffed;
    border-color: #52c41a;
    color: #52c41a;
}

.question-nav-item.flagged {
    background: #fff7e6;
    border-color: #fa8c16;
    color: #fa8c16;
}
