/* 通用组件样式 */

/* 卡片容器 */
.card-container {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #ebedf0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.card-container:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    min-height: 44px;
}

.btn-primary {
    background: #1989fa;
    color: #fff;
    border: 1px solid #1989fa;
}

.btn-primary:hover {
    background: #40a9ff;
    border-color: #40a9ff;
}

.btn-primary:active {
    background: #096dd9;
    border-color: #096dd9;
    transform: scale(0.98);
}

.btn-secondary {
    background: #f7f8fa;
    color: #323233;
    border: 1px solid #ebedf0;
}

.btn-secondary:hover {
    background: #f2f3f5;
}

.btn-secondary:active {
    background: #ebedf0;
    transform: scale(0.98);
}

.btn-success {
    background: #07c160;
    color: white;
}

.btn-success:hover {
    background: #06ad56;
}

.btn-success:active {
    background: #059748;
    transform: scale(0.98);
}

.btn-warning {
    background: #ff976a;
    color: white;
}

.btn-warning:hover {
    background: #ff8a50;
}

.btn-warning:active {
    background: #ff7d36;
    transform: scale(0.98);
}

.btn-danger {
    background: #ee0a24;
    color: white;
}

.btn-danger:hover {
    background: #d60920;
}

.btn-danger:active {
    background: #be081c;
    transform: scale(0.98);
}

.btn-large {
    padding: 16px 32px;
    font-size: 16px;
    min-height: 50px;
}

.btn-small {
    padding: 8px 16px;
    font-size: 12px;
    min-height: 32px;
}

.btn-block {
    width: 100%;
}

.btn-round {
    border-radius: 22px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn:disabled:hover,
.btn:disabled:active {
    transform: none;
}

/* 输入框组件 */
.input-group {
    margin-bottom: 16px;
}

.input-label {
    display: block;
    font-size: 14px;
    color: #323233;
    margin-bottom: 8px;
    font-weight: 500;
}

.input-field {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ebedf0;
    border-radius: 6px;
    font-size: 16px;
    background: white;
    transition: all 0.3s ease;
}

.input-field:focus {
    outline: none;
    border-color: #1989fa;
    box-shadow: 0 0 0 2px rgba(25, 137, 250, 0.1);
}

.input-field::placeholder {
    color: #c8c9cc;
}

.input-field:disabled {
    background: #f7f8fa;
    color: #c8c9cc;
    cursor: not-allowed;
}

.input-error {
    border-color: #ee0a24;
}

.input-error:focus {
    border-color: #ee0a24;
    box-shadow: 0 0 0 2px rgba(238, 10, 36, 0.1);
}

.input-help {
    font-size: 12px;
    color: #969799;
    margin-top: 4px;
}

.input-error-text {
    font-size: 12px;
    color: #ee0a24;
    margin-top: 4px;
}

/* 选择框组件 */
.select-field {
    position: relative;
    width: 100%;
}

.select-field select {
    width: 100%;
    padding: 12px 40px 12px 16px;
    border: 1px solid #ebedf0;
    border-radius: 6px;
    font-size: 16px;
    background: white;
    appearance: none;
    cursor: pointer;
}

.select-field::after {
    content: '';
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #c8c9cc;
    pointer-events: none;
}

/* 复选框和单选框 */
.checkbox-group,
.radio-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.checkbox-item,
.radio-item {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox-item input,
.radio-item input {
    margin-right: 8px;
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.checkbox-item label,
.radio-item label {
    font-size: 14px;
    color: #323233;
    cursor: pointer;
}

/* 标签组件 */
.tag {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.tag-primary {
    background: #e8f4fd;
    color: #1989fa;
}

.tag-success {
    background: #e8f5e8;
    color: #07c160;
}

.tag-warning {
    background: #fff2e8;
    color: #ff976a;
}

.tag-danger {
    background: #ffeaea;
    color: #ee0a24;
}

.tag-info {
    background: #f7f8fa;
    color: #646566;
}

/* 加载组件 */
.loading {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1989fa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
    color: #969799;
}

/* 空状态组件 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-text {
    font-size: 16px;
    color: #969799;
    margin-bottom: 8px;
}

.empty-desc {
    font-size: 14px;
    color: #c8c9cc;
}

/* 分割线 */
.divider {
    height: 1px;
    background: #ebedf0;
    margin: 16px 0;
}

.divider-text {
    position: relative;
    text-align: center;
    background: #f7f8fa;
    padding: 0 16px;
    font-size: 14px;
    color: #969799;
}

.divider-text::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    right: 0;
    height: 1px;
    background: #ebedf0;
    z-index: -1;
}

/* 通知组件 */
.notice {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notice-info {
    background: #e8f4fd;
    color: #1989fa;
    border: 1px solid #b3d8ff;
}

.notice-success {
    background: #e8f5e8;
    color: #07c160;
    border: 1px solid #b3e5b3;
}

.notice-warning {
    background: #fff2e8;
    color: #ff976a;
    border: 1px solid #ffcc99;
}

.notice-error {
    background: #ffeaea;
    color: #ee0a24;
    border: 1px solid #ffb3b3;
}

.notice-icon {
    font-size: 16px;
}

.notice-text {
    flex: 1;
    font-size: 14px;
}

/* 响应式工具类 */
@media (max-width: 768px) {
    .hidden-mobile {
        display: none !important;
    }
}

@media (min-width: 769px) {
    .hidden-desktop {
        display: none !important;
    }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-8 { margin-top: 8px; }
.mt-16 { margin-top: 16px; }
.mt-24 { margin-top: 24px; }
.mb-8 { margin-bottom: 8px; }
.mb-16 { margin-bottom: 16px; }
.mb-24 { margin-bottom: 24px; }

.p-8 { padding: 8px; }
.p-16 { padding: 16px; }
.p-24 { padding: 24px; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }

.w-full { width: 100%; }
.h-full { height: 100%; }
