<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考试刷题系统</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/components.css">
</head>
<body>
    <div id="app">
        <!-- 顶部导航栏 -->
        <div class="nav-bar" id="navBar">
            <div class="nav-left">
                <button class="nav-back" id="navBack" style="display: none;">
                    <svg class="icon" viewBox="0 0 1024 1024">
                        <path d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 0 0 0 50.3l450.8 352.1c5.2 4.1 12.9 0.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"/>
                    </svg>
                </button>
            </div>
            <div class="nav-title" id="navTitle">考试刷题系统</div>
            <div class="nav-right"></div>
        </div>

        <!-- 页面容器 -->
        <div class="page-container" id="pageContainer">
            <!-- 首页 -->
            <div class="page" id="homePage">
                <!-- 用户信息卡片 -->
                <div class="user-card card-container">
                    <div class="user-info">
                        <div class="avatar">
                            <svg class="icon" viewBox="0 0 1024 1024">
                                <path d="M512 74.667C270.933 74.667 74.667 270.933 74.667 512S270.933 949.333 512 949.333 949.333 753.067 949.333 512 753.067 74.667 512 74.667zM512 256c70.4 0 128 57.6 128 128s-57.6 128-128 128-128-57.6-128-128 57.6-128 128-128z m0 576c-105.6 0-201.6-54.4-256-140.8 1.067-85.333 170.667-132.267 256-132.267s254.933 46.933 256 132.267C713.6 777.6 617.6 832 512 832z"/>
                            </svg>
                        </div>
                        <div class="info">
                            <div class="name">张某某</div>
                            <div class="level">学习等级：LV.5</div>
                        </div>
                    </div>
                    <div class="stats">
                        <div class="stat-item">
                            <div class="number">128</div>
                            <div class="label">练习次数</div>
                        </div>
                        <div class="stat-item">
                            <div class="number">15</div>
                            <div class="label">考试次数</div>
                        </div>
                        <div class="stat-item">
                            <div class="number">85%</div>
                            <div class="label">正确率</div>
                        </div>
                    </div>
                </div>

                <!-- 快速入口 -->
                <div class="quick-actions">
                    <div class="section-title">快速开始</div>
                    <div class="grid">
                        <div class="grid-item action-item" data-action="practice">
                            <div class="grid-icon">📝</div>
                            <div class="grid-text">开始练习</div>
                        </div>
                        <div class="grid-item action-item" data-action="exam">
                            <div class="grid-icon">✅</div>
                            <div class="grid-text">模拟考试</div>
                        </div>
                        <div class="grid-item action-item" data-action="wrong">
                            <div class="grid-icon">❌</div>
                            <div class="grid-text">错题复习</div>
                        </div>
                        <div class="grid-item action-item" data-action="report">
                            <div class="grid-icon">📊</div>
                            <div class="grid-text">学习报告</div>
                        </div>
                    </div>
                </div>

                <!-- 学习进度 -->
                <div class="progress-section">
                    <div class="section-title">学习进度</div>
                    <div class="progress-list">
                        <div class="progress-item card-container" data-subject="1">
                            <div class="subject-info">
                                <div class="subject-name">公共基础知识</div>
                                <div class="progress-text">450/600 题</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 75%"></div>
                            </div>
                        </div>
                        <div class="progress-item card-container" data-subject="2">
                            <div class="subject-info">
                                <div class="subject-name">专业知识</div>
                                <div class="progress-text">280/400 题</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 70%"></div>
                            </div>
                        </div>
                        <div class="progress-item card-container" data-subject="3">
                            <div class="subject-info">
                                <div class="subject-name">综合能力</div>
                                <div class="progress-text">200/200 题</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill complete" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 推荐练习 -->
                <div class="recommend-section">
                    <div class="section-title">推荐练习</div>
                    <div class="card-list">
                        <div class="recommend-card card-container">
                            <div class="card-thumb">🎯</div>
                            <div class="card-content">
                                <div class="card-title">AI智能练习</div>
                                <div class="card-desc">基于大数据分析的个性化题目推荐</div>
                                <div class="card-tag">智能</div>
                            </div>
                        </div>
                        <div class="recommend-card card-container">
                            <div class="card-thumb">🚀</div>
                            <div class="card-content">
                                <div class="card-title">重点突破训练</div>
                                <div class="card-desc">针对薄弱知识点的专项强化练习</div>
                                <div class="card-tag">推荐</div>
                            </div>
                        </div>
                        <div class="recommend-card card-container">
                            <div class="card-thumb">⚡</div>
                            <div class="card-content">
                                <div class="card-title">快速提分模式</div>
                                <div class="card-desc">高效学习路径，快速提升答题能力</div>
                                <div class="card-tag">热门</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 练习页面 -->
            <div class="page" id="practicePage" style="display: none;">
                <div class="section-title">选择练习科目</div>
                <div class="subject-list">
                    <div class="subject-item card-container" data-subject="1">
                        <div class="subject-icon">📚</div>
                        <div class="subject-content">
                            <div class="subject-name">公共基础知识</div>
                            <div class="subject-desc">共600题 · 已练习450题</div>
                            <div class="subject-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 75%"></div>
                                </div>
                                <span class="progress-percent">75%</span>
                            </div>
                        </div>
                        <div class="subject-arrow">›</div>
                    </div>
                    <div class="subject-item card-container" data-subject="2">
                        <div class="subject-icon">💼</div>
                        <div class="subject-content">
                            <div class="subject-name">专业知识</div>
                            <div class="subject-desc">共400题 · 已练习280题</div>
                            <div class="subject-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 70%"></div>
                                </div>
                                <span class="progress-percent">70%</span>
                            </div>
                        </div>
                        <div class="subject-arrow">›</div>
                    </div>
                    <div class="subject-item card-container" data-subject="3">
                        <div class="subject-icon">🎯</div>
                        <div class="subject-content">
                            <div class="subject-name">综合能力</div>
                            <div class="subject-desc">共200题 · 已完成</div>
                            <div class="subject-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill complete" style="width: 100%"></div>
                                </div>
                                <span class="progress-percent">100%</span>
                            </div>
                        </div>
                        <div class="subject-arrow">›</div>
                    </div>
                </div>
            </div>

            <!-- 考试页面 -->
            <div class="page" id="examPage" style="display: none;">
                <div class="section-title">选择考试</div>
                <div class="exam-list">
                    <div class="exam-item card-container" data-exam="1">
                        <div class="exam-header">
                            <div class="exam-title">模拟考试一</div>
                            <div class="exam-status available">可参加</div>
                        </div>
                        <div class="exam-info">
                            <div class="exam-detail">
                                <span>题目数量：100题</span>
                                <span>考试时长：120分钟</span>
                            </div>
                            <div class="exam-desc">包含公共基础知识、专业知识等综合题型</div>
                        </div>
                    </div>
                    <div class="exam-item card-container" data-exam="2">
                        <div class="exam-header">
                            <div class="exam-title">模拟考试二</div>
                            <div class="exam-status available">可参加</div>
                        </div>
                        <div class="exam-info">
                            <div class="exam-detail">
                                <span>题目数量：80题</span>
                                <span>考试时长：90分钟</span>
                            </div>
                            <div class="exam-desc">重点考查专业知识和实际应用能力</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人中心页面 -->
            <div class="page" id="profilePage" style="display: none;">
                <div class="profile-header">
                    <div class="profile-avatar">
                        <svg class="icon" viewBox="0 0 1024 1024">
                            <path d="M512 74.667C270.933 74.667 74.667 270.933 74.667 512S270.933 949.333 512 949.333 949.333 753.067 949.333 512 753.067 74.667 512 74.667zM512 256c70.4 0 128 57.6 128 128s-57.6 128-128 128-128-57.6-128-128 57.6-128 128-128z m0 576c-105.6 0-201.6-54.4-256-140.8 1.067-85.333 170.667-132.267 256-132.267s254.933 46.933 256 132.267C713.6 777.6 617.6 832 512 832z"/>
                        </svg>
                    </div>
                    <div class="profile-info">
                        <div class="profile-name">张某某</div>
                        <div class="profile-level">学习等级：LV.5</div>
                    </div>
                </div>

                <div class="menu-list">
                    <div class="menu-item" data-action="history">
                        <div class="menu-icon">📋</div>
                        <div class="menu-text">历史记录</div>
                        <div class="menu-arrow">›</div>
                    </div>
                    <div class="menu-item" data-action="wrong">
                        <div class="menu-icon">❌</div>
                        <div class="menu-text">错题本</div>
                        <div class="menu-arrow">›</div>
                    </div>
                    <div class="menu-item" data-action="feedback">
                        <div class="menu-icon">💬</div>
                        <div class="menu-text">意见反馈</div>
                        <div class="menu-arrow">›</div>
                    </div>
                    <div class="menu-item" data-action="settings">
                        <div class="menu-icon">⚙️</div>
                        <div class="menu-text">设置</div>
                        <div class="menu-arrow">›</div>
                    </div>
                </div>
            </div>

            <!-- 练习详情页面 -->
            <div class="page" id="practiceDetailPage" style="display: none;">
                <div class="practice-header card-container">
                    <div class="practice-info">
                        <div class="practice-title">公共基础知识</div>
                        <div class="practice-progress">
                            <span>进度：450/600题</span>
                            <span class="progress-percent">75%</span>
                        </div>
                    </div>
                    <div class="practice-stats">
                        <div class="stat-item">
                            <div class="stat-number">85%</div>
                            <div class="stat-label">正确率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">128</div>
                            <div class="stat-label">已练习</div>
                        </div>
                    </div>
                </div>

                <div class="practice-modes">
                    <div class="section-title">练习模式</div>
                    <div class="mode-list">
                        <div class="mode-item card-container" data-mode="sequence">
                            <div class="mode-icon">📖</div>
                            <div class="mode-content">
                                <div class="mode-name">顺序练习</div>
                                <div class="mode-desc">按章节顺序练习，系统学习</div>
                            </div>
                            <div class="mode-arrow">›</div>
                        </div>
                        <div class="mode-item card-container" data-mode="random">
                            <div class="mode-icon">🎲</div>
                            <div class="mode-content">
                                <div class="mode-name">随机练习</div>
                                <div class="mode-desc">随机抽取题目，巩固知识</div>
                            </div>
                            <div class="mode-arrow">›</div>
                        </div>
                        <div class="mode-item card-container" data-mode="wrong">
                            <div class="mode-icon">❌</div>
                            <div class="mode-content">
                                <div class="mode-name">错题练习</div>
                                <div class="mode-desc">专项练习错题，查漏补缺</div>
                            </div>
                            <div class="mode-arrow">›</div>
                        </div>
                    </div>
                </div>

                <div class="chapter-list">
                    <div class="section-title">章节练习</div>
                    <div class="chapters">
                        <div class="chapter-item card-container" data-chapter="1">
                            <div class="chapter-info">
                                <div class="chapter-name">第一章 基础理论</div>
                                <div class="chapter-progress">45/50题 · 90%</div>
                            </div>
                            <div class="chapter-status complete">已完成</div>
                        </div>
                        <div class="chapter-item card-container" data-chapter="2">
                            <div class="chapter-info">
                                <div class="chapter-name">第二章 实务应用</div>
                                <div class="chapter-progress">32/40题 · 80%</div>
                            </div>
                            <div class="chapter-status progress">进行中</div>
                        </div>
                        <div class="chapter-item card-container" data-chapter="3">
                            <div class="chapter-info">
                                <div class="chapter-name">第三章 综合分析</div>
                                <div class="chapter-progress">0/35题 · 0%</div>
                            </div>
                            <div class="chapter-status">未开始</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 考试详情页面 -->
            <div class="page" id="examDetailPage" style="display: none;">
                <div class="exam-header card-container">
                    <div class="exam-title">模拟考试一</div>
                    <div class="exam-info">
                        <div class="exam-detail">
                            <span>📝 100题</span>
                            <span>⏰ 120分钟</span>
                            <span>📊 综合测试</span>
                        </div>
                        <div class="exam-desc">包含公共基础知识、专业知识等综合题型</div>
                    </div>
                </div>

                <div class="exam-rules card-container">
                    <div class="section-title">考试须知</div>
                    <div class="rules-list">
                        <div class="rule-item">
                            <span class="rule-icon">⏰</span>
                            <span class="rule-text">考试时间为120分钟，请合理安排答题时间</span>
                        </div>
                        <div class="rule-item">
                            <span class="rule-icon">📝</span>
                            <span class="rule-text">共100道题目，包括单选题、多选题和判断题</span>
                        </div>
                        <div class="rule-item">
                            <span class="rule-icon">💾</span>
                            <span class="rule-text">系统会自动保存答题进度，可随时暂停</span>
                        </div>
                        <div class="rule-item">
                            <span class="rule-icon">✅</span>
                            <span class="rule-text">提交后不可修改，请仔细检查后再提交</span>
                        </div>
                    </div>
                </div>

                <div class="exam-actions">
                    <button class="btn btn-primary btn-large btn-block" id="startExamBtn">开始考试</button>
                </div>
            </div>

            <!-- 连续练习做题页面 -->
            <div class="page" id="practiceQuestionPage" style="display: none;">
                <div class="question-header">
                    <div class="question-progress">
                        <div class="progress-info">
                            <span class="current-question">第 <span id="currentQuestionNum">1</span> 题</span>
                            <span class="total-questions">共 <span id="totalQuestions">20</span> 题</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="questionProgress" style="width: 5%"></div>
                        </div>
                    </div>
                    <div class="question-timer" id="questionTimer">00:00</div>
                </div>

                <div class="question-content card-container">
                    <div class="question-type" id="questionType">单选题</div>
                    <div class="question-text" id="questionText">
                        中华人民共和国成立于哪一年？
                    </div>
                </div>

                <div class="question-options" id="questionOptions">
                    <div class="option-item" data-value="A">
                        <div class="option-label">A</div>
                        <div class="option-text">1948年</div>
                    </div>
                    <div class="option-item" data-value="B">
                        <div class="option-label">B</div>
                        <div class="option-text">1949年</div>
                    </div>
                    <div class="option-item" data-value="C">
                        <div class="option-label">C</div>
                        <div class="option-text">1950年</div>
                    </div>
                    <div class="option-item" data-value="D">
                        <div class="option-label">D</div>
                        <div class="option-text">1951年</div>
                    </div>
                </div>

                <div class="question-actions">
                    <button class="btn btn-secondary" id="prevQuestionBtn" disabled>上一题</button>
                    <button class="btn btn-primary" id="submitAnswerBtn" disabled>提交答案</button>
                    <button class="btn btn-secondary" id="nextQuestionBtn" style="display: none;">下一题</button>
                </div>

                <!-- AI解析面板 -->
                <div class="ai-analysis card-container" id="aiAnalysis" style="display: none;">
                    <div class="analysis-header">
                        <div class="analysis-title">🤖 AI智能解析</div>
                        <div class="analysis-result" id="analysisResult">
                            <span class="result-icon">✅</span>
                            <span class="result-text">回答正确</span>
                        </div>
                    </div>

                    <div class="analysis-content">
                        <div class="analysis-section">
                            <div class="section-title">📝 答案解析</div>
                            <div class="section-content" id="answerExplanation">
                                中华人民共和国成立于1949年10月1日，这是中国历史上的重要时刻，标志着新中国的诞生。
                            </div>
                        </div>

                        <div class="analysis-section">
                            <div class="section-title">💡 知识点</div>
                            <div class="section-content" id="knowledgePoints">
                                <div class="knowledge-tag">中国近现代史</div>
                                <div class="knowledge-tag">新中国成立</div>
                                <div class="knowledge-tag">重要历史事件</div>
                            </div>
                        </div>

                        <div class="analysis-section" id="errorAnalysisSection" style="display: none;">
                            <div class="section-title">❌ 错误分析</div>
                            <div class="section-content" id="errorAnalysis">
                                您选择了1948年，这是错误的。1948年是中华人民共和国成立的前一年，当时正值解放战争的关键时期。
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 考试进行页面 -->
            <div class="page" id="examQuestionPage" style="display: none;">
                <div class="exam-progress-header">
                    <div class="exam-info-bar">
                        <div class="exam-title-small">模拟考试一</div>
                        <div class="exam-timer" id="examTimer">120:00</div>
                    </div>
                    <div class="exam-progress">
                        <div class="progress-stats">
                            <span>已答：<span id="answeredCount">0</span></span>
                            <span>未答：<span id="unansweredCount">100</span></span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="examProgress" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <div class="exam-question-content card-container">
                    <div class="question-number">第 <span id="examQuestionNum">1</span> 题</div>
                    <div class="question-type" id="examQuestionType">单选题</div>
                    <div class="question-text" id="examQuestionText">
                        以下哪项不属于可再生能源？
                    </div>
                </div>

                <div class="exam-question-options" id="examQuestionOptions">
                    <div class="option-item" data-value="A">
                        <div class="option-label">A</div>
                        <div class="option-text">太阳能</div>
                    </div>
                    <div class="option-item" data-value="B">
                        <div class="option-label">B</div>
                        <div class="option-text">风能</div>
                    </div>
                    <div class="option-item" data-value="C">
                        <div class="option-label">C</div>
                        <div class="option-text">煤炭</div>
                    </div>
                    <div class="option-item" data-value="D">
                        <div class="option-label">D</div>
                        <div class="option-text">水能</div>
                    </div>
                </div>

                <div class="exam-question-actions">
                    <button class="btn btn-secondary" id="examPrevBtn">上一题</button>
                    <button class="btn btn-warning" id="examSubmitBtn">提交试卷</button>
                    <button class="btn btn-secondary" id="examNextBtn">下一题</button>
                </div>

                <!-- 题目导航 -->
                <div class="question-navigator card-container">
                    <div class="navigator-title">题目导航</div>
                    <div class="question-grid" id="questionGrid">
                        <!-- 题目编号将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="tabbar" id="tabbar">
            <div class="tabbar-item active" data-tab="home">
                <div class="tabbar-icon">🏠</div>
                <div class="tabbar-text">首页</div>
            </div>
            <div class="tabbar-item" data-tab="practice">
                <div class="tabbar-icon">📖</div>
                <div class="tabbar-text">练习</div>
            </div>
            <div class="tabbar-item" data-tab="exam">
                <div class="tabbar-icon">✏️</div>
                <div class="tabbar-text">考试</div>
            </div>
            <div class="tabbar-item" data-tab="profile">
                <div class="tabbar-icon">👤</div>
                <div class="tabbar-text">我的</div>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
</body>
</html>
