# 使用Node.js作为基础镜像进行构建
FROM node:16 as builder

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制.env.production为.env文件
COPY .env.production .env

# 复制源代码
COPY . .

# 构建项目
RUN npm run build

# 使用nginx镜像部署
FROM nginx:alpine

# 复制构建产物到nginx目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置文件
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露80端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]