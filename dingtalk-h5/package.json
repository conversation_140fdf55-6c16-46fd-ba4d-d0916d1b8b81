{"name": "internal-ai-h5", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "build:debug": "vite build --mode debug", "preview": "vite preview"}, "dependencies": {"@vueuse/core": "^10.3.0", "axios": "^1.4.0", "dingtalk-h5-remote-debug": "^0.1.3", "dingtalk-jsapi": "^3.1.4", "html2canvas": "^1.4.1", "pinia": "^2.1.6", "vant": "^4.9.21", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@types/node": "^20.10.1", "@vitejs/plugin-vue": "^4.2.3", "postcss-px-to-viewport": "^1.1.1", "sass": "^1.89.2", "terser": "^5.43.1", "vite": "^5.4.19"}}