## 文档标题
考试刷题系统 H5 小程序（Vue3 + Vant4）模块说明书（最终版原型解析）

## 一、概述
- 目标平台与技术栈
  - 前端：Vue 3 + Vite + JavaScript
  - UI 组件库：Vant 4（Mobile）
  - 路由：Vue Router 4
  - 状态管理：Pinia
  - 网络：Axios（或等价库）
- 适配
  - 移动端 H5（小程序风格），375 设计宽度，1px 细边，适配 iOS/Android
  - 需要考虑底部安全区与状态栏（NavBar、Tabbar）
- 主题与风格
  - 主色：蓝色系（按钮/进度条），辅色：绿色（可用/正确）、红色（错误/警告）
  - 圆角卡片、阴影、分区标题、图标 Emoji 或 Iconfont

## 二、信息架构与全局设计
### 2.1 路由结构（建议）
- /home 首页（tab）
- /practice 练习科目列表（tab）
- /exam 考试列表（tab）
- /profile 我的（tab）
- /practice/:subjectId 练习详情（模式与章节）
- /practice/:subjectId/session 连续练习做题页
- /exam/:examId 考试详情
- /exam/:examId/session 考试进行页
- /history 历史记录
- /wrong 错题本
- /report 学习报告
- /feedback 意见反馈
- /settings 设置

说明：4 个主 Tab 路由常驻，其余为子页面路由，支持通过参数控制状态恢复。

### 2.2 全局组件
- AppNavBar 顶部导航（Vant NavBar 封装，支持返回/标题）
- AppTabbar 底部导航（Vant Tabbar）
- AppCard 卡片容器（样式封装 + slot）
- AppProgress 进度条（Vant Progress 封装）
- AppSkeleton/SkeletonList 加载骨架
- AppEmpty 空状态
- QuestionOptions 题目选项列表组件（支持单选/多选/判断）
- AIAnalysisPanel AI 解析组件（正确/错误、解析、知识点、错误分析）
- QuestionNavigator 题目导航宫格（考试页）

### 2.3 状态管理（Pinia）
- userStore
  - userInfo：id、name、level、avatar
  - stats：练习次数、考试次数、正确率
- subjectStore
  - subjects：[{ id, name, total, done, progress }]
  - chapters：按 subjectId 索引的章节进度
- practiceStore
  - currentSubjectId、mode（sequence/random/wrong）
  - session：id、进度、已作答数
  - questions 缓存、当前题指针、定时器状态、AI 解析
- examStore
  - exams 列表、状态（可参加/未开始/已结束）
  - currentExam：详情、规则
  - session：id、倒计时、答题进度、题目导航状态
- uiStore
  - loading、toast、dialog 控制
  - keepAlivePages 配置

### 2.4 网络与缓存
- 统一 axios 实例 + 拦截器（token 注入、错误处理）
- 本地缓存（localStorage/IndexedDB）
  - auth_token、userInfo
  - last_subject、last_practice_session、last_exam_session
  - 离线作答草稿（practice/exam 答案、进度）
- 自动保存策略
  - 练习：每次选项变更即刻保存草稿；提交答案后持久化
  - 考试：每题作答保存草稿；固定间隔心跳上报

### 2.5 鉴权与路由守卫
- 进入非公共路由（练习、考试、我的）需鉴权
- 守卫逻辑
  - 无 token → 跳转登录（如无登录页，可先用游客模式并持久化用户 ID）
  - 有未提交考试 session → 进入考试进行页
  - 有未结束练习 session → 进入练习做题页（可弹窗提醒）

## 三、页面与模块说明（按原型逐页解析）
### 3.1 首页（/home）
- 目的：概览个人信息、快速入口、学习进度、推荐练习
- 模块
  - 顶部导航：标题“考试刷题系统”
  - 用户信息卡片：头像、姓名、学习等级（LV.x）、统计（练习次数、考试次数、正确率）
  - 快速入口（Grid）：开始练习、模拟考试、错题复习、学习报告
  - 学习进度：科目列表（名称、已做/总数、进度条、已完成标记）
  - 推荐练习：卡片（AI 智能练习、重点突破、快速提分）
- 交互
  - 点击快速入口跳转对应页面
  - 点击进度项进入对应科目练习详情
- 数据
  - GET /api/user/me → userInfo, stats
  - GET /api/subjects/progress → subjects[]
  - GET /api/recommend → 推荐项
- Vant 组件映射
  - NavBar、Grid、Card、Progress、Button、Tag、Skeleton、Toast

### 3.2 练习页（/practice）
- 目的：选择练习科目
- 模块：科目列表（图标、名称、描述、进度条、百分比、箭头）
- 交互：点击进入 练习详情（/practice/:subjectId）
- 数据：GET /api/subjects/progress
- 异常：无科目→空状态；网络失败→重试按钮

### 3.3 考试页（/exam）
- 目的：选择考试
- 模块：考试卡片（标题、可参加状态、题量与时长、描述）
- 交互：点击进入 考试详情（/exam/:examId）
- 数据：GET /api/exams
- 状态：状态徽标（available/disable）

### 3.4 我的（/profile）
- 目的：个人中心聚合
- 模块：头像、昵称、等级；菜单项：历史记录、错题本、意见反馈、设置
- 交互：跳转 /history、/wrong、/feedback、/settings
- 数据：GET /api/user/me（与首页复用）

### 3.5 练习详情（/practice/:subjectId）
- 目的：选择练习模式或按章节练习；查看该科目概览
- 模块
  - 顶部卡片：科目名称、已做/总题、正确率/已练习统计
  - 练习模式：顺序练习、随机练习、错题练习
  - 章节列表：章节名、已做/总题、完成/进行中/未开始状态
- 交互
  - 选择模式 → 创建/恢复练习 session → 跳转做题页
  - 选择章节 → 指定章节练习（可作为 sequence 的子参数）
- 数据
  - GET /api/subjects/:id/summary（进度、统计）
  - GET /api/subjects/:id/chapters
  - POST /api/practice/sessions（创建/恢复）
- Vant：CellGroup、Cell、Tag、Button、Progress

### 3.6 考试详情（/exam/:examId）
- 目的：展示考试规则和信息，开始考试
- 模块：考试标题、题量/时长/类型；规则列表（时间、题型构成、自动保存、提交不可修改）
- 交互：点击“开始考试”→ 创建考试 session → 跳转考试进行页
- 数据
  - GET /api/exams/:id
  - POST /api/exam/sessions
- 约束：弹窗确认进入考试；确保网络可用

### 3.7 连续练习做题页（/practice/:subjectId/session）
- 目的：进行连续练习与 AI 解析
- 模块
  - 顶部：题目进度（当前/总）、进度条、计时（可选）
  - 题干卡片：题型（单选/多选/判断等）、题干
  - 选项区：A/B/C/D...，支持多选
  - 操作区：上一题、提交答案、下一题（提交后显示下一题）
  - AI 解析面板：显示正确/错误、答案解析、知识点标签、错误分析（错误时）
- 交互与状态
  - 选项交互：未提交前可更改；多选支持取消；提交前需至少选择一个
  - 提交后：锁定选项；展示解析；下一题可用
  - 上下题：自动保存草稿；已提交题再次查看保留解析
  - 题目边界：首题“上一题”禁用；末题“下一题”变“完成/返回”
- 数据与接口
  - GET /api/practice/sessions/:sid/questions?cursor=x（可分页/懒加载）
  - POST /api/practice/sessions/:sid/answers（提交当前题/批量）
  - GET /api/questions/:qid/analysis（或随提交返回）
- Vant：Radio/Checkbox、Button、Progress、Tag、Toast、Sticky、Skeleton

### 3.8 考试进行页（/exam/:examId/session）
- 目的：计时答题、可导航跳转、提交试卷
- 模块
  - 顶部信息条：考试名、倒计时（CountDown）
  - 进度区：已答/未答数、进度条
  - 题干区：题号、题型、题干
  - 选项区：与练习不同，默认不展示解析
  - 操作区：上一题、下一题、提交试卷
  - 题目导航：宫格显示 1..N，标记状态（未答/已答/当前/标记），可快速跳转
- 交互与限制
  - 不显示 AI 解析（考试态）
  - 倒计时结束自动提交
  - 提交前二次确认（Dialog）
- 数据与接口
  - GET /api/exam/sessions/:sid/questions?cursor=x
  - POST /api/exam/sessions/:sid/answers（保存作答）
  - POST /api/exam/sessions/:sid/submit
  - 心跳：POST /api/exam/sessions/:sid/heartbeat（可选）
- Vant：CountDown、Grid/Button、Dialog、Toast、Progress

### 3.9 其他页面（从入口可达，原型未详述）
- 历史记录（/history）
  - 列表：练习记录、考试记录，支持筛选/分页
  - GET /api/history?type=practice|exam
- 错题本（/wrong）
  - 维度：按科目/知识点
  - 支持“再练一次”进入错题练习模式
  - GET /api/wrong?subjectId= / GET /api/wrong/points
- 学习报告（/report）
  - 展示正确率、能力雷达、薄弱点、建议
  - GET /api/report/overview
- 意见反馈（/feedback）
  - 表单：文本、联系方式、图片上传（可选）
  - POST /api/feedback
- 设置（/settings）
  - 主题/字号/缓存清理、消息通知开关、关于
  - 本地配置项存储于 localStorage，服务端同步可选

## 四、题目与作答模型
- Question
  - id、type（single/multiple/judge/...）
  - stem（富文本/纯文本）、options：[{value,label,text}]
  - knowledgePoints：string[]、difficulty（可选）
- Answer
  - questionId、values（数组，单选长度 1）、isCorrect（可选）
  - submitAt、duration
- Analysis（练习态）
  - correctAnswer、explanation、knowledgePoints、errorAnalysis（当错误时）
- 导航状态（考试态）
  - status: not_answered | answered | current | flagged（可拓展）

交互规则
- 单选：选中即高亮，提交前允许切换
- 多选：多次点击选择/取消，提交前允许变更；提交时校验非空
- 判断：两个选项
- 提交错误处理：网络失败→保留草稿，用户可重试

## 五、计时与进度
- 练习计时：仅展示本次练习累计时长（可选）
- 考试计时：CountDown 必填；前端倒计时 + 服务端时间校验
- 进度条计算
  - 练习：当前题序/总题
  - 考试：已答/总题百分比

## 六、Vant4 组件映射速查
- 导航：NavBar, Tabbar, TabbarItem
- 列表/卡片：Cell, CellGroup, Card, List
- 表单：Radio, RadioGroup, Checkbox, CheckboxGroup, Field, Uploader
- 反馈：Toast, Dialog, Loading, Empty, Skeleton
- 展示：Progress, Tag, Grid, GridItem, CountDown, Divider, Sticky
- 其他：PullRefresh, Pagination（如需）

## 七、错误状态与边界场景
- 未登录：拦截进入，提示登录/游客模式
- 无网络：Toast + 重试；考试进行中提示弱网，保存到本地草稿
- 无数据：空状态页（subjects/exams/history）
- session 丢失：提供“恢复/重开”选项
- 倒计时结束：自动提交 + 提示提交结果
- 练习最后一题：下一题按钮变“完成/返回”，回到上页或给出总结页（可选）

## 八、性能与体验
- 懒加载路由与分包
- 题目与选项的局部更新，避免整页重渲染
- 列表虚拟化（历史/错题等大列表时）
- 预取下一题数据；作答节流保存
- 图片/富文本题干的懒加载与压缩

## 九、埋点与日志（可选）
- PV/UV：各页面曝光
- 点击：快速入口、模式选择、开始考试、提交
- 作答：选项变更、提交、解析查看
- 异常：接口失败、超时、自动提交触发

## 十、目录与代码组织（建议）
- src/
  - pages/: HomePage.vue, PracticePage.vue, ExamPage.vue, ProfilePage.vue, PracticeDetailPage.vue, ExamDetailPage.vue, PracticeQuestionPage.vue, ExamQuestionPage.vue, HistoryPage.vue, WrongBookPage.vue, ReportPage.vue, FeedbackPage.vue, SettingsPage.vue
  - components/: AppNavBar.vue, AppCard.vue, AppProgress.vue, QuestionOptions.vue, AIAnalysisPanel.vue, QuestionNavigator.vue, AppEmpty.vue, AppSkeleton.vue
  - stores/: user.ts, subject.ts, practice.ts, exam.ts, ui.ts
  - router/: index.ts
  - services/: http.ts, user.ts, subject.ts, practice.ts, exam.ts, report.ts, feedback.ts
  - models/: index.ts（TS 接口）
  - utils/: time.ts, storage.ts, validator.ts
  - styles/: variables.less, mixins.less, global.less

## 十一、接口清单（示例）
- 用户
  - GET /api/user/me → { id,name,level,avatar,stats:{practiceCount,examCount,accuracy} }
- 科目/练习
  - GET /api/subjects/progress → [{ id,name,total,done,progress }]
  - GET /api/subjects/:id/summary → { total,done,accuracy,practiced }
  - GET /api/subjects/:id/chapters → [{ id,name,total,done,progress,status }]
  - POST /api/practice/sessions → { sessionId, subjectId, mode, total }
  - GET /api/practice/sessions/:sid/questions?cursor= → { list:[Question], nextCursor }
  - POST /api/practice/sessions/:sid/answers → { questionId, isCorrect, analysis? }
- 考试
  - GET /api/exams → [{ id,title,available,questionCount,duration,desc }]
  - GET /api/exams/:id → { id,title,questionCount,duration,desc,rules }
  - POST /api/exam/sessions → { sessionId, examId, total, endAt }
  - GET /api/exam/sessions/:sid/questions?cursor= → { list:[Question], nextCursor }
  - POST /api/exam/sessions/:sid/answers → 204
  - POST /api/exam/sessions/:sid/submit → { score,accuracy,rank? }
- 其他
  - GET /api/history?type=practice|exam
  - GET /api/wrong?subjectId=
  - GET /api/report/overview
  - POST /api/feedback

说明：返回结构与字段可按后端最终定义微调，前端需做空值与异常兼容。

## 十二、验证点与测试建议
- 路由跳转正确，Tabbar 高亮与返回逻辑符合原型
- 练习模式下：提交按钮状态联动；AI 解析显示逻辑正确（错误时展示“错误分析”）
- 考试模式下：不显示解析；倒计时归零自动提交；题目导航状态正确
- 进度条与计数：和题目指针/已答数量一致
- 异常断网：草稿保存与恢复可用
- 性能：题目切换不卡顿，页面首屏 < 2s（本地）

## 十三、待确认事项（请和甲方/后端确认）
- 是否存在登录/注册流程？当前是否允许游客模式？
- AI 解析来源：前端静态/后端返回/在线大模型接口（计费与时延）
- 题型是否包含填空、简答？如果有，作答与判分逻辑？
- 考试规则：是否允许返回上一题？是否允许标记题目？
- 学习报告展示形式与指标维度（是否需要图表）
- 错题本维度：按科目/知识点/时间；是否支持移除错题
- 是否需要“交卷前校验未答题目提醒”
- 接口的分页策略与限流/重试策略

（完）

