import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')
  
  // 判断是否为调试模式
  const isDebug = mode === 'debug' || env.VITE_DEBUG === 'true'
  
  return {
    base: '/',
    plugins: [vue()],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler'  // 使用现代编译器 API
        }
      },
      postcss: {
        plugins: [
          require('postcss-px-to-viewport')({
            viewportWidth: 375,
            unitPrecision: 6,
            viewportUnit: 'vw',
            minPixelValue: 1,
            selectorBlackList: ['.ignore', '.hairlines'],
            exclude: [/node_modules/]
          })
        ]
      }
    },
    // 构建配置 - 根据环境动态调整
    build: {
      target: 'es2015', // 设置目标为ES2015，兼容Chrome 69，包含Promise.allSettled polyfill
      sourcemap: isDebug, // 调试模式下生成sourcemap
      minify: isDebug ? false : 'terser', // 调试模式下不压缩代码
      terserOptions: isDebug ? {} : {
        compress: {
          drop_console: false, // 保留console.log便于调试
          drop_debugger: false // 保留debugger语句
        }
      },
      rollupOptions: {
        output: {
          // 自定义输出文件名，便于识别
          chunkFileNames: isDebug 
            ? 'assets/js/[name].js' 
            : 'assets/js/[name]-[hash].js',
          entryFileNames: isDebug 
            ? 'assets/js/[name].js' 
            : 'assets/js/[name]-[hash].js',
          assetFileNames: isDebug 
            ? 'assets/[ext]/[name].[ext]' 
            : 'assets/[ext]/[name]-[hash].[ext]'
        }
      }
    },
    server: {
      host: '0.0.0.0',
      port: 3000,
      headers: {
        'Access-Control-Allow-Origin': '*'
      },
      proxy: {
        '/api': {
          target: 'http://127.0.0.1:8080',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        },
        '/minio': {
          target: 'http://127.0.0.1:9000',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/minio/, '')
        }
      }
    }
  }
})
