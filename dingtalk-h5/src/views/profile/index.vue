<template>
  <div class="page-profile">
    <!-- 个人信息头部 -->
    <div class="profile-header">
      <div class="profile-avatar">
        <svg class="icon" viewBox="0 0 1024 1024">
          <path d="M512 74.667C270.933 74.667 74.667 270.933 74.667 512S270.933 949.333 512 949.333 949.333 753.067 949.333 512 753.067 74.667 512 74.667zM512 256c70.4 0 128 57.6 128 128s-57.6 128-128 128-128-57.6-128-128 57.6-128 128-128z m0 576c-105.6 0-201.6-54.4-256-140.8 1.067-85.333 170.667-132.267 256-132.267s254.933 46.933 256 132.267C713.6 777.6 617.6 832 512 832z"/>
        </svg>
      </div>
      <div class="profile-info">
        <div class="profile-name">{{ userData?.name || '用户' }}</div>
        <div class="profile-level">{{ userData?.level || 'LV.1' }}</div>
      </div>
    </div>

    <!-- 功能菜单列表 -->
    <div class="menu-list">
      <van-cell-group>
        <van-cell
          title="历史记录"
          is-link
          @click="handleMenuClick('history')"
        >
          <template #icon>
            <div class="menu-icon">📋</div>
          </template>
        </van-cell>
        <van-cell
          title="错题本"
          is-link
          @click="handleMenuClick('wrong')"
        >
          <template #icon>
            <div class="menu-icon">❌</div>
          </template>
        </van-cell>
        <van-cell
          title="意见反馈"
          is-link
          @click="handleMenuClick('feedback')"
        >
          <template #icon>
            <div class="menu-icon">💬</div>
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 意见反馈弹窗 -->
    <van-dialog
      v-model:show="feedbackVisible"
      title="意见反馈"
      show-cancel-button
      :before-close="handleDialogClose"
      @confirm="submitFeedback"
      @cancel="cancelFeedback"
    >
      <van-form ref="feedbackFormRef" :model="feedbackForm" :rules="feedbackRules">
        <van-field
          v-model="feedbackForm.type"
          name="type"
          label="反馈类型"
          placeholder="请选择反馈类型"
          readonly
          is-link
          @click="handleTypeClick"
        />
        <van-field
          v-model="feedbackForm.title"
          name="title"
          label="反馈标题"
          placeholder="请输入反馈标题"
          maxlength="200"
          show-word-limit
        />
        <van-field
          v-model="feedbackForm.content"
          name="content"
          label="反馈内容"
          type="textarea"
          placeholder="请详细描述您的问题或建议"
          rows="4"
          maxlength="1000"
          show-word-limit
        />
        <van-field
          v-model="feedbackForm.priority"
          name="priority"
          label="优先级"
          placeholder="请选择优先级"
          readonly
          is-link
          @click="handlePriorityClick"
        />
      </van-form>
    </van-dialog>

    <!-- 反馈类型选择器 -->
    <van-popup v-model:show="showTypePicker" position="bottom">
      <van-picker
        :columns="typeOptions"
        @confirm="onTypeConfirm"
        @cancel="() => showTypePicker = false"
      />
    </van-popup>

    <!-- 优先级选择器 -->
    <van-popup v-model:show="showPriorityPicker" position="bottom">
      <van-picker
        :columns="priorityOptions"
        @confirm="onPriorityConfirm"
        @cancel="() => showPriorityPicker = false"
      />
    </van-popup>

    <!-- 加载状态 -->
    <van-skeleton v-if="loading" :row="6" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { http } from '@/utils/http'
import { showFailToast, showSuccessToast } from 'vant'

const router = useRouter()

// 响应式数据
const userData = ref(null)
const loading = ref(true)
const feedbackVisible = ref(false)
const showTypePicker = ref(false)
const showPriorityPicker = ref(false)
const feedbackFormRef = ref(null)

// 意见反馈表单数据
const feedbackForm = ref({
  type: '',
  title: '',
  content: '',
  priority: '中'
})

// 表单验证规则
const feedbackRules = {
  type: [{ required: true, message: '请选择反馈类型' }],
  title: [
    { required: true, message: '请输入反馈标题' },
    { max: 200, message: '标题不能超过200个字符' }
  ],
  content: [
    { required: true, message: '请输入反馈内容' },
    { max: 1000, message: '内容不能超过1000个字符' }
  ]
}

// 选择器选项
const typeOptions = [
  { text: 'Bug报告', value: 'Bug报告' },
  { text: '功能建议', value: '功能建议' },
  { text: '投诉', value: '投诉' },
  { text: '其他', value: '其他' }
]

const priorityOptions = [
  { text: '低', value: '低' },
  { text: '中', value: '中' },
  { text: '高', value: '高' },
  { text: '紧急', value: '紧急' }
]

// 菜单点击处理
const handleMenuClick = (action) => {
  switch (action) {
    case 'history':
      router.push('/history')
      break
    case 'wrong':
      router.push('/wrong')
      break
    case 'feedback':
      openFeedbackDialog()
      break
  }
}

// 打开意见反馈弹窗
const openFeedbackDialog = () => {
  // 重置表单
  feedbackForm.value = {
    type: '',
    title: '',
    content: '',
    priority: '中'
  }
  feedbackVisible.value = true
}

// 点击反馈类型字段
const handleTypeClick = () => {
  showTypePicker.value = true
}

// 点击优先级字段
const handlePriorityClick = () => {
  showPriorityPicker.value = true
}

// 反馈类型选择确认
const onTypeConfirm = ({ selectedOptions }) => {
  feedbackForm.value.type = selectedOptions[0]?.value || selectedOptions[0]
  showTypePicker.value = false
}

// 优先级选择确认
const onPriorityConfirm = ({ selectedOptions }) => {
  feedbackForm.value.priority = selectedOptions[0]?.value || selectedOptions[0]
  showPriorityPicker.value = false
}

// 弹窗关闭前处理
const handleDialogClose = (action) => {
  if (action === 'confirm') {
    return false // 阻止默认关闭，由submitFeedback处理
  }
  return true // 允许取消关闭
}

// 取消反馈
const cancelFeedback = () => {
  feedbackVisible.value = false
}

// 提交意见反馈
const submitFeedback = async () => {
  try {
    // 表单验证
    await feedbackFormRef.value?.validate()

    // 提交数据
    const submitData = {
      type: feedbackForm.value.type,
      title: feedbackForm.value.title,
      content: feedbackForm.value.content,
      priority: feedbackForm.value.priority
    }

    await http.post('/api/dingapp/feedback', submitData)

    // 提交成功
    showSuccessToast('反馈提交成功，感谢您的建议！')
    feedbackVisible.value = false

  } catch (error) {
    if (error.name !== 'ValidationError') {
      console.error('提交反馈失败:', error)
      showFailToast('提交失败，请稍后重试')
    }
  }
}

// 页面初始化
onMounted(async () => {
  try {
    loading.value = true
    const user = await http.get('/api/dingapp/user/me')
    if (user.data) {
      userData.value = user.data
    } else {
      userData.value = user
    }
  } catch (error) {
    console.error('用户信息加载失败:', error)
    showFailToast('用户信息加载失败')
  } finally {
    loading.value = false
  }
})
</script>

<style lang="scss" scoped>
.page-profile {
  padding: 16px;
  padding-bottom: 80px;
  background-color: #f7f8fa;
  min-height: 100vh;
}

// 个人信息头部
.profile-header {
  background: white;
  border-radius: 12px;
  padding: 24px 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;

  .profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;

    .icon {
      width: 40px;
      height: 40px;
      fill: white;
    }
  }

  .profile-info {
    flex: 1;

    .profile-name {
      font-size: 24px;
      font-weight: 600;
      color: #323233;
      margin-bottom: 8px;
    }

    .profile-level {
      font-size: 16px;
      color: #969799;
    }
  }
}

// 功能菜单列表
.menu-list {
  .menu-icon {
    font-size: 20px;
    margin-right: 12px;
    width: 24px;
    text-align: center;
  }

  :deep(.van-cell-group) {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }

  :deep(.van-cell) {
    padding: 16px;
    font-size: 16px;

    &:not(:last-child) {
      border-bottom: 1px solid #f7f8fa;
    }

    .van-cell__title {
      font-weight: 500;
    }
  }
}

// 意见反馈弹窗样式
:deep(.van-dialog) {
  border-radius: 12px;

  .van-dialog__header {
    padding: 20px 20px 0;
    font-size: 18px;
    font-weight: 600;
  }

  .van-dialog__content {
    padding: 20px;
  }

  .van-form {
    .van-field {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .van-field__label {
      font-weight: 500;
      color: #323233;
    }
  }
}

// 选择器弹窗样式
:deep(.van-popup) {
  border-radius: 12px 12px 0 0;
}

:deep(.van-picker) {
  .van-picker__toolbar {
    padding: 16px 20px;
  }

  .van-picker__confirm {
    color: #1989fa;
    font-weight: 600;
  }

  .van-picker__cancel {
    color: #969799;
  }
}
</style>

