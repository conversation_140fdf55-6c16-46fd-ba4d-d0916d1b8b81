<template>
  <div class="login-container">
    <div class="login-box">
      <img src="/src/assets/gzyc.svg" alt="logo" class="logo">
      <h2>刷题系统系统</h2>
      <van-button type="primary" block @click="handleLogin" :loading="loading">钉钉单点登录</van-button>
    </div>
    <van-dialog v-model:show="loginFailDialog" title="登录失败" :show-cancel-button="false" @confirm="handleFailDialogClose">
      <p>登录失败，请稍后重试</p>
    </van-dialog>
  </div>
</template>

<script setup>
import {onMounted, ref} from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)
const loginFailDialog = ref(false)
const uid = ref('');
const hostURL = "https://172-16-219-19031988-akm3tyf4p728t3.ztna-dingtalk.com"

onMounted(() => {
    // 从 URL 中读取 uid
    const urlParams = new URLSearchParams(window.location.search);
    uid.value = urlParams.get('uid');

    if (!uid.value) {
        // handleClick()
    } else {
        try {
            loading.value = true
            userStore.loginByUid(uid.value)
        } catch (error) {
            console.error('登录失败:', error)
            loginFailDialog.value = true
            loading.value = false
        } finally {
            loading.value = false
        }
    }
})



function handleClick() {
    window.location.href = '/api/dingapp/user/render';
}

const handleLogin = async () => {
  loading.value = true
  try {
    await userStore.login()
    // 登录成功后的跳转由handleLoginSuccess函数处理
  } catch (error) {
    console.error('登录失败:', error)
    loginFailDialog.value = true
    loading.value = false
  }
}

const handleFailDialogClose = () => {
  loginFailDialog.value = false
}
</script>

<style lang="scss" scoped>
.login-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f7f8fa;

  .login-box {
    width: 80%;
    max-width: 300px;
    text-align: center;

    .logo {
      width: 80px;
      height: 80px;
      margin-bottom: 20px;
    }

    h2 {
      margin-bottom: 30px;
      color: #323233;
      font-size: 20px;
    }
  }
}
</style>
