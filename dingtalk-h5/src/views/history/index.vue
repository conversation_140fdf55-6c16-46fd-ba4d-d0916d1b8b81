<template>
  <div class="page-history">
    <div class="header">历史记录</div>
    <van-tabs v-model:active="active">
      <van-tab title="练习">
        <van-list>
          <van-cell v-for="item in practiceList" :key="'p-'+item.id"
                    :title="item.title"
                    :label="`开始：${fmt(item.startTime)}  结束：${fmt(item.endTime)}  题量：${item.total} 已作答：${item.done}`" />
        </van-list>
      </van-tab>
      <van-tab title="考试">
        <van-list>
          <van-cell v-for="item in examList" :key="'e-'+item.id"
                    :title="item.title"
                    :label="`开始：${fmt(item.startTime)}  结束：${fmt(item.endTime)}  题量：${item.total} 正确率：${(item.accuracy??0).toFixed(1)}%`" />
        </van-list>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { http } from '@/utils/http'

const active = ref(0)
const practiceList = ref([])
const examList = ref([])

const fmt = (t) => t ? new Date(t).toLocaleString() : '-'

const load = async () => {
  const all = await http.get('/api/dingapp/history')
  practiceList.value = all.filter(x => x.type === 'practice')
  examList.value = all.filter(x => x.type === 'exam')
}

onMounted(load)
</script>

<style scoped>
.page-history { padding-bottom: 60px; }
.header { font-size: 18px; padding: 12px 16px; }
</style>

