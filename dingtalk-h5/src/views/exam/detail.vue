<template>
    <div>
        <van-nav-bar title="考试详情" left-arrow @click-left="goBack"/>
        <div class="page-exam-detail">
            <div class="exam-header card-container">
                <div class="exam-title">{{ examDetail.name || '考试详情' }}</div>
                <div class="exam-info">
                    <div class="exam-detail">
                        <span>📝 {{ examDetail.questionCount || 0 }}题</span>
                        <span>⏰ {{ examDetail.durationMinutes || 0 }}分钟</span>
                        <span>📊 综合测试</span>
                    </div>
                    <div class="exam-desc">{{ examDetail.description || '' }}</div>
                </div>
            </div>

            <div class="exam-rules card-container">
                <div class="section-title">考试须知</div>
                <div class="rules-list">
                    <div class="rule-item">
                        <span class="rule-icon">⏰</span>
                        <span class="rule-text">考试时间为{{ examDetail.durationMinutes || 120 }}分钟，请合理安排答题时间</span>
                    </div>
                    <div class="rule-item">
                        <span class="rule-icon">📝</span>
                        <span class="rule-text">考试过程中可以随时保存答案，避免意外丢失</span>
                    </div>
                    <div class="rule-item">
                        <span class="rule-icon">🔄</span>
                        <span class="rule-text">可以返回修改之前的答案，合理利用考试时间</span>
                    </div>
                    <div class="rule-item">
                        <span class="rule-icon">✅</span>
                        <span class="rule-text">提交后不可修改，请仔细检查后再提交</span>
                    </div>
                </div>
            </div>

            <div class="exam-actions">
                <van-button type="primary"
                            size="large"
                            block
                            :loading="loading"
                            @click="startExam">
                    开始考试
                </van-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import {onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {showFailToast} from 'vant'
import {http} from '@/utils/http'

const route = useRoute()
const router = useRouter()
const paperId = route.params.paperId

const examDetail = ref({})
const loading = ref(false)

const goBack = () => {
    router.push('/exam')
}
// 加载考试详情
const loadExamDetail = async () => {
    try {
        const res = await http.get(`/api/dingapp/exams/${paperId}`)
        console.log("loadExamDetail", res)
        if (res.data) {
            examDetail.value = res.data
        }
    } catch (error) {
        showFailToast('加载考试详情失败')
        console.error('加载考试详情失败:', error)
    }
}

const startExam = async () => {
    if (loading.value) return

    loading.value = true
    try {
        // 跳转到考试会话页面
        router.push(`/exam/${paperId}/session`)
    } catch (error) {
        showFailToast('启动考试失败')
        console.error('启动考试失败:', error)
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    loadExamDetail()
})
</script>

<style scoped>
.page-exam-detail {
    padding: 16px;
    padding-bottom: 100px;
}

.exam-header {
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.exam-header .exam-title {
    font-size: 20px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 12px;
}

.exam-header .exam-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.exam-header .exam-detail {
    display: flex;
    gap: 16px;
    font-size: 14px;
    color: #646566;
}

.exam-header .exam-desc {
    font-size: 14px;
    color: #969799;
}

.exam-rules {
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 16px;
}

.rules-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.rule-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.rule-icon {
    font-size: 16px;
    margin-top: 2px;
    flex-shrink: 0;
}

.rule-text {
    font-size: 14px;
    color: #646566;
    line-height: 1.5;
}

.exam-actions {
    position: fixed;
    bottom: 20px;
    left: 16px;
    right: 16px;
    z-index: 100;
}


</style>
