<template>
  <div>
      <van-nav-bar title="考试进行中" left-arrow @click-left="goBack" />
      <div class="page-exam-session">
          <!-- 考试进度头部 -->
          <div class="exam-progress-header">
              <div class="exam-info-bar">
                  <div class="exam-title-small">考试进行中</div>
                  <div class="exam-timer">{{ remainText }}</div>
              </div>
              <div class="exam-progress">
                  <div class="progress-stats">
                      <span>已答：<span>{{ answeredCount }}</span></span>
                      <span>未答：<span>{{ unansweredCount }}</span></span>
                  </div>
                  <div class="progress-bar">
                      <div class="progress-fill" :style="{ width: progressPercent + '%' }"></div>
                  </div>
              </div>
          </div>

          <!-- 题目导航 -->
<!--          <div class="question-navigator card-container">-->
<!--              <div class="navigator-title">题目导航</div>-->
<!--              <div class="question-grid">-->
<!--                  <div class="question-nav-item"-->
<!--                       v-for="navIndex in total"-->
<!--                       :key="navIndex - 1"-->
<!--                       :class="getNavItemClass(navIndex - 1)"-->
<!--                       @click="jumpToQuestion(navIndex - 1)">-->
<!--                      {{ navIndex }}-->
<!--                  </div>-->
<!--              </div>-->
<!--          </div>-->

          <!-- 题目内容 -->
          <div v-if="question" class="exam-question-content card-container">
              <div class="question-number">第 {{ index + 1 }} 题</div>
              <div class="question-type">{{ getQuestionType(question.type) }}</div>
              <div class="question-text">{{ question.stem }}</div>
          </div>

          <!-- 题目选项 -->
          <div v-if="question" class="exam-question-options">
              <template v-for="opt in question.options" :key="opt.value">
                  <div class="option-item"
                       :class="{ selected: isOptionSelected(opt.value) }"
                       @click="selectOption(opt.value)">
                      <div class="option-label">{{ opt.label }}</div>
                      <div class="option-text">{{ opt.text }}</div>
                  </div>
              </template>
          </div>
      </div>
      <!-- 操作按钮 -->
      <div class="exam-question-actions">
          <van-button v-if="!isFirstQuestion" type="default" @click="prev">上一题</van-button>
          <van-button type="warning" @click="submit">提交试卷</van-button>
          <van-button v-if="!isLastQuestion" type="primary" @click="next">下一题</van-button>
      </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showConfirmDialog } from 'vant'
import { http } from '@/utils/http'

const route = useRoute()
const router = useRouter()
const paperId = route.params.paperId

// 会话
const sessionId = ref(null)
const pageSize = 1
const index = ref(0)
const total = ref(0)
const question = ref(null)
const nextCursor = ref(0)
const expireTime = ref(null)
const currentTime = ref(Date.now())
let timer = null

// 答案和导航
const answerMap = reactive({})
const singleAnswer = ref('')
const questionNavList = ref([])
const allAnswers = reactive({}) // 存储所有题目的答案

const isMulti = computed(() => {
  if (!question.value) return false
  return (question.value.type && /多选/i.test(question.value.type))
})

// 计算统计数据
const answeredCount = computed(() => {
  return Object.keys(allAnswers).filter(qid => {
    const answer = allAnswers[qid]
    return answer && answer.length > 0
  }).length
})

const unansweredCount = computed(() => {
  return total.value - answeredCount.value
})

const progressPercent = computed(() => {
  return total.value > 0 ? Math.round((answeredCount.value / total.value) * 100) : 0
})

const isFirstQuestion = computed(() => {
  return index.value <= 0
})

const isLastQuestion = computed(() => {
  return index.value >= total.value - 1
})

const remainText = computed(() => {
  if (!expireTime.value) return '-'
  const now = currentTime.value
  const left = new Date(expireTime.value).getTime() - now
  if (left <= 0) return '已截止'
  const m = Math.floor(left / 60000)
  const s = Math.floor((left % 60000) / 1000)
  return `${m}分${s}秒`
})

const goBack = () => {
    if (timer) clearInterval(timer)

    // 弹出确认框
    showConfirmDialog({
        title: '退出考试',
        message: '确定要退出当前考试吗？考试进度将会清空。',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
    }).then(() => {
        // 用户确认退出，跳转到 exam 页面
        router.push('/exam')
    }).catch(() => {
        // 用户取消，不执行任何操作
    })
}

const createSession = async () => {
  const res = await http.post('/api/dingapp/exam/sessions', { paperId, pageSize })
    console.log("createSession", res)
    if (res.data) {
        sessionId.value = res.data.sessionId
        expireTime.value = res.data.endAt
        total.value = res.data.total || 0

        // 初始化完整的题目导航列表
        questionNavList.value = Array.from({ length: total.value }, (_, i) => ({
          id: null, // 题目ID将在访问时填充
          index: i,
          visited: false // 标记是否已访问
        }))
    }
  startTimer()
  await loadQuestion(0)
}

const startTimer = () => {
  if (timer) clearInterval(timer)
  timer = setInterval(() => {
    // 更新当前时间，触发响应式更新
    currentTime.value = Date.now()

    if (expireTime.value) {
      const left = new Date(expireTime.value).getTime() - currentTime.value
      if (left <= 0) {
        // 时间到了，自动提交
        clearInterval(timer)
        submit()
      }
    }
  }, 1000)
}

const loadQuestion = async (cursor) => {
  const res = await http.get(`/api/dingapp/exam/sessions/${sessionId.value}/questions`, { params: { cursor, limit: pageSize } })
    console.log("loadQuestion", res)
    if (res.data) {
        const page = res.data
        question.value = page.list && page.list[0]
        nextCursor.value = page.nextCursor
    }
  index.value = cursor

  // 更新题目导航列表
  if (question.value && questionNavList.value[cursor]) {
    questionNavList.value[cursor].id = question.value.id
    questionNavList.value[cursor].visited = true
  }

  // 恢复答案状态
  if (question.value) {
    const savedAnswer = allAnswers[question.value.id]
    if (savedAnswer && savedAnswer.length > 0) {
      if (isMulti.value) {
        answerMap[question.value.id] = [...savedAnswer]
      } else {
        singleAnswer.value = savedAnswer[0] || ''
      }
    } else {
      singleAnswer.value = ''
      if (!answerMap[question.value.id]) {
        answerMap[question.value.id] = []
      }
    }
  }
}

const prev = async () => {
  if (index.value <= 0) return
  await loadQuestion(index.value - 1)
}
const next = async () => {
  if (nextCursor.value == null) return
  await loadQuestion(nextCursor.value)
}

// 新增的功能函数
const getQuestionType = (type) => {
  if (!type) return '单选题'
  const typeStr = String(type).toLowerCase()
  if (typeStr.includes('多选')) return '多选题'
  if (typeStr.includes('单选')) return '单选题'
  if (typeStr.includes('判断')) return '判断题'
  return '单选题'
}

const selectOption = (optionValue) => {
  if (!question.value) return

  if (isMulti.value) {
    // 多选题处理
    if (!answerMap[question.value.id]) {
      answerMap[question.value.id] = []
    }
    const currentAnswers = answerMap[question.value.id]
    const index = currentAnswers.indexOf(optionValue)
    if (index > -1) {
      currentAnswers.splice(index, 1)
    } else {
      currentAnswers.push(optionValue)
    }
    allAnswers[question.value.id] = [...currentAnswers]
  } else {
    // 单选题处理
    singleAnswer.value = optionValue
    allAnswers[question.value.id] = [optionValue]
  }

  // 自动保存答案
  saveCurrentAnswer()
}

const isOptionSelected = (optionValue) => {
  if (!question.value) return false

  if (isMulti.value) {
    return answerMap[question.value.id]?.includes(optionValue) || false
  } else {
    return singleAnswer.value === optionValue
  }
}

const saveCurrentAnswer = async () => {
  if (!question.value) return

  try {
    const values = isMulti.value
      ? (answerMap[question.value.id] || [])
      : (singleAnswer.value ? [singleAnswer.value] : [])

    await http.post(`/api/dingapp/exam/sessions/${sessionId.value}/answers`, [{
      questionId: question.value.id,
      values,
      durationSec: 0
    }])
  } catch (error) {
    console.error('保存答案失败:', error)
  }
}

const jumpToQuestion = async (targetIndex) => {
  if (targetIndex === index.value) return
  await loadQuestion(targetIndex)
}

const getNavItemClass = (navIndex) => {
  const classes = ['question-nav-item']

  if (navIndex === index.value) {
    classes.push('current')
      return classes.join(' ')
  }

  // 检查是否已访问过该题目
  const navItem = questionNavList.value[navIndex]
  if (navItem && navItem.visited && navItem.id) {
    // 检查是否已答题
    const hasAnswer = allAnswers[navItem.id]
    if (hasAnswer && hasAnswer.length > 0) {
      classes.push('answered')
    } else {
      classes.push('unvisited')
    }
  } else {
    classes.push('unvisited')
  }

  return classes.join(' ')
}

const submit = async () => {
  try {
    const unanswered = unansweredCount.value
    let message = '确定要提交试卷吗？提交后将无法修改答案。'
    if (unanswered > 0) {
      message = `还有${unanswered}道题未作答，确定要提交试卷吗？`
    }

    await showConfirmDialog({
      title: '提交确认',
      message
    })

    const res = await http.post(`/api/dingapp/exam/sessions/${sessionId.value}/submit`, {})
      console.log("submit", res)
      if (res.data) {
          if (res.data && res.data.sessionId) {
              router.replace(`/exam/${paperId}/result?sessionId=${res.data.sessionId}`)
          }
      }
  } catch (error) {
    // 用户取消提交或其他错误
    console.log('提交取消或失败:', error)
  }
}

onMounted(createSession)
onBeforeUnmount(() => { if (timer) clearInterval(timer) })
</script>

<style scoped>
.page-exam-session {
  padding-bottom: 120px;
}

/* 考试进度头部 */
.exam-progress-header {
  padding: 16px;
  background: #fff;
  border-bottom: 1px solid #ebedf0;
  margin-bottom: 16px;
  position: sticky;
  top: 0;
  z-index: 999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.exam-info-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.exam-title-small {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.exam-timer {
  font-size: 18px;
  font-weight: 600;
  color: #ff4d4f;
}

.exam-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
}

.progress-bar {
  height: 4px;
  background: #f5f5f5;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #1989fa;
  transition: width 0.3s ease;
}

/* 题目内容 */
.exam-question-content {
  margin: 0 16px 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.question-number {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.question-type {
  display: inline-block;
  padding: 2px 8px;
  background: #e8f4ff;
  color: #1989fa;
  font-size: 12px;
  border-radius: 4px;
  margin-bottom: 12px;
}

.question-text {
  font-size: 16px;
  color: #323233;
  line-height: 1.6;
}

/* 题目选项 */
.exam-question-options {
  margin: 0 16px 20px;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 16px;
  margin-bottom: 12px;
  background: #fff;
  border: 1px solid #ebedf0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.option-item:hover {
  border-color: #1989fa;
}

.option-item.selected {
  border-color: #1989fa;
  background: #e8f4ff;
}

.option-label {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #f7f8fa;
  color: #646566;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  margin-right: 12px;
  flex-shrink: 0;
}

.option-item.selected .option-label {
  background: #1989fa;
  color: #fff;
}

.option-text {
  font-size: 15px;
  color: #323233;
  line-height: 1.5;
}

/* 操作按钮 */
.exam-question-actions {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  padding: 0 16px 20px;
}

.exam-question-actions .van-button {
  flex: 1;
}

/* 题目导航 */
.question-navigator {
  margin: 0 16px 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.navigator-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16px;
}

.question-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
}

.question-nav-item {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f7f8fa;
  color: #646566;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.question-nav-item:hover {
  background: #e8f4ff;
  color: #1989fa;
}

.question-nav-item.current {
  background: #1989fa;
  color: #fff;
}

.question-nav-item.answered {
  background: #52c41a;
  color: #fff;
}

.question-nav-item.answered.current {
  background: #1989fa;
}

.question-nav-item.visited {
  background: #ffa940;
  color: #fff;
}

.question-nav-item.visited.current {
  background: #1989fa;
}

.question-nav-item.unvisited {
  background: #f7f8fa;
  color: #646566;
}


</style>

