<template>
  <div class="page-exam">
    <div class="section-title">选择考试</div>
    <div class="exam-list">
      <div class="exam-item card-container"
           v-for="item in list"
           :key="item.paperId"
           @click="goToExamDetail(item)">
        <div class="exam-header">
          <div class="exam-title">{{ item.title }}</div>
          <div class="exam-status" :class="getStatusClass(item)">
            {{ getStatusText(item) }}
          </div>
        </div>
        <div class="exam-info">
          <div class="exam-detail">
            <span>题目数量：{{ item.questionCount }}题</span>
            <span>考试时长：{{ item.duration }}分钟</span>
          </div>
          <div class="exam-desc">{{ item.desc || '' }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { http } from '@/utils/http'

const list = ref([])
const router = useRouter()

const goToExamDetail = (item) => {
  router.push(`/exam/${item.paperId}/detail`)
  // router.push(`/exam/1962707466414112770/result?sessionId=1963537977337020417`)
}

const getStatusClass = (item) => {
  return item.available ? 'available' : 'unavailable'
}

const getStatusText = (item) => {
  return item.available ? '可参加' : '已结束'
}

onMounted(async () => {
    const res = await http.get('/api/dingapp/exams');
    console.log("res", res)
    if (res.data) {
        list.value = res.data
    }
})
</script>

<style scoped>
.page-exam {
  padding: 16px;
  padding-bottom: 80px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16px;
}

.exam-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.exam-item {
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.exam-item:active {
  transform: scale(0.98);
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.exam-title {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.exam-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.exam-status.available {
  background: #e8f5e8;
  color: #07c160;
}

.exam-status.unavailable {
  background: #f7f8fa;
  color: #969799;
}

.exam-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.exam-detail {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #969799;
}

.exam-desc {
  font-size: 14px;
  color: #646566;
}


</style>

