<template>
    <div class="page-exam-result">
        <div class="header">考试成绩</div>
        <van-cell-group inset>
            <van-cell title="试卷名称" :value="detail?.paperName || '-'"/>
            <van-cell title="正确率" :value="fmt(detail?.accuracy)"/>
            <van-cell title="分数" :value="detail?.score ?? 0"/>
            <van-cell title="用时" :value="`${detail?.durationSec ?? 0} 秒`"/>
            <van-cell title="答题统计" :value="`${stat.correct} 正确 / ${stat.wrong} 错误 / ${stat.unanswered} 未答`"/>
        </van-cell-group>

        <van-cell-group inset>
            <van-cell title="仅看错题">
                <template #right-icon>
                    <van-switch v-model="onlyWrong" />
                </template>
            </van-cell>
        </van-cell-group>

        <div v-if="wrongAnchors.length" class="wrong-anchors">
            <div class="label">错题直达：</div>
            <div class="anchors">
                <van-button size="mini" plain type="danger"
                            v-for="a in wrongAnchors" :key="a.questionId"
                            :class="{ active: a.questionId === activeQid }"
                            @click="goAnchor(a.questionId)">
                    {{ a.index + 1 }}
                </van-button>
            </div>
        </div>

        <van-back-top right="5vw" bottom="20vh" />

        <div class="q-list">
            <div class="q-item" v-for="it in filteredList" :key="it.questionId" :id="'q-'+it.questionId"
                 @click="setActive(it.questionId)">
                <div class="q-header">
                    <div class="q-title">
                        <span class="q-type">【{{ displayType(it.type) }}】</span>
                        <span class="q-no">{{ originIndex(it.questionId) }}.</span>
                        <span v-if="it.correct" class="ok">[正确]</span>
                        <span v-else class="bad">[错误]</span>
                    </div>
                    <van-button size="mini" type="primary" plain @click.stop="toggleCollapse(it.questionId)">
                        {{ collapsedMap[it.questionId] ? '展开' : '收起' }}
                    </van-button>
                </div>
                <div class="q-body" v-show="!collapsedMap[it.questionId]">
                    <div class="stem-text">{{ it.stem }}</div>
                    <div class="q-options">
                        <div v-for="opt in it.options" :key="opt.value" class="q-opt"
                             :class="{ picked: it.userAnswers?.includes(opt.value), right: it.correctAnswers?.includes(opt.value) }">
                            {{ opt.label }}. {{ opt.text }}
                            <span v-if="it.userAnswers?.includes(opt.value)" class="tag">我选</span>
                            <span v-if="it.correctAnswers?.includes(opt.value)" class="tag success">正确</span>
                        </div>
                    </div>
                    <div class="q-analysis" v-if="it.analysis || it.knowledge">
                        <div class="title">解析</div>
                        <div class="content">{{ it.analysis || '-' }}</div>
                        <div class="title">知识点</div>
                        <div class="content">{{ it.knowledge || '-' }}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="actions">
            <van-button type="primary" block @click="goExam">返回考试列表</van-button>
        </div>
    </div>
</template>

<script setup>
import {ref, reactive, computed, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {http} from '@/utils/http'

const route = useRoute()
const router = useRouter()
const paperId = route.params.paperId
const sessionId = route.query.sessionId
const detail = ref(null)
const onlyWrong = ref(false)
const collapsedMap = reactive({})
const activeQid = ref(null)
const qidToIndex = reactive({})

const fmt = (n) => (n ?? 0).toFixed(1) + '%'

const stat = computed(() => {
    const list = detail.value?.list || []
    let correct = 0, wrong = 0, unanswered = 0
    list.forEach(it => {
        const ua = it.userAnswers || []
        if (!ua.length) unanswered++
        else if (it.correct) correct++
        else wrong++
    })
    return {correct, wrong, unanswered}
})

const filteredList = computed(() => {
    const list = detail.value?.list || []
    return onlyWrong.value ? list.filter(it => it.correct === false) : list
})

const wrongAnchors = computed(() => {
    const allList = detail.value?.list || []
    const wrongList = allList.filter(it => it.correct === false)
    // 仅看错题时也保持原始题号，index 使用 qidToIndex
    return wrongList.map(it => ({
        index: (qidToIndex[it.questionId] ?? allList.findIndex(q => q.questionId === it.questionId)),
        questionId: it.questionId
    }))
})

const setActive = (qid) => {
    activeQid.value = qid
}
const toggleCollapse = (qid) => {
    collapsedMap[qid] = !collapsedMap[qid]
}

const goAnchor = (qid) => {
    const el = document.getElementById('q-' + qid)
    if (el) el.scrollIntoView({behavior: 'smooth', block: 'start'})
    activeQid.value = qid
}

const backTop = () => {
    window.scrollTo({top: 0, behavior: 'smooth'})
    activeQid.value = null
}

const displayType = (t) => {
    if (!t) return '-'
    // 简单映射：英文/中文兼容
    const s = String(t).toLowerCase()
    if (s.includes('多选')) return '多选'
    if (s.includes('单选')) return '单选'
    if (s.includes('判断')) return '判断'
    return t
}

const originIndex = (qid) => {
    const idx = qidToIndex[qid]
    if (idx !== undefined) return idx + 1
    const list = detail.value?.list || []
    const fi = list.findIndex(q => q.questionId === qid)
    return (fi >= 0 ? fi : 0) + 1
}

const initMaps = () => {
    const list = detail.value?.list || []
    list.forEach((q, idx) => {
        qidToIndex[q.questionId] = idx
        // 默认行为：正确题收起（collapsed=true），错误或未答展开（collapsed=false）
        if (collapsedMap[q.questionId] === undefined) collapsedMap[q.questionId] = q.correct === true
    })
}


onMounted(async () => {
    if (sessionId) {
        const res = await http.get(`/api/dingapp/exam/sessions/${sessionId}/detail`);
        console.log("res", res)
        if (res.data) {
            detail.value = res.data;
            initMaps();
        }
    } else {
        // 回退：概览
        const res = await http.get('/api/dingapp/report/overview')
        console.log("res", res)
        if (res.data) {
            const overview = res.data;
            detail.value = {paperName: '-', accuracy: overview?.examAccuracy ?? 0, score: null, durationSec: 0, list: []}
            initMaps();
        }
    }
})

const goExam = () => router.replace('/exam')
</script>

<style scoped>
.page-exam-result {
    padding-bottom: 60px;
}

.header {
    font-size: 18px;
    padding: 12px 16px;
}

.wrong-anchors {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    gap: 8px;
}

.wrong-anchors .label {
    color: #666;
    font-size: 13px;
}

.wrong-anchors .anchors {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}


.q-list {
    padding: 12px;
}

.q-item {
    background: #fff;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
}

.q-stem {
    font-size: 15px;
    margin-bottom: 8px;
}

.q-stem .q-type {
    color: #888;
    margin-right: 6px;
}

.q-stem.error {
    color: #ee0a24;
}

.q-options {
    display: grid;
    grid-template-columns: 1fr;
    gap: 6px;
}

.q-opt {
    padding: 6px 8px;
    border-radius: 4px;
    background: #f7f8fa;
}

.q-opt.picked {
    border: 1px solid #1989fa;
}

.q-opt.right {
    background: #e8f7e8;
}

.q-analysis .title {
    margin-top: 8px;
    font-weight: 600;
}

/* New header/body layout styles */
.q-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 6px;
    border-bottom: 1px dashed #eee;
}

.q-title {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #666;
    font-size: 14px;
}

.q-title .q-type { color: #888; }
.q-title .q-no { color: #333; }
.ok { color: #07c160; }
.bad { color: #ee0a24; }

.q-body { padding-top: 8px; }

.stem-text {
    color: #333;
    font-size: 15px;
    line-height: 1.6;
    white-space: pre-wrap;
    margin-bottom: 8px;
}

.wrong-anchors .van-button.active {
    background: #ffece8;
    border-color: #ee0a24;
    color: #ee0a24;
}

.q-analysis .content {
    color: #666;
    font-size: 13px;
    white-space: pre-wrap;
}

.tag {
    margin-left: 6px;
    font-size: 12px;
    color: #1989fa;
}

.tag.success {
    color: #07c160;
}

.actions {
    padding: 16px;
}
</style>

