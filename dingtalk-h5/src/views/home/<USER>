<template>
  <div class="page-home">
    <!-- 用户信息卡片 -->
    <div class="user-card">
      <div class="user-info">
        <div class="avatar">
          <svg class="icon" viewBox="0 0 1024 1024">
            <path d="M512 74.667C270.933 74.667 74.667 270.933 74.667 512S270.933 949.333 512 949.333 949.333 753.067 949.333 512 753.067 74.667 512 74.667zM512 256c70.4 0 128 57.6 128 128s-57.6 128-128 128-128-57.6-128-128 57.6-128 128-128z m0 576c-105.6 0-201.6-54.4-256-140.8 1.067-85.333 170.667-132.267 256-132.267s254.933 46.933 256 132.267C713.6 777.6 617.6 832 512 832z"/>
          </svg>
        </div>
        <div class="info">
          <div class="name">{{ userData?.name || '用户' }}</div>
          <div class="level">{{ userData?.level || 'LV.1' }}</div>
        </div>
      </div>
      <div class="stats">
        <div class="stat-item">
          <div class="number">{{ userData?.stats?.practiceCount || 0 }}</div>
          <div class="label">练习次数</div>
        </div>
        <div class="stat-item">
          <div class="number">{{ userData?.stats?.examCount || 0 }}</div>
          <div class="label">考试次数</div>
        </div>
        <div class="stat-item">
          <div class="number">{{ formatAccuracy(userData?.stats?.accuracy) }}</div>
          <div class="label">正确率</div>
        </div>
      </div>
    </div>

    <!-- 快速入口 -->
    <div class="quick-actions">
      <div class="section-title">快速开始</div>
      <van-grid :column-num="2" :gutter="12">
        <van-grid-item
          v-for="action in quickActionList"
          :key="action.key"
          @click="handleQuickAction(action.key)"
        >
          <div class="grid-content">
            <div class="grid-icon">{{ action.icon }}</div>
            <div class="grid-text">{{ action.text }}</div>
          </div>
        </van-grid-item>
      </van-grid>
    </div>

    <!-- 学习进度 -->
    <div class="progress-section">
      <div class="section-title">学习进度</div>
      <div class="progress-list">
        <div
          v-for="subject in subjectProgress"
          :key="subject.id"
          class="progress-item"
          @click="handleSubjectClick(subject.id)"
        >
          <div class="subject-info">
            <div class="subject-name">{{ subject.name }}</div>
            <div class="progress-text">{{ subject.done }}/{{ subject.total }} 题</div>
          </div>
          <div class="progress-bar">
            <van-progress
              :percentage="subject.progress"
              :color="subject.progress === 100 ? '#07c160' : '#1989fa'"
              :show-pivot="false"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 推荐练习 -->
<!--    <div class="recommend-section">-->
<!--      <div class="section-title">推荐练习</div>-->
<!--      <div class="card-list">-->
<!--        <div-->
<!--          v-for="card in recommendList"-->
<!--          :key="card.id"-->
<!--          class="recommend-card"-->
<!--        >-->
<!--          <div class="card-thumb">{{ card.icon }}</div>-->
<!--          <div class="card-content">-->
<!--            <div class="card-title">{{ card.title }}</div>-->
<!--            <div class="card-desc">{{ card.desc }}</div>-->
<!--            <div class="card-tag">{{ card.tag }}</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->

    <!-- 加载状态 -->
    <van-skeleton v-if="loading" :row="8" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { http } from '@/utils/http'
import { showFailToast } from 'vant'

const router = useRouter()

// 响应式数据
const userData = ref(null)
const reportData = ref(null)
const subjectProgress = ref([])
const loading = ref(true)

// 快速入口配置
const quickActionList = [
  { key: 'practice', icon: '📝', text: '开始练习' },
  { key: 'exam', icon: '✅', text: '模拟考试' },
  { key: 'wrong', icon: '❌', text: '错题复习' },
  { key: 'report', icon: '📊', text: '学习报告' }
]

// 推荐练习配置
const recommendList = [
  { id: 1, icon: '🎯', title: 'AI智能练习', desc: '基于大数据分析的个性化题目推荐', tag: '智能' },
  { id: 2, icon: '🚀', title: '重点突破训练', desc: '针对薄弱知识点的专项强化练习', tag: '推荐' },
  { id: 3, icon: '⚡', title: '快速提分模式', desc: '高效学习路径，快速提升答题能力', tag: '热门' }
]

// 格式化正确率
const formatAccuracy = (accuracy) => {
  return accuracy ? `${accuracy.toFixed(1)}%` : '0%'
}

// 快速入口点击处理
const handleQuickAction = (key) => {
  if (key === 'wrong') {
    router.push('/wrong/select')
  } else {
    router.push(`/${key}`)
  }
}

// 科目点击处理
const handleSubjectClick = (subjectId) => {
  router.push(`/practice?subjectId=${subjectId}`)
}

// 页面初始化
onMounted(async () => {
  try {
    loading.value = true
    const [user, subjects] = await Promise.all([
      http.get('/api/dingapp/user/me'),
      http.get('/api/dingapp/subjects/progress')
    ])
      console.log("user", user)

      if (user.data) {
        userData.value = user.data
      }
      if (subjects.data) {
        subjectProgress.value = subjects.data
      }
  } catch (error) {
    console.error('数据加载失败:', error)
    showFailToast('数据加载失败')
  } finally {
    loading.value = false
  }
})
</script>

<style lang="scss" scoped>
.page-home {
  padding: 16px;
  padding-bottom: 80px;
  background-color: #f7f8fa;
  min-height: 100vh;
}

// 用户信息卡片
.user-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;

      .icon {
        width: 32px;
        height: 32px;
        fill: white;
      }
    }

    .info {
      flex: 1;

      .name {
        font-size: 20px;
        font-weight: 600;
        color: #323233;
        margin-bottom: 4px;
      }

      .level {
        font-size: 14px;
        color: #969799;
      }
    }
  }

  .stats {
    display: flex;
    justify-content: space-around;

    .stat-item {
      text-align: center;

      .number {
        font-size: 24px;
        font-weight: 600;
        color: #1989fa;
        margin-bottom: 4px;
      }

      .label {
        font-size: 12px;
        color: #969799;
      }
    }
  }
}

// 区块标题
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 12px;
}

// 快速入口
.quick-actions {
  margin-bottom: 24px;

  .grid-content {
    padding: 16px;
    text-align: center;

    .grid-icon {
      font-size: 32px;
      margin-bottom: 8px;
    }

    .grid-text {
      font-size: 14px;
      color: #323233;
      font-weight: 500;
    }
  }
}

// 学习进度
.progress-section {
  margin-bottom: 24px;

  .progress-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }

  .progress-item {
    padding: 16px 20px;
    border-bottom: 1px solid #f7f8fa;
    cursor: pointer;
    transition: background-color 0.2s;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #f7f8fa;
    }

    .subject-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .subject-name {
        font-size: 16px;
        font-weight: 500;
        color: #323233;
      }

      .progress-text {
        font-size: 14px;
        color: #969799;
      }
    }

    .progress-bar {
      :deep(.van-progress) {
        height: 6px;
      }
    }
  }
}

// 推荐练习
.recommend-section {
  .card-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .recommend-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s;

    &:active {
      transform: scale(0.98);
    }

    .card-thumb {
      font-size: 32px;
      margin-right: 16px;
    }

    .card-content {
      flex: 1;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #323233;
        margin-bottom: 4px;
      }

      .card-desc {
        font-size: 14px;
        color: #969799;
        margin-bottom: 8px;
        line-height: 1.4;
      }

      .card-tag {
        display: inline-block;
        padding: 2px 8px;
        background: #f2f3f5;
        color: #646566;
        font-size: 12px;
        border-radius: 4px;
      }
    }
  }
}
</style>

