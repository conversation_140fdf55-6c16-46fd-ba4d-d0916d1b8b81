<template>
    <div>
        <van-nav-bar title="学习报告" left-arrow @click-left="goBack"/>
        <div class="page-report">
            <div class="header">学习报告</div>
            <van-cell-group inset>
                <van-cell title="练习次数" :value="report?.practiceCount ?? 0" />
                <van-cell title="考试次数" :value="report?.examCount ?? 0" />
                <van-cell title="练习正确率" :value="fmt(report?.practiceAccuracy)" />
                <van-cell title="考试正确率" :value="fmt(report?.examAccuracy)" />
                <van-cell title="总体正确率" :value="fmt(report?.accuracy)" />
                <van-cell title="薄弱科目Top3" :label="(report?.weakSubjects||[]).join('、') || '-'" />
            </van-cell-group>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { http } from '@/utils/http'
import {useRouter} from "vue-router";

const router = useRouter()
const report = ref(null)
const fmt = (n) => (n ?? 0).toFixed(1) + '%'

const goBack = () => {
    router.push('/home')
}

onMounted(async () => {
    const res = await http.get('/api/dingapp/report/overview')
    if (res && res.data) {
        report.value = res.data
    }
})
</script>

<style scoped>
.page-report { padding-bottom: 60px; }
.header { font-size: 18px; padding: 12px 16px; }
</style>

