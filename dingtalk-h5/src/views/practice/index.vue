<template>
    <div class="page-practice">
        <div class="section-title">选择练习科目</div>
        <div class="subject-list">
            <div
                v-for="item in list"
                :key="item.id"
                class="subject-item card-container"
                @click="goToDetail(item)"
            >
                <div class="subject-icon">{{ getSubjectIcon(item.name) }}</div>
                <div class="subject-content">
                    <div class="subject-name">{{ item.name }}</div>
                    <div class="subject-desc">共{{ item.total }}题 · 已练习{{ item.done }}题</div>
                    <div class="subject-progress">
                        <div class="progress-bar">
                            <div
                                class="progress-fill"
                                :class="{ complete: item.progress >= 100 }"
                                :style="{ width: item.progress + '%' }"
                            ></div>
                        </div>
                        <span class="progress-percent">{{ item.progress }}%</span>
                    </div>
                </div>
                <div class="subject-arrow">›</div>
            </div>
        </div>

        <!-- 加载状态 -->
        <van-loading v-if="loading" class="loading-center" vertical>
            加载中...
        </van-loading>

        <!-- 空状态 -->
        <van-empty v-if="!loading && list.length === 0" description="暂无练习科目"/>
    </div>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {useRouter} from 'vue-router'
import {showFailToast} from 'vant'
import {http} from '@/utils/http'

const list = ref([])
const loading = ref(false)
const router = useRouter()

// 获取科目图标
const getSubjectIcon = (subjectName) => {
    const iconMap = {
        '公共基础知识': '📚',
        '专业知识': '💼',
        '综合能力': '🎯'
    }
    return iconMap[subjectName] || '📖'
}

// 跳转到练习详情页面
const goToDetail = (item) => {
    router.push(`/practice/${item.id}/detail`)
}

// 加载科目进度数据
const loadSubjects = async () => {
    try {
        loading.value = true
        const res = await http.get('/api/dingapp/subjects/progress');
        console.log("res", res)
        if (res.data) {
            list.value = res.data
        }
    } catch (error) {
        showFailToast('加载失败，请重试')
        console.error('加载科目进度失败:', error)
    } finally {
        loading.value = false
    }
}

onMounted(loadSubjects)
</script>

<style scoped>
.page-practice {
    padding: 16px 16px 80px;
    background: #f7f8fa;
    min-height: 100vh;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 16px;
}

.subject-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.subject-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #fff;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.subject-item:active {
    transform: scale(0.98);
    background: #f8f9fa;
}

.subject-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-right: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.subject-content {
    flex: 1;
}

.subject-name {
    font-size: 16px;
    font-weight: 500;
    color: #323233;
    margin-bottom: 4px;
}

.subject-desc {
    font-size: 14px;
    color: #969799;
    margin-bottom: 8px;
}

.subject-progress {
    display: flex;
    align-items: center;
    gap: 8px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: #ebedf0;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #1989fa;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-fill.complete {
    background: #07c160;
}

.progress-percent {
    font-size: 12px;
    color: #969799;
    min-width: 35px;
    text-align: right;
}

.subject-arrow {
    font-size: 18px;
    color: #c8c9cc;
    margin-left: 8px;
}

.loading-center {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

/* 卡片容器样式已内联到具体元素中 */
</style>

