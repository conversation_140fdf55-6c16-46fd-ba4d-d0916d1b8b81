<template>
    <div class="practice-layout">
        <van-nav-bar title="练习详情" left-arrow @click-left="goBack" fixed placeholder />
        <div class="page-practice-session">
            <!-- 题目进度头部 -->
            <div class="question-header">
                <div class="question-progress">
                    <div class="progress-info">
                        <span class="current-question">第 <span>{{ index + 1 }}</span> 题</span>
                        <span class="total-questions">共 <span>{{ total }}</span> 题</span>
                    </div>
                    <div class="progress-bar">
                        <div
                            class="progress-fill"
                            :style="{ width: progressPercentage + '%' }"
                        ></div>
                    </div>
                </div>
                <div class="question-timer">{{ formattedTime }}</div>
            </div>

            <!-- 错题练习模式提示 -->
            <div v-if="mode === 'wrong'" class="wrong-mode-tip">
                当前为错题练习模式，将优先出错题
            </div>

            <!-- 题目内容 -->
            <div v-if="question" class="question-content card-container">
                <div class="question-type">{{ question.type || '单选题' }}</div>
                <div class="question-text">{{ question.stem }}</div>
            </div>

            <!-- 题目选项 -->
            <div v-if="question" class="question-options">
                <template v-for="opt in question.options" :key="opt.value">
                    <div
                        class="option-item"
                        :class="optionClass(opt.value)"
                        @click="selectOption(opt.value)"
                    >
                        <div class="option-label">{{ opt.label }}</div>
                        <div class="option-text">{{ opt.text }}</div>
                    </div>
                </template>
            </div>
            <!-- AI解析面板 (预留位置) -->
            <!--        <AIAnalysisPanel-->
            <!--            v-if="showAnalysis"-->
            <!--            :question="question"-->
            <!--            :user-answer="currentAnswer"-->
            <!--            :is-correct="isAnswerCorrect"-->
            <!--            @close="hideAnalysis"-->
            <!--        />-->

            <!-- 加载状态 -->
            <van-loading v-if="loading" class="loading-center" vertical>
                加载中...
            </van-loading>
        </div>
        <!-- 操作按钮 -->
        <div class="question-actions">
            <van-button
                type="default"
                @click="prev"
                :disabled="index <= 0"
            >
                上一题
            </van-button>
            <van-button
                type="primary"
                @click="submitCurrent"
                :disabled="!hasSelectedAnswer || submittedMap[question?.id]"
                v-if="!showNextButton"
            >
                作答
            </van-button>
            <van-button
                v-if="showNextButton && !isLastQuestion"
                type="default"
                @click="next"
            >
                下一题
            </van-button>
            <van-button
                v-if="showNextButton && isLastQuestion"
                type="primary"
                @click="finishPractice"
            >
                完成练习
            </van-button>
        </div>
    </div>
</template>

<script setup>
import {ref, reactive, computed, onMounted, onUnmounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {showSuccessToast, showFailToast, showConfirmDialog} from 'vant'
import {http} from '@/utils/http'

const route = useRoute()
const router = useRouter()
const subjectId = route.params.subjectId

// 会话相关
const sessionId = ref(null)
const mode = ref(route.query.mode || 'sequence')
const chapterId = ref(route.query.chapterId || null)
const pageSize = 1 // 单题翻页
const total = ref(0)
const loading = ref(false)

// 题目相关
const index = ref(0)
const question = ref(null)
const nextCursor = ref(0)

// 答案相关
const answerMap = reactive({})
const singleAnswer = ref('')
const currentAnswer = ref([])
// const showAnalysis = ref(false)
const isAnswerCorrect = ref(false)
const showNextButton = ref(false)


// 提交与锁定相关
const submittedMap = reactive({})
const perQuestionStartTime = ref(0)

// 计时器相关
const startTime = ref(0)
const currentTime = ref(0)
const timerInterval = ref(null)

// 计算属性
const isMulti = computed(() => {
    if (!question.value) return false
    return (question.value.type && /多选/i.test(question.value.type))
})

const progressPercentage = computed(() => {
    if (total.value === 0) return 0
    return Math.round(((index.value + 1) / total.value) * 100)
})

const formattedTime = computed(() => {
    const seconds = Math.floor((currentTime.value - startTime.value) / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
})

const hasSelectedAnswer = computed(() => {
    if (isMulti.value) {
        return answerMap[question.value?.id]?.length > 0
    } else {
        return singleAnswer.value !== ''
    }
})

const isLastQuestion = computed(() => {
    return index.value + 1 >= total.value
})

// 计算选项展示 class：选中态 + 正确/错误态
const optionClass = (optionValue) => {
    const classes = { selected: false, correct: false, wrong: false }
    const q = question.value
    if (!q) return classes

    // 选中态
    const selected = isMulti.value
        ? (answerMap[q.id]?.includes(optionValue) || false)
        : (singleAnswer.value === optionValue)
    classes.selected = selected

    // 提交后高亮
    if (submittedMap[q.id]) {
        const correctSet = new Set(q.correctAnswers || [])
        if (correctSet.has(optionValue)) {
            classes.correct = true
        }
        if (!classes.correct && selected) {
            classes.wrong = true
        }
    }
    return classes
}

// 选择选项
const selectOption = (optionValue) => {
    // 已提交答案后不允许修改
    if (submittedMap[question.value.id]) return

    if (isMulti.value) {
        if (!answerMap[question.value.id]) {
            answerMap[question.value.id] = []
        }
        const answers = answerMap[question.value.id]
        const index = answers.indexOf(optionValue)
        if (index > -1) {
            answers.splice(index, 1)
        } else {
            answers.push(optionValue)
        }
    } else {
        singleAnswer.value = optionValue
    }
}

// 作答提交（单题）
const submitCurrent = async () => {
    if (!question.value || !hasSelectedAnswer.value || submittedMap[question.value.id]) return

    try {
        loading.value = true
        const values = isMulti.value
            ? (answerMap[question.value.id] || [])
            : (singleAnswer.value ? [singleAnswer.value] : [])

        currentAnswer.value = values

        // 单题耗时：以进入该题的时间为起点
        const duration = Math.floor((Date.now() - perQuestionStartTime.value) / 1000)

        const res = await http.post(`/api/dingapp/practice/sessions/${sessionId.value}/answer`, {
            questionId: question.value.id,
            values,
            durationSec: duration
        })

        // axios 封装返回 response.data；后端为 R<T>，此时 res.data 才是 payload
        const list = res?.data || []
        const saved = Array.isArray(list) ? list[0] : null
        const correct = saved?.isCorrect === 1
        isAnswerCorrect.value = correct
        submittedMap[question.value.id] = true
        showNextButton.value = true

        if (correct) {
            showSuccessToast('回答正确！')
        } else {
            showFailToast('回答错误!')
        }
    } catch (error) {
        showFailToast('提交失败，请重试')
        console.error('提交答案失败:', error)
    } finally {
        loading.value = false
    }
}

// 隐藏解析面板
// const hideAnalysis = () => {
//     showAnalysis.value = false
// }

// 加载练习会话
const loadSession = async () => {
    try {
        loading.value = true
        const sessionData = {
            subjectId,
            mode: mode.value,
            pageSize
        }
        if (chapterId.value) {
            sessionData.chapterId = chapterId.value
        }

        const res = await http.post('/api/dingapp/practice/sessions', sessionData)
        console.log("loadSession", res)
        if (res.data) {
            sessionId.value = res.data.sessionId
            total.value = res.data.total || 0
        }
        await loadQuestion(0)
        startTimer()
    } catch (error) {
        showFailToast('加载练习失败')
        console.error('加载练习会话失败:', error)
    } finally {
        loading.value = false
    }
}

// 加载题目
const loadQuestion = async (cursor) => {
    try {
        const res = await http.get(`/api/dingapp/practice/sessions/${sessionId.value}/questions`, {
            params: {cursor, limit: pageSize}
        })
        console.log("loadQuestion", res)
        if (res.data) {
            question.value = res.data.list && res.data.list[0]
            nextCursor.value = res.data.nextCursor
            index.value = cursor
        }

        // 重置答题状态
        resetQuestionState()
    } catch (error) {
        showFailToast('加载题目失败')
        console.error('加载题目失败:', error)
    }
}

// 重置题目状态
const resetQuestionState = () => {
    // 若未提交，则清空当前题选择；若已提交，保持既有选择但禁用交互
    if (!submittedMap[question.value?.id]) {
        singleAnswer.value = ''
        if (question.value) {
            answerMap[question.value.id] = []
        }
        showNextButton.value = false
        currentAnswer.value = []
    } else {
        // 已提交题目：显示“下一题”按钮
        showNextButton.value = true
    }
    // 重置单题计时起点
    perQuestionStartTime.value = Date.now()
}

// 上一题
const prev = async () => {
    if (index.value <= 0) return
    // 返回上一题也保持锁定；若本题未提交则清除选择
    await loadQuestion(index.value - 1)
}

// 下一题
const next = async () => {
    if (nextCursor.value == null) return
    await loadQuestion(nextCursor.value)
}

// 完成练习
const finishPractice = async () => {
    try {
        loading.value = true
        const res = await http.post(`/api/dingapp/practice/sessions/${sessionId.value}/finish`)

        if (res.data) {
            const result = res.data
            // 显示统计弹窗
            showConfirmDialog({
                title: '练习完成',
                message: `总题数：${result.total}题\n正确：${result.correct}题\n错误：${result.wrong}题\n正确率：${result.accuracy?.toFixed(1)}%`,
                confirmButtonText: '确定',
                showCancelButton: false,
            }).then(() => {
                // 返回练习页面
                router.push('/practice')
            })
        }
    } catch (error) {
        showFailToast('获取练习结果失败')
        console.error('完成练习失败:', error)
    } finally {
        loading.value = false
    }
}

// 启动计时器
const startTimer = () => {
    startTime.value = Date.now()
    currentTime.value = Date.now()

    timerInterval.value = setInterval(() => {
        currentTime.value = Date.now()
    }, 1000)
}

// 停止计时器
const stopTimer = () => {
    if (timerInterval.value) {
        clearInterval(timerInterval.value)
        timerInterval.value = null
    }
}

// 返回上一页
const goBack = () => {
    showConfirmDialog({
        title: '退出练习',
        message: '确定要退出当前练习吗？练习进度将会保存。',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
    }).then(() => {
        // 用户确认退出，跳转到 detail 页面
        router.push(`/practice/${subjectId}/detail`)
    }).catch(() => {
        // 用户取消，不执行任何操作
    })
}

onMounted(loadSession)
onUnmounted(stopTimer)
</script>

<style scoped>

.practice-layout {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.page-practice-session {
    padding: 0;
    background: #f7f8fa;
    flex: 1;
    overflow-y: auto;
    padding-bottom: calc(76px + env(safe-area-inset-bottom));
}

/* 题目进度头部 */
.question-header {
    background: #fff;
    padding: 16px;
    border-bottom: 1px solid #ebedf0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.question-progress {
    margin-bottom: 12px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    color: #323233;
}

.current-question {
    font-weight: 500;
}

.total-questions {
    color: #969799;
}

.progress-bar {
    height: 6px;
    background: #ebedf0;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #1989fa;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.question-timer {
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    color: #1989fa;
}

/* 题目内容 */
.question-content {
    margin: 16px;
    padding: 20px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.question-type {
    display: inline-block;
    padding: 4px 8px;
    background: #e8f4fd;
    color: #1989fa;
    font-size: 12px;
    border-radius: 4px;
    margin-bottom: 12px;
}

.question-text {
    font-size: 16px;
    line-height: 1.6;
    color: #323233;
}

/* 题目选项 */
.question-options {
    margin: 16px;
}

.option-item {
    display: flex;
    align-items: center;
    padding: 16px;
    margin-bottom: 12px;
    background: #fff;
    border-radius: 8px;
    border: 2px solid #ebedf0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.option-item:hover {
    border-color: #1989fa;
}

.option-item.selected {
    border-color: #1989fa;
    background: #e8f4fd;
}

/* 正确/错误高亮样式（静态H5约定） */
.option-item.correct {
    border-color: #07c160;
    background: #e8f9ef;
}
.option-item.wrong {
    border-color: #ee0a24;
    background: #fdecee;
}


.option-item:active {
    transform: scale(0.98);
}

.option-label {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f7f8fa;
    border-radius: 50%;
    font-weight: 500;
    color: #323233;
    margin-right: 12px;
    flex-shrink: 0;
}

.option-item.selected .option-label {
    background: #1989fa;
    color: #fff;
}

.option-text {
    flex: 1;
    font-size: 15px;
    line-height: 1.5;
    color: #323233;
}

/* 操作按钮 */
.question-actions {
    display: flex;
    gap: 12px;
    padding: 16px 16px calc(16px + env(safe-area-inset-bottom));
    background: #fff;
    border-top: 1px solid #ebedf0;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
}

.question-actions .van-button {
    flex: 1;
    height: 44px;
    border-radius: 8px;
}

/* 加载状态 */
.loading-center {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.wrong-mode-tip {
    margin: 12px 16px 0;
    background: #fff7e6;
    color: #d48806;
    border: 1px solid #ffe58f;
    border-radius: 8px;
    padding: 10px 12px;
    font-size: 14px;
}

/* 卡片容器样式已内联到具体元素中 */
</style>

