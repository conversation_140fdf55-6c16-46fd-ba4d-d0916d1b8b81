<template>
    <div>
        <van-nav-bar title="练习详情" left-arrow @click-left="goBack" />
        <div class="page-practice-detail">
            <!-- 科目概览卡片 -->
            <div class="practice-header card-container">
                <div class="practice-info">
                    <div class="practice-title">{{ subjectSummary.name || '加载中...' }}</div>
                    <div class="practice-progress">
                        <span>进度：{{ subjectSummary.done || 0 }}/{{ subjectSummary.total || 0 }}题</span>
                        <span class="progress-percent">{{ subjectSummary.progress || 0 }}%</span>
                    </div>
                </div>
                <div class="practice-stats">
                    <div class="stat-item">
                        <div class="stat-number">{{ subjectSummary.accuracy || 0 }}%</div>
                        <div class="stat-label">正确率</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ subjectSummary.practiced || 0 }}</div>
                        <div class="stat-label">已练习</div>
                    </div>
                </div>
            </div>

            <!-- 练习模式 -->
            <div class="practice-modes">
                <div class="section-title">练习模式</div>
                <div class="mode-list">
                    <div
                        class="mode-item card-container"
                        v-for="mode in practiceMode"
                        :key="mode.key"
                        @click="startPractice(mode.key)"
                    >
                        <div class="mode-icon">{{ mode.icon }}</div>
                        <div class="mode-content">
                            <div class="mode-name">{{ mode.name }}</div>
                            <div class="mode-desc">{{ mode.desc }}</div>
                        </div>
                        <div class="mode-arrow">›</div>
                    </div>
                </div>
            </div>

            <!-- 章节练习 -->
            <div class="chapter-list">
                <div class="section-title">章节练习</div>
                <div class="chapters">
                    <div
                        v-for="chapter in chapters"
                        :key="chapter.id"
                        class="chapter-item card-container"
                        @click="startChapterPractice(chapter)"
                    >
                        <div class="chapter-info">
                            <div class="chapter-name">{{ chapter.name }}</div>
                            <div class="chapter-progress">{{ chapter.done }}/{{ chapter.total }}题 · {{ chapter.progress }}%</div>
                        </div>
                        <div class="chapter-status" :class="getChapterStatusClass(chapter.status)">
                            {{ getChapterStatusText(chapter.status) }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 加载状态 -->
            <van-loading v-if="loading" class="loading-center" vertical>
                加载中...
            </van-loading>
        </div>
    </div>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {showFailToast} from 'vant'
import {http} from '@/utils/http'

const route = useRoute()
const router = useRouter()
const subjectId = route.params.subjectId

const loading = ref(false)
const subjectSummary = ref({})
const chapters = ref([])

// 练习模式配置
const practiceMode = [
    {
        key: 'sequence',
        name: '顺序练习',
        desc: '按章节顺序练习，系统学习',
        icon: '📖'
    },
    {
        key: 'random',
        name: '随机练习',
        desc: '随机抽取题目，巩固知识',
        icon: '🎲'
    },
    {
        key: 'wrong',
        name: '错题练习',
        desc: '专项练习错题，查漏补缺',
        icon: '❌'
    }
]

// 获取章节状态样式类
const getChapterStatusClass = (status) => {
    const statusMap = {
        'complete': 'complete',
        'progress': 'progress',
        'not_started': ''
    }
    return statusMap[status] || ''
}

// 获取章节状态文本
const getChapterStatusText = (status) => {
    const textMap = {
        'complete': '已完成',
        'progress': '进行中',
        'not_started': '未开始'
    }
    return textMap[status] || '未开始'
}

// 开始练习（按模式）
const startPractice = (mode) => {
    router.push(`/practice/${subjectId}/session?mode=${mode}`)
}

// 开始章节练习
const startChapterPractice = (chapter) => {
    router.push(`/practice/${subjectId}/session?mode=sequence&chapterId=${chapter.id}`)
}

// 加载科目概览
const loadSubjectSummary = async () => {
    try {
        const response = await http.get(`/api/dingapp/subjects/${subjectId}/summary`);
        console.log("loadSubjectSummary", response)
        if (response.data) {
            subjectSummary.value = response.data
        }
    } catch (error) {
        showFailToast('加载科目信息失败')
        console.error('加载科目概览失败:', error)
    }
}

// 加载章节列表
const loadChapters = async () => {
    try {
        const response = await http.get(`/api/dingapp/subjects/${subjectId}/chapters`);
        console.log("loadChapters", response)
        if (response.data) {
            chapters.value = response.data
        }
    } catch (error) {
        showFailToast('加载章节信息失败')
        console.error('加载章节列表失败:', error)
    }
}

// 初始化数据
const initData = async () => {
    loading.value = true
    try {
        await Promise.all([
            loadSubjectSummary(),
            loadChapters()
        ])
    } finally {
        loading.value = false
    }
}

const goBack = () => {
    router.push('/practice')
}

onMounted(() => {
    initData()
})
</script>

<style scoped>
.page-practice-detail {
    padding: 16px 16px 80px;
    background: #f7f8fa;
    min-height: 100vh;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 16px;
}

/* 科目概览卡片 */
.practice-header {
    background: #fff;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.practice-info {
    margin-bottom: 16px;
}

.practice-title {
    font-size: 18px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 8px;
}

.practice-progress {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #969799;
}

.progress-percent {
    font-weight: 500;
    color: #1989fa;
}

.practice-stats {
    display: flex;
    justify-content: space-around;
    padding-top: 16px;
    border-top: 1px solid #ebedf0;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 20px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: #969799;
}

/* 练习模式 */
.practice-modes {
    margin-bottom: 20px;
}

.mode-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.mode-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #fff;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.mode-item:active {
    transform: scale(0.98);
    background: #f8f9fa;
}

.mode-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-right: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.mode-content {
    flex: 1;
}

.mode-name {
    font-size: 16px;
    font-weight: 500;
    color: #323233;
    margin-bottom: 4px;
}

.mode-desc {
    font-size: 14px;
    color: #969799;
}

.mode-arrow {
    font-size: 18px;
    color: #c8c9cc;
    margin-left: 8px;
}

/* 章节列表 */
.chapter-list {
    margin-bottom: 20px;
}

.chapters {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.chapter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #fff;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.chapter-item:active {
    transform: scale(0.98);
    background: #f8f9fa;
}

.chapter-info {
    flex: 1;
}

.chapter-name {
    font-size: 16px;
    font-weight: 500;
    color: #323233;
    margin-bottom: 4px;
}

.chapter-progress {
    font-size: 14px;
    color: #969799;
}

.chapter-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    background: #f7f8fa;
    color: #969799;
}

.chapter-status.complete {
    background: #e8f5e8;
    color: #07c160;
}

.chapter-status.progress {
    background: #e8f4fd;
    color: #1989fa;
}

.loading-center {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

/* 卡片容器样式已内联到具体元素中 */
</style>
