<template>
    <div class="page-wrong">
        <div class="header">
            <van-nav-bar :title="pageTitle" left-arrow @click-left="goBack"/>
        </div>

        <div class="actions" v-if="subjectId">
            <van-button type="primary" @click="practiceWrong">错题练习</van-button>
            <van-button type="default" @click="loadMore">加载更多</van-button>
        </div>

        <van-list v-model:loading="loading" :finished="finished" @load="loadMore">
            <van-cell v-for="item in list" :key="item.questionId"
                      :title="''"
                      :label="truncate(item.stem, 50)"/>
        </van-list>

        <van-empty v-if="!loading && !list.length && subjectId" description="暂无错题"/>
    </div>
</template>

<script setup>
import {ref, onMounted, computed} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {http} from '@/utils/http'

const route = useRoute()
const router = useRouter()
const subjectId = ref('')
const subjectName = ref('')
const list = ref([])
const loading = ref(false)
const finished = ref(false)
const page = ref(1)
const pageSize = 20

const pageTitle = computed(() => {
    return subjectName.value ? `${subjectName.value} - 错题本` : '错题本'
})

const truncate = (text, len) => {
    if (!text) return '-'
    return text.length > len ? text.substring(0, len) + '...' : text
}

const loadMore = async () => {
    if (!subjectId.value) return
    loading.value = true
    try {
        const res = await http.get('/api/dingapp/wrong', {
            params: {subjectId: subjectId.value, page: page.value, size: pageSize}
        })
        if (res && res.data && res.data.length) {
            list.value.push(...res.data)
            page.value++
        } else {
            finished.value = true
        }
    } finally {
        loading.value = false
    }
}

const practiceWrong = () => {
    router.push(`/practice/${subjectId.value}/session?mode=wrong`)
}

const goBack = () => {
    router.back()
}

onMounted(async () => {
    subjectId.value = route.params.subjectId || ''
    if (subjectId.value) {
        // 获取科目名称
        try {
            const res = await http.get('/api/dingapp/subjects/progress')
            if (res && res.data) {
                const subject = res.data.find(s => s.id == subjectId.value)
                subjectName.value = subject?.name || `科目#${subjectId.value}`
            }
        } catch (e) {
            subjectName.value = `科目#${subjectId.value}`
        }
        // 加载错题
        await loadMore()
    }
})
</script>

<style scoped>
.page-wrong {
    padding-bottom: 60px;
}

.header {
    margin-bottom: 12px;
}

.actions {
    display: flex;
    gap: 12px;
    padding: 12px 16px;
}
</style>

