<template>
  <div class="page-401">
    <van-empty image="error" description="未授权或登录已过期">
      <div class="actions">
        <van-button type="primary" block @click="goLogin">去登录</van-button>
        <van-button class="mt8" plain type="primary" block @click="retry">刷新重试</van-button>
      </div>
    </van-empty>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()
const goLogin = () => router.replace('/login')
const retry = () => location.reload()
</script>

<style scoped>
.page-401 {
  padding: 24px;
}
.actions {
  margin-top: 12px;
}
.mt8 { margin-top: 8px; }
</style>

