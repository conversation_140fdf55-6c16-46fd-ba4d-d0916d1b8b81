<template>
  <div class="timer" :class="timerClass">
    <span class="timer-icon" v-if="showIcon">⏱️</span>
    <span class="timer-text">{{ formattedTime }}</span>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  // 计时模式：'up' 正计时，'down' 倒计时
  mode: {
    type: String,
    default: 'up',
    validator: (value) => ['up', 'down'].includes(value)
  },
  // 倒计时的初始时间（秒）
  initialTime: {
    type: Number,
    default: 0
  },
  // 是否自动开始
  autoStart: {
    type: Boolean,
    default: true
  },
  // 是否显示图标
  showIcon: {
    type: Boolean,
    default: false
  },
  // 警告时间阈值（秒），倒计时模式下生效
  warningThreshold: {
    type: Number,
    default: 300 // 5分钟
  }
})

const emit = defineEmits(['timeup', 'warning', 'tick'])

const currentTime = ref(0)
const isRunning = ref(false)
const timerInterval = ref(null)

// 计算属性
const formattedTime = computed(() => {
  const time = props.mode === 'down' ? Math.max(0, props.initialTime - currentTime.value) : currentTime.value
  const hours = Math.floor(time / 3600)
  const minutes = Math.floor((time % 3600) / 60)
  const seconds = time % 60
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  } else {
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }
})

const timerClass = computed(() => {
  if (props.mode === 'down') {
    const remainingTime = props.initialTime - currentTime.value
    if (remainingTime <= 0) {
      return 'timer-expired'
    } else if (remainingTime <= props.warningThreshold) {
      return 'timer-warning'
    }
  }
  return ''
})

// 方法
const start = () => {
  if (isRunning.value) return
  
  isRunning.value = true
  timerInterval.value = setInterval(() => {
    currentTime.value++
    emit('tick', currentTime.value)
    
    if (props.mode === 'down') {
      const remainingTime = props.initialTime - currentTime.value
      
      // 检查是否到达警告时间
      if (remainingTime === props.warningThreshold) {
        emit('warning', remainingTime)
      }
      
      // 检查是否时间到
      if (remainingTime <= 0) {
        stop()
        emit('timeup')
      }
    }
  }, 1000)
}

const stop = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value)
    timerInterval.value = null
  }
  isRunning.value = false
}

const reset = () => {
  stop()
  currentTime.value = 0
}

const pause = () => {
  stop()
}

const resume = () => {
  start()
}

// 获取当前时间（秒）
const getCurrentTime = () => {
  return props.mode === 'down' ? Math.max(0, props.initialTime - currentTime.value) : currentTime.value
}

// 暴露方法给父组件
defineExpose({
  start,
  stop,
  reset,
  pause,
  resume,
  getCurrentTime
})

onMounted(() => {
  if (props.autoStart) {
    start()
  }
})

onUnmounted(() => {
  stop()
})
</script>

<style scoped>
.timer {
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  color: #1989fa;
}

.timer-icon {
  font-size: 16px;
}

.timer-text {
  font-size: 16px;
}

.timer-warning {
  color: #ff8c00;
}

.timer-warning .timer-text {
  animation: pulse 1s infinite;
}

.timer-expired {
  color: #ff4444;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
