<template>
  <div class="ai-analysis card-container">
    <div class="analysis-header">
      <div class="analysis-title">🤖 AI智能解析</div>
      <div class="analysis-result" :class="resultClass">
        <span class="result-icon">{{ resultIcon }}</span>
        <span class="result-text">{{ resultText }}</span>
      </div>
    </div>

    <div class="analysis-content">
      <!-- 正确答案 -->
      <div class="analysis-section">
        <div class="section-title">正确答案</div>
        <div class="section-content">
          <span class="correct-answer">{{ correctAnswerText }}</span>
        </div>
      </div>

      <!-- 答案解析 -->
      <div class="analysis-section">
        <div class="section-title">答案解析</div>
        <div class="section-content">
          <p class="explanation-text">{{ explanationText }}</p>
        </div>
      </div>

      <!-- 知识点标签 -->
      <div class="analysis-section" v-if="knowledgePoints.length > 0">
        <div class="section-title">相关知识点</div>
        <div class="section-content">
          <div class="knowledge-tags">
            <span 
              v-for="point in knowledgePoints" 
              :key="point"
              class="knowledge-tag"
            >
              {{ point }}
            </span>
          </div>
        </div>
      </div>

      <!-- 错误分析 (仅错误时显示) -->
      <div class="analysis-section" v-if="!isCorrect && errorAnalysis">
        <div class="section-title">错误分析</div>
        <div class="section-content">
          <p class="error-text">{{ errorAnalysis }}</p>
        </div>
      </div>
    </div>

    <!-- 关闭按钮 -->
    <div class="analysis-actions">
      <van-button 
        type="primary" 
        size="small" 
        @click="$emit('close')"
      >
        知道了
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    default: () => ({})
  },
  userAnswer: {
    type: Array,
    default: () => []
  },
  isCorrect: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close'])

// 计算属性
const resultClass = computed(() => {
  return props.isCorrect ? 'correct' : 'incorrect'
})

const resultIcon = computed(() => {
  return props.isCorrect ? '✅' : '❌'
})

const resultText = computed(() => {
  return props.isCorrect ? '回答正确' : '回答错误'
})

const correctAnswerText = computed(() => {
  // 这里应该从后端获取正确答案，暂时模拟
  return 'B. 1949年'
})

const explanationText = computed(() => {
  // 这里应该从后端获取解析内容，暂时模拟
  return '中华人民共和国成立于1949年10月1日，这是中国历史上的重要时刻，标志着新中国的诞生。'
})

const knowledgePoints = computed(() => {
  // 这里应该从后端获取知识点，暂时模拟
  return ['中国近现代史', '新中国成立', '重要历史事件']
})

const errorAnalysis = computed(() => {
  if (props.isCorrect) return ''
  // 这里应该根据用户答案生成错误分析，暂时模拟
  return '您选择的答案不正确。请注意区分中华人民共和国成立时间与其他重要历史事件的时间。'
})

// 预留方法：从后端获取AI解析数据
const fetchAnalysisData = async (questionId, userAnswer) => {
  // TODO: 实现从后端获取AI解析数据的逻辑
  // const response = await http.get(`/api/dingapp/questions/${questionId}/analysis`, {
  //   params: { userAnswer: userAnswer.join(',') }
  // })
  // return response
  console.log('预留方法：获取AI解析数据', { questionId, userAnswer })
}

// 预留方法：记录用户查看解析行为
const recordAnalysisView = async (questionId) => {
  // TODO: 实现记录用户查看解析行为的逻辑
  // await http.post('/api/dingapp/practice/analysis-views', {
  //   questionId,
  //   viewedAt: new Date()
  // })
  console.log('预留方法：记录解析查看', { questionId })
}
</script>

<style scoped>
.ai-analysis {
  margin: 16px;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.analysis-header {
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.analysis-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.analysis-result {
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-icon {
  font-size: 18px;
}

.result-text {
  font-size: 14px;
  font-weight: 500;
}

.analysis-result.correct {
  color: #07c160;
}

.analysis-result.incorrect {
  color: #ff4444;
}

.analysis-content {
  padding: 16px;
}

.analysis-section {
  margin-bottom: 16px;
}

.analysis-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8px;
}

.section-content {
  font-size: 14px;
  line-height: 1.6;
  color: #646566;
}

.correct-answer {
  display: inline-block;
  padding: 4px 8px;
  background: #e8f5e8;
  color: #07c160;
  border-radius: 4px;
  font-weight: 500;
}

.explanation-text {
  margin: 0;
}

.knowledge-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.knowledge-tag {
  display: inline-block;
  padding: 4px 8px;
  background: #e8f4fd;
  color: #1989fa;
  font-size: 12px;
  border-radius: 12px;
}

.error-text {
  margin: 0;
  color: #ff4444;
  background: #fff2f2;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #ff4444;
}

.analysis-actions {
  padding: 16px;
  border-top: 1px solid #ebedf0;
  text-align: center;
}

/* 卡片容器样式已内联到具体元素中 */
</style>
