<template>
  <div class="question-progress">
    <div class="progress-info">
      <span class="current-question">第 <span>{{ current }}</span> 题</span>
      <span class="total-questions">共 <span>{{ total }}</span> 题</span>
    </div>
    <div class="progress-bar">
      <div 
        class="progress-fill" 
        :style="{ width: percentage + '%' }"
      ></div>
    </div>
    <div v-if="showPercentage" class="progress-text">
      {{ percentage }}%
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  current: {
    type: Number,
    required: true
  },
  total: {
    type: Number,
    required: true
  },
  showPercentage: {
    type: <PERSON><PERSON>an,
    default: false
  }
})

const percentage = computed(() => {
  if (props.total === 0) return 0
  return Math.round((props.current / props.total) * 100)
})
</script>

<style scoped>
.question-progress {
  width: 100%;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #323233;
}

.current-question {
  font-weight: 500;
}

.total-questions {
  color: #969799;
}

.progress-bar {
  height: 6px;
  background: #ebedf0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #1989fa;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 12px;
  color: #969799;
  margin-top: 4px;
}
</style>
