import {createRouter, createWebHashHistory, createWebHistory} from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = createRouter({
  history: createWebHistory(), // 使用Hash模式，符合钉钉H5规范
  // history: createWebHashHistory(), // 使用Hash模式，符合钉钉H5规范
  routes: [
    { path: '/', redirect: '/home' },
    { path: '/login', component: () => import('../views/login/index.vue') },
    { path: '/401', component: () => import('../views/error/401.vue') },
    { path: '/home', component: () => import('../views/home/<USER>') },
    { path: '/practice', component: () => import('../views/practice/index.vue') },
    { path: '/practice/:subjectId/detail', component: () => import('../views/practice/detail.vue') },
    { path: '/practice/:subjectId/session', component: () => import('../views/practice/session.vue') },
    { path: '/exam', component: () => import('../views/exam/index.vue') },
    { path: '/exam/:paperId/detail', component: () => import('../views/exam/detail.vue') },
    { path: '/exam/:paperId/session', component: () => import('../views/exam/session.vue') },
    { path: '/exam/:paperId/result', component: () => import('../views/exam/result.vue') },
    { path: '/profile', component: () => import('../views/profile/index.vue') },
    { path: '/history', component: () => import('../views/history/index.vue') },
    { path: '/wrong/select', component: () => import('../views/wrong/select.vue') },
    { path: '/wrong/:subjectId', component: () => import('../views/wrong/index.vue') },
    { path: '/report', component: () => import('../views/report/index.vue') },
  ]
})

router.beforeEach((to, _from, next) => {
  const userStore = useUserStore()
  if (!userStore.isLogin && to.path !== '/login' && to.path !== '/401') {
    next('/login')
    return
  }
  next()
})

export default router
