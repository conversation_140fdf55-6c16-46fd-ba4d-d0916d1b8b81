<template>
    <div id="app">
        <router-view style="z-index: 1;"></router-view>

        <van-tabbar v-if="showTabBar" class="custom-tabbar" route>
            <van-tabbar-item replace to="/home" icon="home-o">首页</van-tabbar-item>
            <van-tabbar-item replace to="/practice" icon="notes-o">练习</van-tabbar-item>
            <van-tabbar-item replace to="/exam" icon="description-o">考试</van-tabbar-item>
            <van-tabbar-item replace to="/profile" icon="user-o">我的</van-tabbar-item>
        </van-tabbar>

        <!-- 提示信息 -->
        <van-toast v-model:show="toastShow" type="text" position="middle" :duration="2500">{{
                toastMessage
            }}
        </van-toast>
    </div>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {showFailToast, showSuccessToast, showToast} from 'vant'
import {http} from '@/utils/http'

const route = useRoute()
const router = useRouter()

const warningText = ref('')
const toastShow = ref(false)
const toastMessage = ref('')

// 控制底部标签栏的显示
const showTabBar = computed(() => {
    const TabBarRoutes = ['/home', '/practice', '/exam', '/profile', "/http://127.0.0.1:3000/"]
    return TabBarRoutes.includes(route.path)
})


// 在组件挂载时加载保存的位置
onMounted(() => {
})

</script>

<style>
#app {
    font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
    background-color: #f9fafb;
}

/* 新增的 tabbar 样式 */
.custom-tabbar {
    /* box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.1); */
    padding: 4px 0;
    height: 65px !important;
    position: relative;
    z-index: 999;
    border-top: thin solid #e5e7eb;
}

.van-tabbar-item__icon img {
    display: block;
    height: 28px !important;
}

.tabbar-icon-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 28px;
}

.van-tabbar-item__icon img {
    display: block;
    height: 28px;
}

/* 其他已有样式保持不变 */
.tab-icon {
    width: 28px;
    height: 28px;
    margin-bottom: 4px;

}

.tabbar-icon-wrapper .van-icon {
    font-size: 24px;
    margin-bottom: 4px;
    color: #9295A0;
}

.tabbar-icon-wrapper span {
    font-size: 12px;
    color: #9295A0;
}

.tabbar-icon-wrapper .van-icon.active {
    color: #4285F4;
}

.tabbar-icon-wrapper span.active {
    color: #4285F4;
}

/* 覆盖 vant 默认样式 */
:deep(.van-tabbar-item) {
    height: 85px;
}

:deep(.van-tabbar-item__icon) {
    margin-bottom: 0;
}


.van-tabbar-item--active {
    background-color: transparent !important;
}




</style>
