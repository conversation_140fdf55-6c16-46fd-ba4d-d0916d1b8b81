import {defineStore} from 'pinia'
import {ref} from 'vue'
import {http} from '@/utils/http'
import {useRouter} from 'vue-router'
import {showToast} from 'vant'

import * as dd from 'dingtalk-jsapi'

export const useUserStore = defineStore('user', () => {
    const accessToken = ref(null)
    const refreshToken = ref(null)
    const isLogin = ref(false)
    const userInfo = ref(null)
    const router = useRouter()

    async function login() {
        return new Promise((resolve, reject) => {
            console.log('开始获取授权码', import.meta.env.VITE_DING_CORP_ID)
            dd.getAuthCode({
                corpId: import.meta.env.VITE_DING_CORP_ID
            })
                .then((res) => {
                    console.log(res)
                    let authCode = res.code
                    http.post('/api/dingapp/user/login', {
                        authCode: authCode
                    }).then((res) => {
                        console.log('登录成功', res)
                        handleLoginSuccess(res)
                        resolve()
                    }).catch((err) => {
                        console.log(err)
                        console.log('登录失败')
                        reject(err)
                    })
                })
                .catch((err) => {
                    console.log('获取授权码失败', err)
                })
        })
    }

    async function loginByUid(uid) {
        const res = await http.get('/api/dingapp/user/loginByUid', {
            params: {
                uid: uid
            }
        });

        if (res) {
            console.log('登录成功', res)
            handleLoginSuccess(res)
        }
    }


    function handleLoginSuccess(response, redirect) {
        accessToken.value = response.access_token
        refreshToken.value = response.refresh_token
        isLogin.value = true

        // 存储用户信息
        userInfo.value = response

        // 将用户信息保存到localStorage，以便刷新页面后恢复
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value))

        showToast({
            type: 'success',
            message: '登录成功',
            duration: 1000
        })

        startRefreshTokenTask()

        // 立即执行路由跳转，不使用setTimeout
        if (redirect) {
            router.replace(redirect)
        } else {
            router.replace('/home')
        }
    }

    function startRefreshTokenTask() {
        setInterval(async () => {
            if (!isLogin.value) return

            try {
                // const res = await http.post('/api/blade-auth/oauth/token', {
                //   grant_type: 'refresh_token',
                //   refresh_token: refreshToken.value,
                //   scope: 'all',
                //   tenantId: '000000'
                // }, {
                //   headers: {
                //     'Tenant-Id': '000000'
                //   }
                // })

                const formData = new FormData();
                formData.append('grant_type', 'refresh_token'); // 确保参数名完全匹配
                formData.append('refresh_token', refreshToken.value);
                formData.append('scope', 'all');
                formData.append('tenantId', '000000');

                const res = await http({
                    method: 'POST',
                    url: '/api/blade-auth/oauth/token',
                    data: formData, // 或 params 如果使用 URLSearchParams
                    headers: {
                        'Tenant-Id': '000000',
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                });

                accessToken.value = res.access_token
                refreshToken.value = res.refresh_token
            } catch (error) {
                console.error('刷新token失败:', error)
            }
        }, 5400 * 1000) //1.5h
    }

    // 初始化时从localStorage恢复用户信息
    function initUserInfo() {
        const storedUserInfo = localStorage.getItem('userInfo')
        if (storedUserInfo) {
            try {
                userInfo.value = JSON.parse(storedUserInfo)
            } catch (e) {
                console.error('解析用户信息失败:', e)
            }
        }
    }

    // 清除用户信息
    function clearUserInfo() {
        userInfo.value = null
        accessToken.value = null
        refreshToken.value = null
        isLogin.value = false
        localStorage.removeItem('userInfo')
    }

    // 初始化用户信息
    initUserInfo()

    return {
        accessToken,
        refreshToken,
        isLogin,
        userInfo,
        loginByUid,
        handleLoginSuccess,
        clearUserInfo,
        login
    }
})
