<!DOCTYPE html>
<html lang="zh-CN">

  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
      <title>揭阳刷题系统</title>
  </head>

  <body style="background-color: #f9fafb;">
    <div id="app"></div>

    <!-- Promise.allSettled polyfill for Chrome 69 -->
    <script>
      if (!Promise.allSettled) {
        Promise.allSettled = function(promises) {
          return Promise.all(promises.map(function(promise) {
            return Promise.resolve(promise).then(function(value) {
              return { status: 'fulfilled', value: value };
            }).catch(function(reason) {
              return { status: 'rejected', reason: reason };
            });
          }));
        };
      }
    </script>

    <script type="module" src="/src/main.js"></script>
  </body>

</html>
