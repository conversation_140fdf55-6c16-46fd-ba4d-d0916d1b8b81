---
type: "manual"
---

# dingtalk-h5 H5 小程序开发规则

本文档适用于仓库内 dingtalk-h5 子项目，面向在钉钉内嵌 WebView 运行的 H5 应用。目标是统一工程约定、降低维护成本，并保障在钉钉容器内的稳定体验。

## 1. 总则与适用范围
- 运行容器：钉钉内嵌 WebView。
- 不做 JSAPI 权限签名：无需签名流程，必要交互走通用 Web 能力或后端服务。
- 仅针对 H5（非小程序 DSL）。
- 环境：dev、prod 两套。

## 2. 技术栈基线
- Vue 3 + Vite 5 + Vue Router 4 + Pinia + Vant 4 + Axios + SCSS。
- 构建工具：Vite，调试端口 3000，开发代理已配置 /api、/minio（按需调整）。
- 代码组织：使用 @ 指向 src 目录。

## 3. 目录结构与命名
- src/views：页面级视图，按业务域分目录；页面文件命名 index.vue（子页 detail.vue 等）。
- src/components：通用组件（无业务耦合）。
- src/stores：Pinia Store，文件名使用业务名（user.js 等）。
- src/utils：工具模块（http.js 等）。
- 命名约定：
  - 目录/文件：kebab-case。
  - 组件名：PascalCase（单文件组件内）。
  - 变量/函数：camelCase。

## 4. 路由规范
- 路由模式：Hash 模式（createWebHashHistory）。
- 统一 401 页面：新增 /401 路由，承载“未授权/登录失效”的统一提示与操作（去登录/刷新重试）。
- 守卫规则：
  - 若未登录（!isLogin）且目标路由非 /login、/401，则跳转 /401。
  - 登录成功后，按 redirect 参数或默认 /task 导航。
- 路由组织：
  - 顶层 routes 保持扁平，路径语义化（/task、/task/detail/:id、/investigate/:taskId、/report、/report/detail/:id、/mine 等）。

## 5. 鉴权与用户态管理
- 登录方式：UID 登录（GET /api/dingapp/user/loginByUid?uid=xxx）。
- Store 字段：accessToken、refreshToken、isLogin、userInfo。
- 登录成功：
  - 写入 Token 与 userInfo，并持久化至 localStorage（key: userInfo）。
  - 启动 Token 刷新任务（建议现有 1.5h 定时维持）。
  - 跳转重定向：redirect 优先，否则 /task。
- 退出/清理：统一方法清空 Token、isLogin、userInfo，并清理 localStorage。

## 6. 网络层规范（Axios）
- Base URL：使用环境变量 VITE_API_URL；只允许通过 import.meta.env 读取。
- 请求拦截：
  - 携带 Blade-Auth: bearer ${accessToken}（登录后）。
  - Authorization（Basic）如需使用，请改为环境变量注入，避免硬编码。
  - GET：使用 URLSearchParams 串联到 URL；避免重复 ?。
  - POST：默认 JSON.stringify；FormData 与 text/plain 保持原样。
- 响应处理：
  - 通用响应结构：{ code, data, msg }。
  - code === 200：返回 data（或保持向后兼容按需返回原对象中的关键字段）。
  - code !== 200：showFailToast(msg || '请求失败') 并驳回 Promise。
  - 401：统一处理为未授权 -> 跳转 /401（并重置 isLogin=false），避免静默失败。
  - 文件下载：当 responseType=blob 且 content-type 为 application/json 时，先解析 JSON 并按 code 处理。
- 超时策略：默认 60s；重试统一由业务层按需实现，不在拦截器内自动重试。

## 7. 环境与构建
- 环境文件：
  - .env.development：
    - VITE_API_URL=（开发网关地址）
    - VITE_DEBUG=true（可选，用于 build:debug 的 SourceMap 逻辑）
    - VITE_BASIC_AUTH=（可选，若网关需要 Basic）
  - .env.production：
    - VITE_API_URL=（生产网关地址）
    - VITE_DEBUG=false
    - VITE_BASIC_AUTH=（可选）
- 构建命令：
  - 开发：npm run dev
  - 生产：npm run build（需要产物 hash）；调试包：npm run build:debug（保留非压缩与 SourceMap 以定位问题）。
- 部署要求：
  - Hash 模式无需后端回退；可直接部署为静态站点（Nginx/OSS/CDN）。
  - base 一般为 '/'；如需发布到子路径，需同步调整 base 与资源路径。

## 8. UI 与样式
- 组件库：Vant 4，按需引入或整体引入（二者择一，保持一致）。
- 主题：色彩/圆角/间距等变量统一由 SCSS 变量集中管理（如 src/assets/styles/_vars.scss，可在全局引入）。
- 组件封装：对复用度高的交互（如确认弹窗、空态占位）进行二次封装，放置 src/components。

## 9. 适配与视口
- 视口基准：375。
- 单位转换：postcss-px-to-viewport（在 Vite 的 css.postcss 中启用）。
  - viewportWidth: 375
  - unitPrecision: 6
  - viewportUnit: vw
  - minPixelValue: 1
  - selectorBlackList: ['.ignore', '.hairlines']（这些选择器内保留 px）
  - exclude: /node_modules/
- 图片与图标：
  - 2x/3x 资源按需提供；优先使用矢量或 iconfont/SVG 精灵。
  - 大图建议懒加载或使用渐进式加载方案。

## 10. 静态资源
- public/：不经打包处理的静态资源（favicon 等）。
- src/assets/：需要由构建管线处理的资源（样式、图片、SVG）。
- 命名：小写连字符，语义化；体积较大的资源需在 MR/评审阶段提示。

## 11. 日志与调试
- 日志：开发阶段可保留 console.log；生产构建可通过 VITE_DEBUG 控制是否保留调试信息。
- SourceMap：仅在 debug 模式生成；生产问题定位时使用。
- 统计/可观测（可选）：如需接入错误上报/性能埋点，统一在 utils 层封装。

## 12. 性能优化
- 路由懒加载：页面级视图保持动态 import（当前实践已符合）。
- 代码分割：保持默认分割策略，避免巨型 chunk；必要时以路由/模块为边界拆分。
- 资源体积：图片压缩、移除未使用依赖；谨慎引入大型第三方库。

## 13. 安全规范
- 避免在前端硬编码凭据（Basic 等），尽量改为环境变量或后端下发。
- 对外部输入（URL、后端数据）进行最小化信任与必要转义，避免 XSS。
- 仅通过 https 访问生产接口；必要时开启 CSP、Referrer-Policy 等安全头（由网关/前置服务配置）。

## 14. 错误与空状态
- 错误提示：统一使用 Vant Toast（成功/失败/警告）。
- 空状态：有数据为空、加载失败、权限不足等情况的统一视觉与文案风格。
- 401 页面：统一承载“未授权/登录失效”说明，提供“去登录（跳转 /login）”“刷新重试”等操作。

## 15. 发布与运维
- 打包产物：/dist 目录；确保使用 hash 文件名（生产）。
- 部署：静态资源服务器（Nginx/OSS）；若使用 CDN，确保缓存策略与回源可控。
- 回滚策略：保留最近两次构建产物，支持快速切换。

## 16. 钉钉内嵌 WebView 约定
- 不进行 JSAPI 权限签名；通用交互用 H5 能力实现。
- 导航：页面级返回请使用前端路由返回；如需关闭页签，使用钉钉容器提供的标准返回（若后续接入 JSAPI 再行定义）。
- 视觉：注意沉浸式状态栏、底部安全区（iOS）与软键盘弹出对布局的影响。
- 跳转外链：尽量在容器内打开；不可控外链需二次确认或 H5 内部承载。

## 17. 常见陷阱与最佳实践
- URL 参数：避免重复拼接 ?；优先用 URLSearchParams。
- localStorage：JSON.parse 包裹 try/catch，防止异常导致白屏。
- 路由刷新：Hash 模式刷新无后端回退依赖；避免使用 History 模式。
- Blob 下载：优先判断 content-type，避免误把错误提示当文件下载。
- 适配黑名单：对不希望转换为 vw 的选择器使用 .ignore 前缀。

## 18. 附录：环境变量清单（建议）
- VITE_API_URL：后端网关地址（dev/prod 必填）。
- VITE_DEBUG：是否开启调试构建（可选）。
- VITE_BASIC_AUTH：Basic 凭据（可选，若后端网关要求）。

---
以上规则作为团队开发基线。如需调整（例如新增 JSAPI 能力、改变适配策略），请在本文件内追加“变更记录”并在评审中达成一致。
