---
type: "manual"
description: "当用户需要再后台管理前端生成代码时，读取这份规则文件"
---

# 后台管理前端页面创建规则（frontend）

适用范围：Vite + Vue 3 + Vue Router 4 + Vuex 4 + Element Plus（Avue 可选）

本规则遵循项目偏好：
- 允许本地静态路由
- 路由 meta 统一要求配置
- Vue 3 Composition API
- 文件与组件命名：PascalCase
- API、Vuex 按 domain 拆分
- 不使用多语言（i18n）
- UI 优先使用 Element Plus 原子组件（无统一布局要求）
- 不考虑按钮级权限

---

## 1. 目录与命名
- 页面路径：`src/views/{Domain}/{Subdomain?}/{Page}.vue`
  - 示例：`src/views/Exam/Question/QuestionList.vue`
- 路由模块：`src/router/views/{Domain}.js`
  - 该模块导出本域的静态路由数组
  - 在 `src/router/views/index.js` 汇总
- API 模块：`src/api/{Domain}.js`
- Vuex 模块：`src/store/modules/{Domain}.js`（`namespaced: true`）
- 命名：
  - 目录、组件、页面文件：PascalCase（如 `QuestionList.vue`）
  - 路由 `name`：与页面组件名一致，PascalCase
  - 路由 `path`：kebab-case（如 `/exam/question/list`）

## 2. 路由与 meta 规范（静态注册）
- 基本字段：
  - `path`（kebab-case）
  - `name`（PascalCase，唯一）
  - `component`（页面组件）
  - `meta`（必填对象）：
    - `title`：字符串，页面标题
    - `isTab`：布尔，是否加入多标签页（建议显式 true）
    - `isAuth`：布尔，是否需要登录（建议显式 true）
    - `keepAlive`：布尔，是否缓存（列表页建议 true）
    - `icon`：字符串，可选
    - `menu`：可选（与菜单数据协作时使用）
    - `target`：布尔，可选（外链）
- 组织方式：
  - 在 `src/router/views/{Domain}.js` 定义数组导出
  - 在 `src/router/views/index.js` 汇总展开
  - 确保与动态路由不冲突（`name`、`path` 唯一）

示例（`src/router/views/Exam.js`）：
```js
import QuestionList from '@/views/Exam/Question/QuestionList.vue';

export default [
  {
    path: '/exam/question/list',
    name: 'QuestionList',
    component: QuestionList,
    meta: {
      title: '题目列表',
      isTab: true,
      isAuth: true,
      keepAlive: true,
      icon: 'List',
    },
  },
];
```

在 `src/router/views/index.js`：
```js
import ExamRoutes from './Exam';

export default [
  ...ExamRoutes,
];
```

> 注意：该汇总文件需被 `src/router/index.js` 所在的 `ViewsRouter` 引入/合并。

## 3. API 模块（`src/api/{Domain}.js`）
- 使用项目内 `src/axios.js` 实例
- 函数语义清晰（不强制前缀）：如 `fetchQuestionList`、`getQuestionById`、`createQuestion`、`updateQuestion`、`removeQuestion`
- 入参使用对象，便于扩展；直接返回 `axios` Promise

示例（`src/api/Exam.js`）：
```js
import request from '@/axios';

export function fetchQuestionList(params) {
  return request({ url: '/exam/question', method: 'get', params });
}

export function getQuestionById(id) {
  return request({ url: `/exam/question/${id}`, method: 'get' });
}

export function createQuestion(data) {
  return request({ url: '/exam/question', method: 'post', data });
}

export function updateQuestion(id, data) {
  return request({ url: `/exam/question/${id}`, method: 'put', data });
}

export function removeQuestion(id) {
  return request({ url: `/exam/question/${id}`, method: 'delete' });
}
```

## 4. Vuex 模块（`src/store/modules/{Domain}.js`，可选）
- 仅在需要跨页面共享时创建

示例（`src/store/modules/Exam.js`）：
```js
export default {
  namespaced: true,
  state: () => ({
    lastUsedQuery: null,
  }),
  mutations: {
    setLastUsedQuery(state, payload) {
      state.lastUsedQuery = payload;
    },
  },
  actions: {
    rememberQuery({ commit }, query) {
      commit('setLastUsedQuery', query);
    },
  },
};
```

## 5. 页面模板（Composition API）
示例：`src/views/Exam/Question/QuestionList.vue`
```vue
<template>
  <div class="page">
    <el-form :inline="true" @submit.prevent>
      <el-form-item label="关键字">
        <el-input v-model="query.keyword" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="loading" @click="onSearch">查询</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>

    <el-space style="margin: 12px 0;">
      <el-button type="primary" @click="onCreate">新增</el-button>
    </el-space>

    <el-table :data="list" v-loading="loading" border>
      <el-table-column prop="id" label="ID" width="100" />
      <el-table-column prop="title" label="标题" />
      <el-table-column label="操作" width="180">
        <template #default="{ row }">
          <el-button type="primary" link @click="onEdit(row)">编辑</el-button>
          <el-button type="danger" link @click="onRemove(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="margin-top: 12px; text-align: right;">
      <el-pagination
        background
        layout="prev, pager, next, sizes, total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        :total="pagination.total"
        @current-change="onPageChange"
        @size-change="onSizeChange"
      />
    </div>

    <el-dialog v-model="dialog.visible" :title="dialog.title" width="600px">
      <el-form :model="form" label-width="80px">
        <el-form-item label="标题">
          <el-input v-model="form.title" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialog.visible=false">取消</el-button>
        <el-button type="primary" :loading="saving" @click="onSubmit">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { fetchQuestionList, createQuestion, updateQuestion, removeQuestion } from '@/api/Exam';

const loading = ref(false);
const saving = ref(false);
const list = ref([]);
const query = reactive({ keyword: '' });
const pagination = reactive({ page: 1, size: 10, total: 0 });

const dialog = reactive({ visible: false, title: '新增', mode: 'create', currentId: null });
const form = reactive({ title: '' });

function onSearch() { pagination.page = 1; load(); }
function onReset() { query.keyword = ''; onSearch(); }
function onPageChange(p){ pagination.page = p; load(); }
function onSizeChange(s){ pagination.size = s; load(); }

async function load(){
  loading.value = true;
  try {
    const { data } = await fetchQuestionList({
      page: pagination.page,
      size: pagination.size,
      keyword: query.keyword,
    });
    list.value = data.records || data.items || [];
    pagination.total = data.total || 0;
  } catch (e) {
    ElMessage.error(e.message || '加载失败');
  } finally {
    loading.value = false;
  }
}

function onCreate(){ dialog.visible = true; dialog.title = '新增'; dialog.mode='create'; form.title=''; dialog.currentId=null; }
function onEdit(row){ dialog.visible = true; dialog.title = '编辑'; dialog.mode='edit'; form.title=row.title; dialog.currentId=row.id; }

async function onSubmit(){
  saving.value = true;
  try {
    if(dialog.mode==='create'){
      await createQuestion({ ...form });
      ElMessage.success('创建成功');
    } else {
      await updateQuestion(dialog.currentId, { ...form });
      ElMessage.success('更新成功');
    }
    dialog.visible=false; load();
  } catch (e) {
    ElMessage.error(e.message || '保存失败');
  } finally {
    saving.value = false;
  }
}

async function onRemove(row){
  try{
    await ElMessageBox.confirm('确认删除该记录吗？','提示',{type:'warning'});
    await removeQuestion(row.id);
    ElMessage.success('删除成功');
    load();
  }catch(e){ /* cancel or error */ }
}

onMounted(load);
</script>

<style scoped>
.page{ padding: 12px; }
</style>
```

## 6. 异常与加载态
- 所有 API 调用使用 `try/catch/finally`，设置 `loading/saving`
- 错误统一 `ElMessage.error` 提示
- 提交期间按钮 `:loading` 并禁用

## 7. 验证清单（手动）
- 路由：可达、`meta.isTab` 生效、`meta.isAuth` 生效
- keepAlive：切换路由返回后保留筛选/分页
- API：加载态、错误提示、成功刷新
- 分页：页码/条数变化刷新
- 对话框：新增/编辑/删除闭环

## 8. 提交策略
- 小步提交：路由、页面、API、Vuex 分步提交
- 提交信息包含域/页面与影响范围
- 合并前本地自测通过

