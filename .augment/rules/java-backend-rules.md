---
type: "manual"
---

# Java 后端开发规则（项目级）

适用范围：本规则适用于本仓库 backend/ 目录内的 Java 服务端代码。本文为项目级约束，面向所有开发与评审人员。

特别说明：
- 文件头部不需要注释（不强制文件级版权/作者头注释）。
- 本规则仅为文档说明，不引入任何构建/部署规则或测试与质量门禁。

---

## 1. 目的与适用范围
- 统一代码风格、分层结构与命名，降低协作成本。
- 明确 API 设计、参数校验与错误处理，提升接口一致性与可维护性。
- 规范持久层（MyBatis-Plus）使用方式，保障可维护性与性能。
- 强化安全与合规，尽量在编码期预防安全问题。

## 2. 通用约定（代码规范与格式）

### 2.1 语言与编码
- Java 17，UTF-8 编码。
- 文本文件统一 LF 换行。

### 2.2 包结构与分层建议
- controller：仅承载入参校验、鉴权入口、调用应用/领域服务、拼装返回；禁止写业务逻辑。
- service：业务编排与事务边界；对外暴露清晰的服务方法。
- manager（可选）：跨聚合/跨资源的组合与协调。
- repository/mapper：数据访问层，负责持久化与查询；使用 MyBatis-Plus。
- domain（entity/DO）：与数据库结构对应的实体；避免夹杂视图字段。
- dto：入参对象；vo：出参/视图对象；二者严禁混用。
- convert（或 assembler）：对象转换集中处理，避免 Controller/Service 里散落转换逻辑。
- common/infra：通用工具、常量、枚举、异常、配置；避免与业务强耦合。

### 2.3 命名规范
- 包名小写，使用有意义的分层/业务前缀；类名使用 UpperCamelCase；方法/变量 lowerCamelCase；常量 UPPER_SNAKE_CASE。
- 禁止无意义或过度缩写；命名需体现业务语义与单位（如 ms、count、amount）。
- DTO/VO/DO/Mapper/Service/Controller 等后缀统一。

### 2.4 注释规范
- 文件头部不需要注释。
- 公共类、复杂方法、接口、关键业务流程需有 Javadoc/行内注释，说明意图而非复述代码。

### 2.5 日志与异常
- 统一使用日志门面（如 slf4j），严禁 System.out/printStackTrace。
- 日志分级：INFO 记录关键业务流程、WARN 可恢复异常、ERROR 需排查故障；DEBUG 仅用于开发定位，避免在生产频繁输出。
- 日志避免敏感信息（口令、密钥、身份证、手机号等），必要时脱敏（如掩码）。
- 异常不得吞掉；向上抛出或转换为统一的业务异常，带可追踪上下文（如 traceId）。

### 2.6 Lombok 使用
- 可使用 @Getter/@Setter/@Builder；慎用 @Data（会生成 equals/hashCode/toString），需要时应显式控制 @EqualsAndHashCode(of=...)。
- DTO/VO 可以使用 @Builder 简化构建；避免在 Entity/DO 上使用 @Builder 影响可读性与一致性。

### 2.7 格式与风格（约定性）
- 缩进 4 空格；最大行宽建议 120；import 分组：java.*、javax.*、org.*、com.*，再到项目内；禁止通配符导入。
- 花括号与空格：遵循常见 Java 社区风格；保留必要空行提高可读性。
- 空对象/集合：使用 Collections.emptyList()/emptyMap() 等，避免返回 null。

### 2.8 其他
- Magic Number/字符串集中为常量；统一单位与数值范围；
- 避免上帝类与超长方法（>150 行需重构）；
- 配置读取集中在 config 层并有默认值；
- 严禁在代码中硬编码密钥、凭据、访问令牌。

---

## 3. API 规则

### 3.1 基础风格
- 路由 RESTful，资源名使用复数：/api/v1/users/{id}。
- 版本前缀：/api/v1；升级采用 /api/v2 并设定兼容窗口。
- HTTP 方法：GET 查询、POST 新建、PUT 全量更新、PATCH 局部更新、DELETE 删除；避免语义错位。

### 3.2 统一响应体
- 建议格式：
```json
{
  "success": true,
  "code": "0",
  "msg": "OK",
  "data": { ... },
  "traceId": "..."
}
```
- 错误码分层建议：
  - 系统级（SYS-）、业务级（BIZ-）、第三方（EXT-）；
  - code 仅用于机器识别，msg 面向人类可读但避免泄露内部实现。

### 3.3 分页/排序/过滤
- 入参：page（>=1）、size（>0），size 建议设置服务端上限；sort=field,asc|desc；filter 按约定 key。
- 返回：
```json
{
  "success": true,
  "code": "0",
  "msg": "OK",
  "data": {
    "list": [ ... ],
    "page": 1,
    "size": 20,
    "total": 123
  },
  "traceId": "..."
}
```
- 禁止全量查询返回巨量数据；必须分页或明确限定条数。

### 3.4 幂等性
- 对转账、下单等关键写操作提供幂等键（如客户端生成的 requestId + 业务关键字段摘要），后端去重防重放。
- PUT/DELETE 天然幂等，POST 需设计幂等策略。

### 3.5 鉴权与授权
- 接口默认鉴权，按角色/权限与数据范围控制访问；白名单端点需单独标注，并控制暴露范围。
- 结合租户与数据范围（data-scope）在服务层/DAO 层正确过滤。

### 3.6 参数校验
- 使用 @Validated/@Valid 与 Bean Validation 注解（@NotNull/@Size/@Pattern 等）；
- 支持分组校验；校验失败应返回统一响应体并附带字段错误信息。

### 3.7 OpenAPI/Knife4j
- Controller、DTO/VO 必须补充 Swagger/OpenAPI 注解（@Operation/@Schema/@Parameter），包含示例值；
- 接口分组与标签清晰；发布改动需同步更新文档说明。

### 3.8 文件上传/下载
- 上传：限制 MIME 白名单与大小，校验扩展名与 Content-Type 一致性；对外链路可配合对象存储直传策略；
- 下载：正确设置 Content-Type/Content-Disposition，支持范围请求（断点续传）按需实现。

### 3.9 时间与序列化
- 与前端约定时区与格式（推荐 ISO-8601，或时间戳毫秒）；服务内部统一处理并清晰转换。

---

## 4. 持久层规则（MyBatis-Plus）

### 4.1 分层与对象边界
- DO/Entity：数据库实体，仅承载持久化字段；
- DTO：入参；VO：出参；
- Converter：集中定义 DO/DTO/VO 之间的转换，避免散落在业务代码中。

### 4.2 命名与审计字段
- 表命名使用下划线风格；字段与 Java 命名通过驼峰/下划线自动映射；
- 建议统一审计字段：create_time、update_time、create_user、update_user、is_deleted、tenant_id 等；
- 逻辑删除：统一使用 is_deleted（0/1 或 boolean），物理删除需严格评审。

### 4.3 Mapper 与 XML 位置
- Mapper XML 应与对应的 Mapper 接口类放在同一目录（同一包路径），确保一一对应、命名一致（如 UserMapper.java 对应 UserMapper.xml）。
- 允许 XML 位于 src/main/java 下与 Mapper 同包。项目已在构建中包含对 src/main/java 下 **/*.xml 的资源打包。
- 若已有 resources/mapper 下的 XML，可按需迁移至与 Mapper 同目录，并在评审中跟踪，避免同名重复加载。

### 4.4 查询与性能
- 必须分页或限制返回条数，禁止大表无分页全量扫描；
- 避免 N+1（合理使用关联查询或批量查询）；
- 批量写入/更新使用批处理接口，控制单批大小；
- 重要语句检查执行计划与索引命中；必要时增加覆盖索引。

### 4.5 事务
- 仅在 service 层使用 @Transactional 作为事务边界；
- 明确传播与回滚策略；跨服务调用不在同一事务，需通过补偿/状态机等保证一致性。

### 4.6 多租户与数据权限
- 使用 blade-starter-tenant 与 blade-starter-data-scope 时，注意：
  - 过滤条件应在 DAO/Service 层可靠生效，避免绕过；
  - 批量/手写 SQL 需显式包含租户/数据范围条件；
  - 严禁以管理员身份绕过过滤进行常态查询。

---

## 5. 安全与合规

### 5.1 输入校验与输出编码
- 对所有外部输入进行白名单校验；字符串做长度与字符集限制；
- 输出到 HTML/日志时进行必要转义；防止 XSS/注入。

### 5.2 SQL 注入防护
- 统一使用 MyBatis 参数绑定与 Wrapper，禁止拼接 SQL；
- 对排序字段与动态列使用白名单映射。

### 5.3 敏感信息处理
- 密钥/口令/访问令牌/身份证/手机号等敏感信息：
  - 不写入日志与异常；
  - 传输与存储按需加密/脱敏；
  - 配置在安全的外部配置中心或环境变量，不提交到仓库。

### 5.4 API 加密与签名
- 需要时使用 blade-starter-api-crypto 对敏感接口进行签名/加解密；明确适用范围与性能权衡。

### 5.5 CORS 与跨域
- 统一跨域策略，限制来源域名；预检请求（OPTIONS）正确处理并限制暴露头；
- 禁止在无必要时放开通配符 *。

### 5.6 速率限制与频控（按需）
- 对登录/验证码/下单等高风险接口设置频控；在网关或服务端拦截实现。

### 5.7 审计与操作记录
- 对敏感操作（角色/权限/金额/配置）记录审计日志：操作者、时间、对象、变更前后关键字段、结果；
- 审计日志不得包含敏感明文。

### 5.8 文件与外链安全
- 文件上传严格校验类型与大小，进行内容型式检查；
- 访问外部 URL 避免 SSRF（白名单主机、限制协议与端口）；
- 避免执行系统命令与反序列化风险；
- 临时文件与缓存及时清理。

---

## 6. 违例等级与处置
- 强制：违反将被评审阻断，需修复后合入；
- 推荐：评审提出优化建议，视重要性安排重构；
- 可选：不阻断评审，团队可按实际情况采纳。

---

## 7. 附录（示例片段）

### 7.1 统一响应体（示例）
```json
{
  "success": false,
  "code": "BIZ-ORDER-001",
  "msg": "下单失败：库存不足",
  "data": null,
  "traceId": "1-abcde-..."
}
```

### 7.2 分页请求与返回（示例）
- 请求：GET /api/v1/orders?page=1&size=20&sort=createTime,desc
- 返回字段：list/page/size/total

### 7.3 Bean Validation（示例）
```java
public record CreateUserDTO(
  @NotBlank String username,
  @Pattern(regexp = "^[0-9]{11}$") String mobile,
  @Size(min = 8, max = 32) String password
) {}
```

### 7.4 Swagger/OpenAPI 注解（示例）
```java
@Tag(name = "User")
@RestController
@RequestMapping("/api/v1/users")
public class UserController {
  @Operation(summary = "Create user")
  @PostMapping
  public R<UserVO> create(@Validated @RequestBody CreateUserDTO dto) { ... }
}
```

### 7.5 Mapper XML 放置路径
- XML 与对应的 Mapper 接口类放在同一目录（同包）存放，例如 com/example/user/mapper/UserMapper.java 与 com/example/user/mapper/UserMapper.xml。
- 允许 XML 位于 src/main/java 下与 Mapper 同包（项目打包会包含该路径下的 **/*.xml）。
- 若存量 XML 位于 resources/mapper，可逐步迁移到与 Mapper 同目录，并注意避免同名重复加载。

